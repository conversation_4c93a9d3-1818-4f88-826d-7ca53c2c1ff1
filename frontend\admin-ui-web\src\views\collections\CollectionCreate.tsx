'use client';

import { useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import { CardActions, CircularProgress, FormHelperText, Grid, InputLabel, TextField, Autocomplete } from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { useFormik } from 'formik';

import MainCard from 'components/MainCard';
import { useLazyGetCollectionsQuery } from 'redux/app/collections/collectionApiSlice';
import { useCreateAssetMutation, useGetAssetsCountQuery, useGetAssetsQuery } from 'redux/app/products/productApiSlice';
import withPermission from 'hoc/withPermission';
import { PermissionKeys } from 'enums/permission-keys.enum';
import { CollectionSchema } from '../../../validations/collection';
import { useCreateCollectionsMutation, useUpdateCollectionsMutation } from 'redux/app/collections/collectionApiSlice';
import { CollectionDto } from 'types/collection-dto';
import { initialCollectionValue } from 'constants/collection';
import { CollectionAsset } from './CollectionAsset';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { Asset } from 'types/product';
import { AutoCompleteOption } from 'types/common';
import { useGetTaxCategoriesQuery } from 'redux/app/tax/taxCategoryApiSlice';

function CreateCollection({
  isEdit = false,
  collectionId,
  initialValues: propInitialValues = {},
  featuredAsset,
  refetch
}: {
  isEdit?: boolean;
  collectionId?: string;
  initialValues?: Partial<CollectionDto>;
  featuredAsset?: { id: string; previewUrl: string };
  refetch: () => void;
}) {
  const router = useRouter();
  const [collectionInput, setCollectionInput] = useState('');
  const [, setTaxCategoryInput] = useState('');
  const [selectedAssets, setSelectedAssets] = useState<{ id: string; preview: string }[]>([]);
  const [openGallery, setOpenGallery] = useState(false);
  const [getCollection, { data: collections, isLoading: isParentLoading }] = useLazyGetCollectionsQuery({});
  const [createCollection, { isLoading: isCreating }] = useCreateCollectionsMutation();
  const [updateCollection, { isLoading: isUpdating }] = useUpdateCollectionsMutation();
  const [page, setPage] = useState(0);
  const [assets, setAssets] = useState<Asset[]>([]);
  const limit = 10;
  const { data: totalAssets } = useGetAssetsCountQuery();
  const { data: paginatedData, refetch: refetchAssets } = useGetAssetsQuery({ order: ['createdOn DESC'], limit, skip: page * limit });

  const [addAsset, { isLoading: isUploading }] = useCreateAssetMutation();
  const { data: taxCategories, isLoading: isTaxCategoriesLoading } = useGetTaxCategoriesQuery({}); // New query for tax categories

  const initialValues = useMemo(
    () => ({
      ...initialCollectionValue,
      ...propInitialValues
    }),
    [propInitialValues]
  );

  useEffect(() => {
    if (featuredAsset) {
      setSelectedAssets([{ id: featuredAsset.id, preview: featuredAsset.previewUrl }]);
    }
  }, [featuredAsset]);

  useEffect(() => {
    const fetchInitialParent = async () => {
      if (isEdit && initialValues?.parentId) {
        const res = await getCollection({
          where: { id: initialValues.parentId }
        }).unwrap();
        if (res?.length > 0) {
          const parent = res[0];
          const exists = collectionOptions.find((opt) => opt.value === parent.id);
          if (!exists) {
            collectionOptions.unshift({ label: parent.name, value: parent.id });
          }
        }
      }
    };

    fetchInitialParent();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleFeatureAssetChange = (selectedAssets: any) => {
    formik.setFieldValue('featuredAssetId', selectedAssets?.id || null);
  };

  const fetchCollections = async () => {
    await getCollection({
      where: { name: { ilike: `%${collectionInput}%` } },
      include: [
        {
          relation: 'childrens'
        }
      ]
    }).unwrap();
  };

  const collectionOptions: AutoCompleteOption[] = useMemo(() => {
    return collections?.map((item) => ({ label: item.name, value: item.id })) ?? [];
  }, [collections]);

  const taxCategoryOptions: AutoCompleteOption[] = useMemo(() => {
    return (
      taxCategories?.map((item) => ({
        label: item.name,
        value: item.id || ''
      })) ?? []
    );
  }, [taxCategories]);

  const formik = useFormik<CollectionDto>({
    initialValues,
    validationSchema: CollectionSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      const { parentId, ...rest } = values;
      const cleanedValues: Partial<CollectionDto> = {
        ...rest,
        ...(parentId ? { parentId } : {}),
        ...(values.taxCategoryId && { taxCategoryId: values.taxCategoryId }),
        featuredAssetId: values.featuredAssetId
      };

      if (isEdit && collectionId) {
        await updateCollection({
          id: collectionId,
          data: cleanedValues
        }).unwrap();
        openSnackbar({
          open: true,
          message: 'category Updated successfully',
          variant: 'alert',
          alert: { color: 'success' }
        } as SnackbarProps);
      } else {
        await createCollection(cleanedValues).unwrap();
        openSnackbar({
          open: true,
          message: 'category Created successfully',
          variant: 'alert',
          alert: { color: 'success' }
        } as SnackbarProps);
      }
      refetch();
      router.push('/collections');
    }
  });

  const handleFileChange = async (file: File) => {
    if (!file) return;

    const formData = new FormData();
    formData.append('file', file);
    const asset = (await addAsset(formData).unwrap()) as unknown as Asset;
    refetchAssets();
    formik.setFieldValue('featuredAssetId', asset.id);
    setSelectedAssets([{ id: asset.id, preview: asset.previewUrl }]);
  };

  useEffect(() => {
    const debounce = setTimeout(fetchCollections, 300);
    return () => clearTimeout(debounce);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [collectionInput]);

  useEffect(() => {
    if (selectedAssets.length === 0) {
      formik.setFieldValue('featuredAssetId', '');
    } else {
      formik.setFieldValue('featuredAssetId', selectedAssets[0].id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedAssets]);

  useEffect(() => {
    if (paginatedData) {
      setAssets((prev) => [...prev, ...paginatedData]);
    }
  }, [paginatedData]);

  const hasMore = useMemo(() => {
    if (totalAssets?.count) {
      return totalAssets?.count > assets?.length;
    }
    return false;
  }, [assets, totalAssets]);

  const loadMoreAssets = () => {
    if (assets.length < (totalAssets?.count ?? 0)) {
      setPage((prev) => prev + 1);
    }
  };

  return (
    <MainCard title={isEdit ? 'Edit Category' : 'Create Category'}>
      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <InputLabel sx={{ mb: 1 }}>Category Name*</InputLabel>
                <TextField
                  fullWidth
                  name="name"
                  placeholder="Enter category name"
                  value={formik.values.name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.name && Boolean(formik.errors.name)}
                  helperText={formik.touched.name && formik.errors.name}
                />
              </Grid>

              <Grid item xs={12}>
                <InputLabel sx={{ mb: 1 }}>Parent Category</InputLabel>
                <Autocomplete
                  freeSolo
                  options={collectionOptions}
                  value={
                    collectionOptions.find((opt) => opt.value === formik.values.parentId) ??
                    (formik.values.parentId ? { label: formik.values.parentId, value: formik.values.parentId } : null)
                  }
                  onInputChange={(_, newInputValue) => setCollectionInput(newInputValue)}
                  onChange={(_, newValue) => {
                    const parentId = typeof newValue === 'string' ? newValue : (newValue?.value ?? '');
                    formik.setFieldValue('parentId', parentId);
                  }}
                  loading={isParentLoading}
                  getOptionLabel={(option) => (typeof option === 'string' ? option : option.label)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Search parent category"
                      onBlur={formik.handleBlur}
                      error={formik.touched.parentId && Boolean(formik.errors.parentId)}
                      name="parentId"
                      helperText={formik.touched.parentId && formik.errors.parentId}
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {isParentLoading && <CircularProgress color="inherit" size={20} />}
                            {params.InputProps.endAdornment}
                          </>
                        )
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <InputLabel sx={{ mb: 1 }}>Tax Category</InputLabel>
                <Autocomplete
                  freeSolo
                  options={taxCategoryOptions}
                  value={
                    taxCategoryOptions.find((opt) => opt.value === formik.values.taxCategoryId) ??
                    (formik.values.taxCategoryId ? { label: formik.values.taxCategoryId, value: formik.values.taxCategoryId } : null)
                  }
                  onInputChange={(_, newInputValue) => setTaxCategoryInput(newInputValue)}
                  onChange={(_, newValue) => {
                    const taxCategoryId = typeof newValue === 'string' ? newValue : (newValue?.value ?? '');
                    formik.setFieldValue('taxCategoryId', taxCategoryId);
                  }}
                  loading={isTaxCategoriesLoading}
                  getOptionLabel={(option) => (typeof option === 'string' ? option : option.label)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Search tax category"
                      onBlur={formik.handleBlur}
                      error={formik.touched.taxCategoryId && Boolean(formik.errors.taxCategoryId)}
                      name="taxCategoryId"
                      helperText={formik.touched.taxCategoryId && formik.errors.taxCategoryId}
                      InputProps={{
                        ...params.InputProps,
                        endAdornment: (
                          <>
                            {isTaxCategoriesLoading && <CircularProgress color="inherit" size={20} />}
                            {params.InputProps.endAdornment}
                          </>
                        )
                      }}
                    />
                  )}
                />
              </Grid>

              <Grid item xs={12}>
                <CollectionAsset
                  assets={assets ?? []}
                  openGallery={openGallery}
                  toggleGallery={() => {
                    setOpenGallery(!openGallery);
                  }}
                  handleFileChange={handleFileChange}
                  isAssetUploading={isUploading}
                  setSelectedAssets={setSelectedAssets}
                  selectedAssets={selectedAssets}
                  handleFeatureAssetChange={handleFeatureAssetChange}
                  featuredAssetId={formik.values.featuredAssetId ?? ''}
                  allowMultiple={false}
                  featuredAsset={featuredAsset!}
                  isEdit={isEdit}
                  hasMore={hasMore}
                  loadMoreAssets={loadMoreAssets}
                  assetsCount={totalAssets?.count ?? 0}
                />
                {formik.touched.featuredAssetId && formik.errors.featuredAssetId && (
                  <FormHelperText error>{formik.errors.featuredAssetId}</FormHelperText>
                )}
              </Grid>
            </Grid>
          </Grid>
        </Grid>

        <CardActions sx={{ justifyContent: 'flex-end', mt: 2 }}>
          <LoadingButton
            type="submit"
            variant="contained"
            loading={isCreating || isUpdating}
            disabled={!formik.isValid || isCreating || isUpdating}
          >
            {isEdit ? 'Update' : 'Create'}
          </LoadingButton>
        </CardActions>
      </form>
    </MainCard>
  );
}

export default withPermission(PermissionKeys.CreateCollection)(CreateCollection);
