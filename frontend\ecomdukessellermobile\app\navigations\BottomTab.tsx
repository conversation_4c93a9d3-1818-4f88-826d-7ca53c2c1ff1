import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {SCREEN_NAME} from '../constants';
import DashboardScreen from '../screens/main/Dashboard/DashboardScreen';
import ProductsScreen from '../screens/main/Products/Productsscreen';
import OrderScreen from '../screens/main/Order/Orderscreen';
import NotificationScreen from '../screens/main/Notification/Notificationscreen';
import {Text} from 'react-native-paper';
import customColors from '../theme/customColors';
import {Images} from '../assets/images';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const Tab = createBottomTabNavigator();
const CustomTabBar = ({state, descriptors, navigation}: any) => {
  const insets = useSafeAreaInsets();

  const icons = {
    [SCREEN_NAME.DASHBOARD]: Images.bottomBar_home,
    [SCREEN_NAME.productsTab]: Images.bottomBarProducts,
    [SCREEN_NAME.ordersTab]: Images.bottomBar_orders,
    [SCREEN_NAME.notificationTab]: Images.bottomBar_notification,
  };

  return (
    <View style={[styles.container, {paddingBottom: insets.bottom}]}>
      {state.routes.map((route: any, index: any) => {
        const {options} = descriptors[route.key];
        const isFocused = state.index === index;
        const icon = icons[route.name];

        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
          }
        };

        return (
          <TouchableOpacity
            key={route.key}
            accessibilityRole="button"
            onPress={onPress}
            style={styles.tab}>
            <View
              style={{
                backgroundColor: '#EFF1F2',
                padding: 10,
                paddingHorizontal: 15,
                borderRadius: 10,
              }}>
              <Image
                source={icon}
                style={[styles.icon, !isFocused && {tintColor: '#aaa'}]}
              />
            </View>

            <Text
              variant="titleSmall"
              style={{
                color: isFocused ? customColors.appBlue : '#515151',
                fontSize: 12,
              }}>
              {options.title}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};
const BottomTabNavigation = () => {
  return (
    <Tab.Navigator
      tabBar={props => <CustomTabBar {...props} />}
      screenOptions={{
        headerShown: false,
      }}>
      <Tab.Screen
        options={{title: 'Home'}}
        name={SCREEN_NAME.DASHBOARD}
        component={DashboardScreen}
      />
      <Tab.Screen
        name={SCREEN_NAME.productsTab}
        options={{title: 'Products'}}
        component={ProductsScreen}
      />
      <Tab.Screen
        options={{title: 'Orders'}}
        name={SCREEN_NAME.ordersTab}
        component={OrderScreen}
      />
      <Tab.Screen
        options={{title: 'Notification'}}
        name={SCREEN_NAME.notificationTab}
        component={NotificationScreen}
      />
    </Tab.Navigator>
  );
};
const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 20,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
  },
  icon: {
    height: 24,
    width: 24,
    marginBottom: 4,
  },
});
export default BottomTabNavigation;
