'use client';
import React, {useEffect, useState} from 'react';
import {
  Typo<PERSON>,
  CardContent,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Box,
  StepIconProps,
  StepConnector,
  Stepper,
  Step,
  StepLabel,
  stepConnectorClasses,
  CardMedia,
  FormControl,
  MenuItem,
  Pagination,
} from '@mui/material';
import styled from '@mui/system/styled';
import FlightIcon from '@mui/icons-material/Flight';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';

import {
  useGetOrderQuery,
  useLazyDownloadInvoicePdfQuery,
  useGetOrderCountQuery,
} from 'redux/order/orderApiSlice';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import {Accordion, AccordionSummary, AccordionDetails} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {Tooltip} from '@mui/material';
import {OrderStatus, orderStatusOptions} from 'enums/orderStatus';
import ReviewModal from 'views/review/ReviewModal';
import {CircularProgress} from '@mui/material';
import {useRouter} from 'next/navigation';
import {IFilter} from 'types/filter';
import {Select} from '@mui/material';
import CardGiftcardIcon from '@mui/icons-material/CardGiftcard';
import {SearchNormal, Star1} from 'iconsax-react';
const ColorlibConnector = styled(StepConnector)(({theme}) => ({
  [`&.${stepConnectorClasses.alternativeLabel}`]: {
    top: 22,
  },
  [`&.${stepConnectorClasses.active}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundImage: 'linear-gradient(to right, #4A90E2, #6A11CB)',
    },
  },
  [`&.${stepConnectorClasses.completed}`]: {
    [`& .${stepConnectorClasses.line}`]: {
      backgroundImage: 'linear-gradient(to right, #4A90E2, #6A11CB)',
    },
  },
  [`& .${stepConnectorClasses.line}`]: {
    height: 3,
    border: 0,
    backgroundColor: '#eaeaf0',
    borderRadius: 1,
    ...theme.applyStyles('dark', {
      backgroundColor: theme.palette.grey[800],
    }),
  },
}));

const ColorlibStepIconRoot = styled('div')<{
  ownerState: {completed?: boolean; active?: boolean};
}>(({theme, ownerState}) => ({
  backgroundColor: '#ccc',
  zIndex: 1,
  color: '#fff',
  width: 50,
  height: 50,
  display: 'flex',
  borderRadius: '50%',
  justifyContent: 'center',
  alignItems: 'center',
  ...theme.applyStyles('dark', {
    backgroundColor: theme.palette.grey[700],
  }),
  ...(ownerState.completed && {
    backgroundColor: '#151B54',
  }),
  ...(ownerState.active && {
    backgroundColor: '#151B54',
    boxShadow: '0 4px 10px 0 rgba(0,0,0,.25)',
  }),
}));

function ColorlibStepIcon(props: StepIconProps) {
  const {active, completed, className} = props;

  const icons: {[index: string]: React.ReactElement} = {
    1: <ThumbUpIcon />,
    2: <FlightIcon />,
    3: <LocalShippingIcon />,
    4: <CardGiftcardIcon />,
  };
  return (
    <ColorlibStepIconRoot
      ownerState={{completed, active}}
      className={className}
    >
      {icons[String(props.icon)]}
    </ColorlibStepIconRoot>
  );
}

const fullStepConfig = [
  {
    label: 'Order Placed',
    status: [OrderStatus.Pending, OrderStatus.Paid],
    message: 'Your order has been placed and is being processed.',
  },
  {
    label: 'Shipped',
    status: [OrderStatus.Picked],
    message: 'Your order has been picked and packed.',
  },
  {
    label: 'Out for Delivery',
    status: [OrderStatus.InTransit],
    message: 'Your order is on the way.',
  },
  {
    label: 'Delivered',
    status: [OrderStatus.Delivered],
    message: 'Your order has been delivered.',
  },
];
function getActiveStepIndex(status: OrderStatus) {
  return fullStepConfig.findIndex(step => step.status.includes(status));
}

const DEFAULT_COLOR = '#9E9E9E';

const statusColors: Record<OrderStatus, string> = {
  [OrderStatus.Pending]: '#FFA500',
  [OrderStatus.Paid]: '#4A90E2',
  [OrderStatus.PaymentFailed]: DEFAULT_COLOR,
  [OrderStatus.Failed]: DEFAULT_COLOR,
  [OrderStatus.Picked]: '#00CED1',
  [OrderStatus.InTransit]: '#FF8C00',
  [OrderStatus.Delivered]: '#32CD32',
  [OrderStatus.DeliveryFailed]: DEFAULT_COLOR,
  [OrderStatus.Cancelled]: DEFAULT_COLOR,
  [OrderStatus.Returned]: DEFAULT_COLOR,
  [OrderStatus.Refunded]: DEFAULT_COLOR,
  [OrderStatus.RefundInitiated]: DEFAULT_COLOR,
  [OrderStatus.RefundFailed]: DEFAULT_COLOR,
};

const OrderCard = () => {
  const {data: user} = useGetUserQuery();
  const customerId = user?.profileId;
  const router = useRouter();

  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [reviewModalState, setReviewModalState] = useState<{
    open: boolean;
    reviewId?: string;
    productVariantId?: string;
    orderLineItemId?: string;
    customerId?: string;
  }>({open: false});

  const [triggerDownloadPdf] = useLazyDownloadInvoicePdfQuery();

  const [_, setEditReviewId] = useState<string | undefined>(undefined);
  const PAGE_SIZE_OPTIONS = [10, 25, 50];
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);

  const [selectedStatus, setSelectedStatus] = useState('');
  const handlePageChange = (
    event: React.ChangeEvent<unknown>,
    newPage: number,
  ) => setPage(newPage);
  const handleRowsPerPageChange = (e: any) => {
    setLimit(parseInt(e.target.value, 10));
    setPage(1);
  };
  const filter: IFilter = {
    limit,
    skip: (page - 1) * limit,
    order: ['createdOn DESC'],
    include: [
      {
        relation: 'order',
        required: true,
        scope: {
          where: {
            ...(selectedStatus && {status: selectedStatus}),
          },

          include: [{relation: 'promoCode'}, {relation: 'shippingAddress'}],
        },
      },
      {
        relation: 'productVariant',
        required: true,
        scope: {
          where: {
            name: {ilike: `%${debouncedSearch}%`},
          },
          include: [{relation: 'product'}, {relation: 'featuredAsset'}],
        },
      },
      {relation: 'review'},
    ],
  };
  const {
    data: orders,
    refetch,
    isLoading,
  } = useGetOrderQuery({filter}, {skip: !customerId});

  const {
    data: orderCount,
    isLoading: isCountLoading,
    isFetching: isCountFetching,
  } = useGetOrderCountQuery({
    filter: {
      where: {
        ...(selectedStatus && {status: selectedStatus}),
        productVariant: {
          name: {ilike: `%${debouncedSearch}%`},
        },
      },
    },
  });

  const totalOrders = orderCount?.count || 0;
  const handleReviewDeletedSuccess = () => {
    setEditReviewId(undefined);
  };

  const currencySymbols: Record<string, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    INR: '₹',
  };
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(searchQuery);
    }, 500);

    return () => clearTimeout(handler);
  }, [searchQuery]);

  const commonButtonStyle = {
    fontSize: '12px',
    fontWeight: 'bold',
    color: '#00004F',
    borderColor: 'transparent',
    textTransform: 'none',
    '&:hover': {
      borderColor: 'transparent',
      backgroundColor: 'rgba(0, 0, 79, 0.1)',
    },
  };
  if (isLoading || isCountLoading || isCountFetching) {
    return (
      <Grid item xs={12}>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          height="60vh"
        >
          <CircularProgress color="secondary" />
        </Box>
      </Grid>
    );
  }

  if (!orders || orders.length === 0) {
    return (
      <Grid item xs={12}>
        <Box sx={{p: 5, borderRadius: 2, textAlign: 'center'}}>
          <Typography
            variant="h3"
            fontWeight="bold"
            gutterBottom
            sx={{
              fontSize: {
                xs: '24px',
                sm: '30px',
                md: '36px',
              },
            }}
          >
            No Orders Found
          </Typography>
          <Typography variant="body1" color="text.secondary" mb={3}>
            You haven't placed any orders yet.
          </Typography>
          <Button
            variant="contained"
            sx={{mt: 3, borderRadius: '20px'}}
            onClick={() => router.push('/home')}
          >
            Back to Home
          </Button>
        </Box>
      </Grid>
    );
  }
  const handleDownloadInvoice = async (invoiceId: string) => {
    try {
      const response = await triggerDownloadPdf(invoiceId).unwrap();
      const blob = new Blob([response], {type: 'application/pdf'});
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `invoice-${invoiceId}.pdf`;
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download invoice PDF:', error);
    }
  };

  return (
    <Grid container spacing={0} sx={{marginTop: '2%'}}>
      {isLoading ? (
        <Grid item xs={12}>
          <Box
            display="flex"
            alignItems="center"
            justifyContent="center"
            height="60vh"
          >
            <CircularProgress color="secondary" />
          </Box>
        </Grid>
      ) : !orders || orders.length === 0 ? (
        <Grid item xs={12}>
          <Box sx={{p: 5, borderRadius: 2, textAlign: 'center'}}>
            <Typography
              variant="h3"
              fontWeight="bold"
              gutterBottom
              sx={{
                fontSize: {
                  xs: '24px',
                  sm: '30px',
                  md: '36px',
                },
              }}
            >
              No Orders Found
            </Typography>
            <Typography variant="body1" color="text.secondary" mb={3}>
              You haven't placed any orders yet.
            </Typography>
            <Button
              variant="contained"
              sx={{mt: 3, borderRadius: '20px'}}
              onClick={() => router.push('/home')}
            >
              Back to Home
            </Button>
          </Box>
        </Grid>
      ) : (
        <Grid
          item
          xs={12}
          sx={{
            width: '100%',
            maxWidth: {xs: '100%', sm: '100%', md: '100%', lg: '100%'},
          }}
        >
          <CardContent>
            <Grid container spacing={2} mb={2} alignItems="center">
              <Grid item xs={12} sm={8}>
                <TextField
                  variant="outlined"
                  size="small"
                  fullWidth
                  placeholder="Search for orders"
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  sx={{bgcolor: 'white', borderRadius: '25px'}}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchNormal />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={4}>
                <Select
                  size="small"
                  fullWidth
                  value={selectedStatus}
                  onChange={e => {
                    setSelectedStatus(e.target.value);
                  }}
                  displayEmpty
                  sx={{
                    borderRadius: '25px',
                    bgcolor: 'white',
                    '.MuiOutlinedInput-notchedOutline': {
                      borderColor: '#1E1E1E',
                    },
                  }}
                >
                  {orderStatusOptions.map(option => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </Grid>
            </Grid>

            {orders?.map((item, index) => {
              const order = item.order;
              const activeStep = getActiveStepIndex(
                order?.status as OrderStatus,
              );
              const showStepper = activeStep >= 0;

              return (
                <Accordion
                  sx={{
                    borderRadius: '12px',
                    boxShadow: 'none',
                    backgroundColor: '#F8F9FA',
                    mb: 2,
                    '&::before': {
                      display: 'none',
                    },
                  }}
                  key={item.id}
                >
                  <AccordionSummary
                    aria-controls="panel1-content"
                    id="panel1-header"
                    expandIcon={<ExpandMoreIcon />}
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      gap: 2,
                    }}
                  >
                    {' '}
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={12} sm={2}>
                        <CardMedia
                          component="img"
                          sx={{
                            width: '100%',
                            maxWidth: 100,
                            height: 'auto',
                            aspectRatio: '1',
                            objectFit: 'contain',
                            borderRadius: 2,
                            cursor: 'pointer',
                          }}
                          image={
                            item?.productVariant?.featuredAsset?.previewUrl ??
                            ''
                          }
                          alt={item?.productVariant?.name}
                          onClick={() =>
                            router.push(
                              `/product-details/${item.productVariantId}`,
                            )
                          }
                        />
                      </Grid>
                      <Grid item xs={9} sm={6}>
                        <Box display="flex" alignItems="center" mb={1}>
                          <Tooltip
                            title={item.productVariant?.name || ''}
                            placement="top"
                          >
                            <Typography
                              fontWeight="bold"
                              sx={{
                                display: '-webkit-box',
                                WebkitLineClamp: 2,
                                WebkitBoxOrient: 'vertical',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                fontSize: {
                                  xs: '13px',
                                  sm: '14px',
                                  md: '16px',
                                },
                              }}
                            >
                              {item.productVariant?.name || '---'}
                            </Typography>
                          </Tooltip>
                        </Box>

                        <Tooltip
                          title={
                            item.productVariant?.product.description || '---'
                          }
                          placement="top"
                        >
                          <Typography
                            fontSize="14px"
                            color="text.secondary"
                            sx={{
                              display: '-webkit-box',
                              WebkitLineClamp: 3,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                            }}
                          >
                            {item.productVariant?.product.description || '---'}
                          </Typography>
                        </Tooltip>

                        <Typography
                          fontWeight="bold"
                          fontSize="16px"
                          color="#0B1448"
                          sx={{mt: 1}}
                        >
                          {currencySymbols[order?.currency] ?? order.currency}{' '}
                          {item.totalPrice || '---'}
                        </Typography>
                      </Grid>
                      <Grid
                        item
                        xs={12}
                        sm={4}
                        textAlign={{xs: 'left', sm: 'right'}}
                      >
                        <Box
                          display="flex"
                          alignItems="center"
                          justifyContent={{
                            xs: 'flex-start',
                            sm: 'flex-end',
                          }}
                        >
                          <Box
                            sx={{
                              width: 10,
                              height: 10,
                              borderRadius: '50%',
                              backgroundColor:
                                statusColors[order.status as OrderStatus] ||
                                'grey',
                              mr: 1,
                            }}
                          />
                          <Typography
                            fontSize="12px"
                            color="text.secondary"
                            marginLeft="2px"
                          >
                            <b> {order.status}</b>
                          </Typography>
                        </Box>

                        <Typography fontSize="10px" color="text.secondary">
                          {fullStepConfig[activeStep]?.message ?? ''}
                        </Typography>

                        {order.status === OrderStatus.Delivered && (
                          <Box
                            display="flex"
                            justifyContent={{
                              xs: 'flex-start',
                              sm: 'flex-end',
                            }}
                            mt={1}
                          >
                            {item?.review?.id ? (
                              <Button
                                variant="outlined"
                                sx={commonButtonStyle}
                                onClick={() => {
                                  setReviewModalState({
                                    open: true,
                                    reviewId: item.review?.id,
                                    productVariantId: item.productVariantId,
                                    orderLineItemId: item.id,
                                    customerId: item.order.customerId,
                                  });
                                }}
                              >
                                View or Edit Review
                              </Button>
                            ) : (
                              <Button
                                variant="outlined"
                                startIcon={<Star1 size="14" color="#8B008B" />}
                                sx={commonButtonStyle}
                                onClick={() =>
                                  setReviewModalState({
                                    open: true,
                                    productVariantId: item.productVariantId,
                                    orderLineItemId: item.id,
                                    customerId: item.order.customerId,
                                  })
                                }
                              >
                                Rate & Review Product
                              </Button>
                            )}
                          </Box>
                        )}
                      </Grid>
                    </Grid>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid
                      container
                      spacing={2}
                      alignItems="center"
                      sx={{mt: 2}}
                    >
                      <Grid item xs={12} sm={4}>
                        <Typography fontSize="18px" fontWeight="bold">
                          Delivery Address
                        </Typography>

                        {item.order.shippingAddress ? (
                          <>
                            <Typography fontSize="15px">
                              {item.order.shippingAddress.name || '---'}
                            </Typography>

                            <Typography fontSize="14px" color="text.secondary">
                              {item.order?.shippingAddress?.addressLine1},{' '}
                              {item.order?.shippingAddress?.addressLine2},{' '}
                              {item.order?.shippingAddress?.locality}
                            </Typography>
                            <Typography fontSize="14px" color="text.secondary">
                              {item.order?.shippingAddress?.city},{' '}
                              {item.order?.shippingAddress?.state} -{' '}
                              {item.order?.shippingAddress?.zipCode},{' '}
                              {item.order?.shippingAddress?.country}
                            </Typography>
                          </>
                        ) : (
                          <Typography fontSize="14px" color="text.secondary">
                            Address not available
                          </Typography>
                        )}
                      </Grid>

                      <Grid item xs={12} sm={8} textAlign="right">
                        <Typography fontSize="16px" fontWeight="bold" mb={3}>
                          Quantity: {item.quantity || '---'}
                        </Typography>
                        {order.invoiceId && (
                          <Box
                            display="flex"
                            alignItems="center"
                            justifyContent="flex-end"
                          >
                            <Typography
                              variant="h5"
                              fontSize="14px"
                              color="#515151"
                              sx={{mr: 2, mt: 1}}
                            >
                              Invoice
                            </Typography>
                            <Button
                              variant="contained"
                              onClick={() =>
                                handleDownloadInvoice(order.invoiceId ?? '')
                              }
                              sx={{
                                borderRadius: '20px',
                                backgroundColor: '#151B54',
                                fontSize: '12px',
                                padding: '6px 16px',
                                textTransform: 'none',
                                marginTop: '1px',
                              }}
                            >
                              Download
                            </Button>
                          </Box>
                        )}
                      </Grid>
                    </Grid>

                    {showStepper && (
                      <Grid item xs={12} mt={3}>
                        <Stepper
                          alternativeLabel
                          activeStep={activeStep}
                          connector={<ColorlibConnector />}
                          sx={{flexWrap: 'wrap'}}
                        >
                          {fullStepConfig.map((step, index) => (
                            <Step key={step.label}>
                              <StepLabel
                                StepIconComponent={ColorlibStepIcon}
                                optional={
                                  activeStep === index ? (
                                    <Typography
                                      variant="caption"
                                      color="text.secondary"
                                    >
                                      {step.message}
                                    </Typography>
                                  ) : null
                                }
                              >
                                {step.label}
                              </StepLabel>
                            </Step>
                          ))}
                        </Stepper>
                      </Grid>
                    )}

                    <Grid item xs={12} mt={3}>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Button
                            variant="contained"
                            fullWidth
                            sx={{
                              mb: {xs: 1, sm: 0},
                              backgroundColor: '#8B008B',
                              borderRadius: '20px',
                              textTransform: 'none',
                            }}
                          >
                            Buy Again
                          </Button>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Button
                            variant="outlined"
                            fullWidth
                            sx={{
                              textTransform: 'none',
                              borderRadius: '20px',
                              borderColor: '#1E1E1E',
                              color: '#1E1E1E',
                            }}
                          >
                            Return/Refund
                          </Button>
                        </Grid>
                      </Grid>
                    </Grid>
                  </AccordionDetails>{' '}
                </Accordion>
              );
            })}
          </CardContent>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mt: 3,
            }}
          >
            <FormControl variant="standard" sx={{m: 1, minWidth: 120}}>
              <Select
                value={limit}
                onChange={handleRowsPerPageChange}
                label="Rows per page"
              >
                {PAGE_SIZE_OPTIONS.map(size => (
                  <MenuItem key={size} value={size}>
                    {size} per page
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <Typography variant="body2" sx={{mx: 2}}>
              Showing {(page - 1) * limit + 1}-
              {Math.min(page * limit, totalOrders)} of {totalOrders}
            </Typography>

            <Pagination
              count={Math.ceil(totalOrders / limit)}
              page={page}
              onChange={handlePageChange}
              color="primary"
              shape="rounded"
            />
          </Box>
        </Grid>
      )}
      <ReviewModal
        open={reviewModalState.open}
        onClose={() => setReviewModalState({open: false})}
        productVariantId={reviewModalState.productVariantId!}
        orderLineItemId={reviewModalState.orderLineItemId!}
        customerId={reviewModalState.customerId as string}
        reviewId={reviewModalState.reviewId}
        refetch={refetch}
        onDeleteSuccess={() => {
          setReviewModalState({open: false});
          handleReviewDeletedSuccess();
        }}
      />
    </Grid>
  );
};

export default OrderCard;
