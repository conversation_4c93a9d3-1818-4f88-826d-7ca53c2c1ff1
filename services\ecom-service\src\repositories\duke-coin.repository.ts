import {Getter, inject} from '@loopback/core';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {PgDataSource} from '../datasources';
import {Duke<PERSON>oin, DukeCoinRelations} from '../models';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {repository, BelongsToAccessor} from '@loopback/repository';
import {ReferralRepository} from './referral.repository';
import {Referral} from '../models/referral.model';

export class DukeCoinRepository extends SequelizeUserModifyCrudRepositoryCore<
  DukeCoin,
  typeof DukeCoin.prototype.id,
  DukeCoinRelations
> {
  public readonly referral: BelongsToAccessor<
    Referral,
    typeof DukeCoin.prototype.id
  >;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('ReferralRepository')
    protected referralRepositoryGetter: Getter<ReferralRepository>,
  ) {
    super(DukeCoin, dataSource, getCurrentUser);
    this.referral = this.createBelongsToAccessorFor(
      'referral',
      referralRepositoryGetter,
    );
    this.registerInclusionResolver('referral', this.referral.inclusionResolver);
  }

  async getUserBalance(): Promise<number> {
    const currentUser = await this.getCurrentUser();
    const userTenantId = currentUser.tenantId;
    const result = await this.dataSource.execute(
      `
  SELECT
    COALESCE(
      SUM(
        CASE
          WHEN transaction_type = 'Earn' THEN coins
          WHEN transaction_type = 'Redeem' THEN -coins_changed
          ELSE 0
        END
      ),
      0
    ) AS balance
  FROM main.duke_coins
  WHERE user_tenant_id = $1
    AND deleted = false
  `,
      [userTenantId],
    );

    const rows = result[0];
    let balance = 0;

    if (Array.isArray(rows) && rows.length > 0 && rows[0].balance != null) {
      balance = Number(parseFloat(rows[0].balance));
    }

    return balance;
  }
}
