import {Avatar, Box, Typography} from '@mui/material';
import {CardStyle} from 'enums/pageSection.enum';
import {useRouter} from 'next/navigation';

interface Props {
  title: string;
  subtitle?: string;
  thumbnail: string;
  facetValueIds: string[];
  cardStyle: CardStyle;
}

export default function FacetCard({
  title,
  subtitle,
  thumbnail,
  facetValueIds,
  cardStyle,
}: Props) {
  const router = useRouter();

  const handleClick = () => {
    router.push(`listing?facetValueIds=${facetValueIds.join(',')}`);
  };

  const cardWidth = 180;
  const cardHeight = 240;

  if (cardStyle === CardStyle.AVATAR_WITH_TITLE) {
    return (
      <Box
        onClick={handleClick}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          textAlign: 'center',
          width: 100,
        }}
      >
        <Avatar
          src={thumbnail}
          alt={title}
          sx={{
            width: 64,
            height: 64,
            mb: 1,
            bgcolor: 'white',
          }}
        />
        <Typography
          variant="subtitle2"
          fontWeight={500}
          sx={{fontSize: '0.85rem'}}
        >
          {title}
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      onClick={handleClick}
      sx={{
        width: cardWidth,
        height: cardHeight,
        borderRadius: 2,
        overflow: 'hidden',
        cursor: 'pointer',
        boxShadow: 1,
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: '#C8D3D5',
      }}
    >
      {(() => {
        switch (cardStyle) {
          case CardStyle.IMAGE_TITLE_SUBTITLE:
            return (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  height: '100%',
                  width: '100%',
                }}
              >
                <Box
                  sx={{
                    width: '100%',
                    height: '40%',
                    backgroundColor: '#C8D3D5',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'flex-start',
                    alignItems: 'flex-start',
                    textAlign: 'start',
                    p: 1.5,
                  }}
                >
                  <Typography
                    variant="h6"
                    fontWeight={700}
                    sx={{color: '#0E1C1F'}}
                  >
                    {title}
                  </Typography>
                  {subtitle && (
                    <Typography
                      variant="caption"
                      sx={{color: '#0E1C1F', mt: 0.5}}
                    >
                      {subtitle}
                    </Typography>
                  )}
                </Box>
                <Box
                  sx={{
                    width: '100%',
                    height: '60%',
                    backgroundImage: `url(${thumbnail})`,
                    backgroundSize: 'cover',
                    backgroundPosition: 'center',
                  }}
                />
              </Box>
            );

          case CardStyle.IMAGE_TITLE:
            return (
              <Box
                sx={{
                  position: 'relative',
                  width: '100%',
                  height: '100%',
                  backgroundImage: `url(${thumbnail})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  display: 'flex',
                  alignItems: 'flex-end',
                  p: 2,
                }}
              >
                <Typography
                  variant="subtitle1"
                  fontWeight={600}
                  sx={{textShadow: '0 0 5px rgba(0,0,0,0.6)', color: '#FFFFFF'}}
                >
                  {title}
                </Typography>
              </Box>
            );

          case CardStyle.IMAGE_ONLY:
            return (
              <Box
                sx={{
                  width: '100%',
                  height: '100%',
                  backgroundImage: `url(${thumbnail})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                }}
              />
            );

          default:
            return (
              <Box
                sx={{
                  position: 'relative',
                  width: '100%',
                  height: '100%',
                  backgroundImage: `url(${thumbnail})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'flex-end',
                  p: 2,
                }}
              >
                <Typography
                  variant="subtitle1"
                  color="white"
                  fontWeight={600}
                  sx={{textShadow: '0 0 5px rgba(0,0,0,0.6)'}}
                >
                  {title}
                </Typography>
                {subtitle && (
                  <Typography
                    variant="body2"
                    color="white"
                    sx={{textShadow: '0 0 5px rgba(0,0,0,0.6)'}}
                  >
                    {subtitle}
                  </Typography>
                )}
                <Typography
                  variant="caption"
                  color="white"
                  mt={1}
                  sx={{
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    px: 1,
                    borderRadius: 1,
                    alignSelf: 'flex-start',
                  }}
                >
                  Unknown Style: {cardStyle}
                </Typography>
              </Box>
            );
        }
      })()}
    </Box>
  );
}
