import * as Yup from 'yup';

export const loginValidationSchema = Yup.object().shape({
  username: Yup.string().email('Invalid email').required('Email is required'),
  password: Yup.string().required('Password is required'),
});

export const forgotPasswordSchema = Yup.object().shape({
  email: Yup.string().email('Invalid email').required('Email is required'),
});
export const changePasswordValidationSchema = Yup.object()
  .shape({
    old: Yup.string().required('Current Password is required'),
    password: Yup.string()
      .required('New Password is required')
      .matches(
        /^.*(?=.{8,})((?=.*[!@#$%^&*()\-_=+{};:,<.>]){1})(?=.*\d)((?=.*[a-z]){1})((?=.*[A-Z]){1}).*$/,
        'Password must contain at least 8 characters, one uppercase, one number and one special case character',
      )
      .test(
        'not-same-as-old',
        'New Password must be different from Current Password',
        function (value) {
          const {old} = this.parent;
          return value !== old;
        },
      ),
    confirm: Yup.string()
      .required('Confirm Password is required')
      .oneOf([Yup.ref('password')], "Passwords don't match."),
  })
  .test(
    'all-passwords-not-same',
    'Old, New, and Confirm Password must not be the same',
    function (values) {
      if (!values) return true;
      const {old, password, confirm} = values;
      return !(
        old &&
        password &&
        confirm &&
        old === password &&
        password === confirm
      );
    },
  );
// export const sellerFormValidation = Yup.object({
//   fbId: Yup.string()
//     .trim()
//     .matches(/.*\S.*/, 'Facebook Link cannot be empty or spaces only')
//     .test(
//       'fb-or-insta',
//       'At least one social field is required: Facebook or Instagram',
//       function (value) {
//         const {instaId} = this.parent;
//         return !!(value || instaId);
//       },
//     ),

//   instaId: Yup.string()
//     .trim()
//     .matches(/.*\S.*/, 'Instagram Link cannot be empty or spaces only')
//     .test(
//       'fb-or-insta',
//       'At least one social field is required: Facebook or Instagram',
//       function (value) {
//         const {fbId} = this.parent;
//         return !!(value || fbId);
//       },
//     ),

//   website: Yup.string()
//     .trim()
//     .matches(
//       /^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/,
//       'Please enter a valid website URL',
//     )
//     .nullable()
//     .notRequired(),
// });

export const sellerFormValidation = Yup.object({
  fbId: Yup.string()
    .trim()
    .url('Must be a valid URL')
    .test(
      'is-facebook-url',
      'Must be a Facebook link',
      value => !value || value.includes('facebook.com'),
    )
    .test(
      'fb-or-insta',
      'At least one social field is required: Facebook or Instagram',
      function (value) {
        const {instaId} = this.parent;
        return !!(value && value.trim()) || !!(instaId && instaId.trim());
      },
    ),

  instaId: Yup.string()
    .trim()
    .url('Must be a valid URL')
    .test(
      'is-instagram-url',
      'Must be an Instagram link',
      value => !value || value.includes('instagram.com'),
    )
    .test(
      'fb-or-insta',
      'At least one social field is required: Facebook or Instagram',
      function (value) {
        const {fbId} = this.parent;
        return !!(value && value.trim()) || !!(fbId && fbId.trim());
      },
    ),

  website: Yup.string()
    .trim()
    .url('Please enter a valid website URL')
    .nullable()
    .notRequired(),
});
