import { useEffect, useState } from 'react';
import { getMessaging, getToken } from 'firebase/messaging';
import firebaseApp from '../firebase/firebase';

const useFcmToken = () => {
  const [, setToken] = useState('');
  const [, setNotificationPermissionStatus] = useState('');

  useEffect(() => {
    const retrieveToken = async () => {
      try {
        if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
          const messaging = getMessaging(firebaseApp);

          const permission = await Notification.requestPermission();
          setNotificationPermissionStatus(permission);

          if (permission === 'granted') {
            const currentToken = await getToken(messaging, {
              vapidKey: 'BKHX5wUj2-MS_IOKMLD1QVl9Iz7hjR1yfYDTkyUuwnsAyIR7Vv9iTXOgZZ-KmslXEbG6PusktWhJR9W8mDQ87Us'
            });
            if (currentToken) {
              setToken(currentToken);
            } else {
            }
          }
        }
        /* eslint-disable @typescript-eslint/no-unused-vars */
      } catch (error) {}
    };

    retrieveToken();
  }, []);

  return null; //{ fcmToken: token, notificationPermissionStatus };
};

export default useFcmToken;
