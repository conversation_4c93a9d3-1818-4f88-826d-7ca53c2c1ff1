import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, repository} from '@loopback/repository';
import {PgDataSource} from '../datasources';
import {
  WeightBasedShippingRule,
  WeightBasedShippingRuleRelations,
  SellerShippingProfile,
} from '../models';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {SellerShippingProfileRepository} from './seller-shipping-profile.repository';

export class WeightBasedShippingRuleRepository extends SequelizeUserModifyCrudRepositoryCore<
  WeightBasedShippingRule,
  typeof WeightBasedShippingRule.prototype.id,
  WeightBasedShippingRuleRelations
> {
  public readonly shippingProfile: BelongsToAccessor<
    SellerShippingProfile,
    typeof WeightBasedShippingRule.prototype.id
  >;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('SellerShippingProfileRepository')
    protected sellerShippingProfileRepositoryGetter: Getter<SellerShippingProfileRepository>,
  ) {
    super(WeightBasedShippingRule, dataSource, getCurrentUser);

    this.shippingProfile = this.createBelongsToAccessorFor(
      'shippingProfile',
      sellerShippingProfileRepositoryGetter,
    );
    this.registerInclusionResolver(
      'shippingProfile',
      this.shippingProfile.inclusionResolver,
    );
  }
}
