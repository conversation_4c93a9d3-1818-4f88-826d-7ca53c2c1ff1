import {Getter, inject} from '@loopback/core';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {PgDataSource} from '../datasources';
import {Help, HelpRelations} from '../models';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';

export class HelpRepository extends SequelizeUserModifyCrudRepositoryCore<
  Help,
  typeof Help.prototype.id,
  HelpRelations
> {
  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {
    super(Help, dataSource, getCurrentUser);
  }
}
