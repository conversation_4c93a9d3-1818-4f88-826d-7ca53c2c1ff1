import * as Yup from 'yup';

export const shippingChargeValidation = Yup.object().shape({
  countryCode: Yup.string().required('Country is required'),
  stateCode: Yup.string(),
  baseCharge: Yup.number()
    .required('Price is required')
    .min(0, 'Price must be positive'),
  additionalItemPrice: Yup.number().min(0, 'Price must be positive'),
  estimatedDaysMin: Yup.number()
    .required('Minimum delivery days is required')
    .min(1, 'Minimum 1 day required'),
  estimatedDaysMax: Yup.number()
    .required('Maximum delivery days is required')
    .min(
      Yup.ref('estimatedDaysMin'),
      'Max days must be greater than or equal to min days',
    ),
});

export const weightBasedRuleValidation = Yup.object().shape({
  minWeight: Yup.number()
    .required('Minimum weight is required')
    .min(0, 'Weight must be positive'),
  maxWeight: Yup.number()
    .required('Maximum weight is required')
    .min(
      Yup.ref('minWeight'),
      'Max weight must be greater than or equal to min weight',
    ),
  charge: Yup.number()
    .required('Price is required')
    .min(0, 'Price must be positive'),
});

export const productShippingChargeValidation = Yup.object().shape({
  productId: Yup.string().required('Product is required'),
  baseCharge: Yup.number()
    .required('Price is required')
    .min(0, 'Price must be positive'),
});

export const shippingProfileValidation = Yup.object()
  .shape({
    name: Yup.string().required('Profile name is required'),
    description: Yup.string(),
    isDefault: Yup.boolean(),
    isActive: Yup.boolean(),
    shippingMethodId: Yup.string().required('Shipping method is required'),
    shippingCharges: Yup.array().of(shippingChargeValidation),
    weightBasedRules: Yup.array().of(weightBasedRuleValidation),
    productShippingCharges: Yup.array().of(productShippingChargeValidation),
  })
  .test(
    'at-least-one-shipping-option',
    'At least one shipping option (shipping charge, weight-based rule, or product-specific charge) is required',
    function (value) {
      const hasShippingCharges =
        value.shippingCharges && value.shippingCharges.length > 0;
      const hasWeightBasedRules =
        value.weightBasedRules && value.weightBasedRules.length > 0;
      const hasProductShippingCharges =
        value.productShippingCharges && value.productShippingCharges.length > 0;

      return (
        hasShippingCharges || hasWeightBasedRules || hasProductShippingCharges
      );
    },
  );
