import {inject} from '@loopback/core';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core/dist/repositories/sequelize-user-modify-crud.repository.base';
import {Legal, LegalRelations} from '../models';
import {SequelizeDataSource} from '@loopback/sequelize';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {Getter} from '@loopback/repository';

export class LegalRepository extends SequelizeUserModifyCrudRepositoryCore<
  Legal,
  typeof Legal.prototype.id,
  LegalRelations
> {
  constructor(
    @inject('datasources.pg') dataSource: SequelizeDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {
    super(Legal, dataSource, getCurrentUser);
  }
}
