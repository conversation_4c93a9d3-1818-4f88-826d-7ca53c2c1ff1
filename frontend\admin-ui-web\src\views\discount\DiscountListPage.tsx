'use client';

import { useMemo, useState, MouseEvent, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';
import IconButton from '../../components/@extended/IconButton';
import { Add, Edit, Eye, Trash } from 'iconsax-react';
import moment from 'moment-timezone';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from '../../utils/table-filter';
import Loader from 'components/Loader';
import { ThemeMode } from 'config';
import { Chip } from '@mui/material';
import { PermissionKeys } from 'enums/permission-keys.enum';
import { useAuth } from 'contexts/AuthContext';
import withPermission from 'hoc/withPermission';
import { useGetDiscountCountQuery, useGetDiscountListQuery } from 'redux/app/discount/discountApiSlice';
import DiscountTable from './DiscountTable';
import { Discount } from 'types/discount';
import AlertDiscountDelete from './AlertDiscountDelete';

const DiscountListPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
  const [discountDeleteId, setDiscountDeleteId] = useState<string>('');
  const [discountDeleteTitle, setDiscountDeleteTitle] = useState<string>('');

  const canCreate = hasPermission(PermissionKeys.CreateDiscount);
  const canEdit = hasPermission(PermissionKeys.UpdateDiscount);
  const canDelete = hasPermission(PermissionKeys.DeleteDiscount);

  const [open, setOpen] = useState<boolean>(false);
  const {
    data: discountList,
    isLoading: discountListLoading,
    refetch
  } = useGetDiscountListQuery({
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['name']),
    ...convertPaginationToLoopback(pagination)
  });
  const { data: discountCount, isLoading: discountCountLoading } = useGetDiscountCountQuery();

  const handleClose = useCallback(() => setOpen((prev) => !prev), []);
  useEffect(() => {
    const fetchData = async () => {
      if (refetch) {
        await refetch();
      }
    };
    fetchData();
  }, [refetch]);

  const columns = useMemo<ColumnDef<Discount>[]>(
    () => [
      {
        header: 'Name',
        accessorKey: 'name',
        enableSorting: true,
        cell: ({ row }) => <Typography>{row.original?.name ?? '-'}</Typography>
      },
      {
        header: 'Description',
        accessorKey: 'description',
        cell: ({ row }) => <Typography>{row.original?.description ?? '-'}</Typography>
      },
      {
        header: 'Start Date',
        accessorKey: 'startDate',
        enableSorting: true,
        cell: ({ row }) => <Typography>{row.original?.startDate ? moment(row.original.startDate).format('DD-MM-YYYY') : '-'}</Typography>
      },
      {
        header: 'End Date',
        accessorKey: 'endDate',
        enableSorting: true,
        cell: ({ row }) => <Typography>{row.original?.endDate ? moment(row.original.endDate).format('DD-MM-YYYY') : '-'}</Typography>
      },
      {
        header: 'Usage Limit',
        accessorKey: 'usageLimitPerUser',
        cell: ({ row }) => <Typography>{row.original?.usageLimitPerUser ?? '-'}</Typography>
      },
      {
        header: 'Combinable',
        accessorKey: 'combinable',
        cell: ({ row }) => (
          <Chip
            label={row.original?.combinable ? 'Yes' : 'No'}
            color={row.original?.combinable ? 'success' : 'default'}
            size="small"
            variant="outlined"
          />
        )
      },
      {
        header: 'Status',
        accessorKey: 'isActive',
        cell: ({ row }) => (
          <Chip
            label={row.original?.isActive ? 'Active' : 'Inactive'}
            color={row.original?.isActive ? 'success' : 'error'}
            size="small"
            variant="outlined"
          />
        )
      },
      {
        header: 'Created On',
        accessorKey: 'createdOn',
        enableSorting: true,
        cell: ({ row }) => <Typography>{moment(row.original?.createdOn).format('DD-MM-YYYY') ?? '-'}</Typography>
      },
      {
        header: 'Actions',
        enableSorting: false,
        cell: ({ row }) => {
          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add style={{ color: theme.palette.error.main, transform: 'rotate(45deg)' }} />
            ) : (
              <Eye />
            );

          return (
            <Stack direction="row" alignItems="center" spacing={0}>
              <Tooltip title="View">
                <IconButton
                  color="secondary"
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  {collapseIcon}
                </IconButton>
              </Tooltip>
              <Tooltip title="Edit">
                <IconButton
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    router.push(`/discount/edit/${row.original.id}`);
                  }}
                  disabled={!canEdit}
                  sx={{
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  <Edit />
                </IconButton>
              </Tooltip>
              <Tooltip title="Delete">
                <IconButton
                  color="error"
                  sx={{
                    ':hover': {
                      color: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.error[100]
                    }
                  }}
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    handleClose();
                    setDiscountDeleteId(row.original.id as string);
                    setDiscountDeleteTitle(row.original.name);
                  }}
                  disabled={!canDelete}
                >
                  <Trash />
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    [theme, router, canEdit, canDelete, handleClose]
  );

  return (
    <>
      {discountCountLoading || discountListLoading ? (
        <Loader />
      ) : (
        <DiscountTable
          {...{
            data: discountList as Discount[],
            columns,
            setSorting,
            sorting,
            columnFilters,
            setColumnFilters,
            loading: discountListLoading,
            globalFilter,
            setGlobalFilter,
            pagination,
            setPagination,
            totalRows: discountCount?.count ?? 0,
            canCreate,
            refetch
          }}
        />
      )}
      <AlertDiscountDelete refetch={refetch} id={discountDeleteId} title={discountDeleteTitle} open={open} handleClose={handleClose} />
    </>
  );
};

export default withPermission(PermissionKeys.ViewDiscount)(DiscountListPage);
