'use client';

import { useEffect, useRef, useState } from 'react';
import { useTheme, styled, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { <PERSON>rid, <PERSON>ack, Pop<PERSON>, TextField, ClickAwayListener, Box } from '@mui/material';

import EmojiPicker, { SkinTones, EmojiClickData } from 'emoji-picker-react';
import MainCard from 'components/MainCard';
import IconButton from 'components/@extended/IconButton';
import SimpleBar from 'components/third-party/SimpleBar';

import { EmojiHappy, Image as ImageIcon, Paperclip, Refresh, Send, VolumeHigh } from 'iconsax-react';

import ChatDrawer from 'components/chat/ChatDrawer';
import ChatHeader from 'components/chat/ChatHeader';
import ChatHistory from 'components/chat/ChatHistory';

import {
  useGetChatMessagesQuery,
  useGetSellerChatsQuery,
  useMarkChatAsReadMutation,
  useSendChatMessageMutation
} from 'redux/app/chat/chatApiSlice';

import { User } from 'types/user-profile';
import { ChatDto } from 'redux/app/types';

const drawerWidth = 320;

const Main = styled('main', {
  shouldForwardProp: (prop: string) => prop !== 'open'
})(({ theme, open }: { theme: Theme; open: boolean }) => ({
  flexGrow: 1,
  transition: theme.transitions.create('margin', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.shorter
  }),
  marginLeft: `-${drawerWidth}px`,
  [theme.breakpoints.down('lg')]: {
    paddingLeft: 0,
    marginLeft: 0
  },
  ...(open && {
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create('margin', {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.shorter
    }),
    marginLeft: 0
  })
}));

interface ExtendedUser extends User {
  presignedPhotoUrl?: string;
}

function extractUser(chat: ChatDto | null): ExtendedUser | null {
  const user = chat?.userData?.userTenant?.user;
  if (!user) return null;

  return {
    ...(user as User),
    presignedPhotoUrl: chat.userData?.presignedPhotoUrl ?? ''
  };
}

export default function Chat() {
  const theme = useTheme();
  const matchDownSM = useMediaQuery(theme.breakpoints.down('lg'));

  const [emailDetails] = useState(false);
  const [openChatDrawer, setOpenChatDrawer] = useState(true);
  const [message, setMessage] = useState('');
  const textInput = useRef<HTMLInputElement | null>(null);
  const emojiButtonRef = useRef<HTMLButtonElement | null>(null);

  const [selectedChat, setSelectedChat] = useState<ChatDto | null>(null);
  const user = extractUser(selectedChat);
  const [markChatAsRead] = useMarkChatAsReadMutation();

  const { data: chats = [] } = useGetSellerChatsQuery({});
  const { data: messages = [], refetch } = useGetChatMessagesQuery(selectedChat?.id ?? '', {
    skip: !selectedChat?.id
  });

  const [sendChatMessage] = useSendChatMessageMutation();
  const [emojiOpen, setEmojiOpen] = useState(false);
  const emojiId = emojiOpen ? 'simple-popper' : undefined;

  const handleDrawerOpen = () => setOpenChatDrawer((prev) => !prev);
  const handleOnEmojiButtonClick = () => setEmojiOpen((prev) => !prev);

  const handleOnSend = async () => {
    if (!message.trim() || !selectedChat?.id) return;
    await sendChatMessage({ chatId: selectedChat.id, message: { message } });
    setMessage('');
    refetch();
  };

  const handleReloadMessages = () => {
    if (selectedChat?.id) {
      refetch();
    }
  };

  const handleEnter = (event: React.KeyboardEvent<HTMLDivElement>) => {
    if (event.key === 'Enter') handleOnSend();
  };

  const onEmojiClick = (emojiObject: EmojiClickData) => {
    setMessage((prev) => prev + emojiObject.emoji);
  };

  useEffect(() => {
    setOpenChatDrawer(!matchDownSM);
  }, [matchDownSM]);

  useEffect(() => {
    if (chats.length && !selectedChat) setSelectedChat(chats[0]);
  }, [chats]);

  useEffect(() => {
    if (selectedChat?.id) {
      markChatAsRead(selectedChat.id);
      refetch();
    }
  }, [selectedChat?.id]);

  return (
    <Box sx={{ display: 'flex', overflow: 'hidden' }}>
      <ChatDrawer
        openChatDrawer={openChatDrawer}
        handleDrawerOpen={handleDrawerOpen}
        selectedChatId={selectedChat?.id ?? null}
        setSelectedChat={setSelectedChat}
        selectedUser={user}
        setUser={() => {}}
        chat={selectedChat}
      />

      <Main theme={theme} open={openChatDrawer} sx={{ minWidth: 0 }}>
        <Grid container height={1}>
          <Grid item xs={12} md={emailDetails ? 8 : 12} xl={emailDetails ? 9 : 12}>
            <MainCard
              content={false}
              sx={{
                height: 1,
                bgcolor: theme.palette.grey[50],
                pt: 2,
                pl: 2,
                borderRadius: emailDetails ? '0' : '0 12px 12px 0'
              }}
            >
              <Grid container spacing={2} height={1}>
                <Grid item xs={12} sx={{ pr: 2, pb: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
                  {user && <ChatHeader user={user} handleDrawerOpen={handleDrawerOpen} loading={false} />}
                </Grid>

                <Grid item xs={12}>
                  <SimpleBar sx={{ overflowX: 'hidden', height: 'calc(100vh - 416px)', minHeight: 420 }}>
                    <Box sx={{ pl: 1, pr: 3, pt: 1, height: '100%' }}>{user && <ChatHistory user={user} messages={messages} />}</Box>
                  </SimpleBar>
                </Grid>

                <Grid item xs={12} sx={{ mt: 3, borderTop: `1px solid ${theme.palette.divider}` }}>
                  <Stack>
                    <TextField
                      inputRef={textInput}
                      fullWidth
                      multiline
                      rows={2}
                      placeholder="Your Message..."
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      onKeyDown={handleEnter}
                      variant="standard"
                      sx={{ mt: 0.5 }}
                    />

                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <Stack direction="row" sx={{ py: 2, ml: -1 }}>
                        <IconButton color="secondary" onClick={handleReloadMessages}>
                          <Refresh />
                        </IconButton>
                        <IconButton
                          ref={emojiButtonRef}
                          aria-describedby={emojiId}
                          onClick={handleOnEmojiButtonClick}
                          sx={{ opacity: 0.5 }}
                          size="medium"
                          color="secondary"
                        >
                          <EmojiHappy />
                        </IconButton>
                        <Popper id={emojiId} open={emojiOpen} anchorEl={emojiButtonRef.current} disablePortal>
                          <ClickAwayListener onClickAway={() => setEmojiOpen(false)}>
                            <MainCard elevation={8} content={false}>
                              <EmojiPicker onEmojiClick={onEmojiClick} defaultSkinTone={SkinTones.DARK} />
                            </MainCard>
                          </ClickAwayListener>
                        </Popper>
                        <IconButton sx={{ opacity: 0.5 }} size="medium" color="secondary">
                          <Paperclip />
                        </IconButton>
                        <IconButton sx={{ opacity: 0.5 }} size="medium" color="secondary">
                          <ImageIcon />
                        </IconButton>
                        <IconButton sx={{ opacity: 0.5 }} size="medium" color="secondary">
                          <VolumeHigh />
                        </IconButton>
                      </Stack>
                      <IconButton color="secondary" onClick={handleOnSend} size="large" sx={{ mr: 1.5 }}>
                        <Send />
                      </IconButton>
                    </Stack>
                  </Stack>
                </Grid>
              </Grid>
            </MainCard>
          </Grid>
        </Grid>
      </Main>
    </Box>
  );
}
