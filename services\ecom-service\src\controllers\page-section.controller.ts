import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
} from '@loopback/rest';
import {PageSection, PageSectionBulkDto, PageSectionDto} from '../models';
import {PageSectionRepository} from '../repositories';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {service} from '@loopback/core';
import {PageSectionService} from '../services';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';

const basePath = '/page-sections';

export class PageSectionController {
  constructor(
    @repository(PageSectionRepository)
    public pageSectionRepository: PageSectionRepository,
    @service(PageSectionService)
    public pageSectionService: PageSectionService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreatePageSection]})
  @post(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'PageSection model instance',
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(PageSection)},
        },
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(PageSectionDto, {
            title: 'NewPageSection',
          }),
        },
      },
    })
    pageSection: PageSectionDto,
  ): Promise<PageSection> {
    return this.pageSectionService.createPageSection(pageSection);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreatePageSection]})
  @post(`${basePath}/bulk`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'PageSection model bulk instance',
        content: {
          schema: {
            type: 'array',
            items: getModelSchemaRef(PageSection),
          },
        },
      },
    },
  })
  async bulk(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(PageSectionBulkDto, {
            title: 'NewBulkPageSection',
          }),
        },
      },
    })
    pageSections: PageSectionBulkDto,
  ): Promise<PageSection[]> {
    return this.pageSectionService.createBulkPageSection(pageSections);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewPageSection]})
  @get(`${basePath}/count`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'PageSection model count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(
    @param.where(PageSection) where?: Where<PageSection>,
  ): Promise<Count> {
    return this.pageSectionRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewPageSection]})
  @get(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of PageSection model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(PageSection, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(
    @param.filter(PageSection) filter?: Filter<PageSection>,
  ): Promise<PageSection[]> {
    return this.pageSectionRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdatePageSection]})
  @patch(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'PageSection PATCH success count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(PageSection, {partial: true}),
        },
      },
    })
    pageSection: PageSection,
    @param.where(PageSection) where?: Where<PageSection>,
  ): Promise<Count> {
    return this.pageSectionRepository.updateAll(pageSection, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewPageSection]})
  @get(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'PageSection model instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(PageSection, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(PageSection, {exclude: 'where'})
    filter?: FilterExcludingWhere<PageSection>,
  ): Promise<PageSection> {
    return this.pageSectionRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdatePageSection]})
  @patch(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'PageSection PATCH success',
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(PageSection, {partial: true}),
        },
      },
    })
    pageSection: PageSection,
  ): Promise<void> {
    await this.pageSectionRepository.updateById(id, pageSection);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdatePageSection]})
  @put(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'PageSection PUT success',
      },
    },
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() pageSection: PageSection,
  ): Promise<void> {
    await this.pageSectionRepository.replaceById(id, pageSection);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeletePageSection]})
  @del(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'PageSection DELETE success',
      },
    },
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.pageSectionRepository.deleteById(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdatePageSection]})
  @patch(`${basePath}/{id}/reorder`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'PageSection PATCH success',
      },
    },
  })
  async reorder(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(PageSection, {partial: true}),
        },
      },
    })
    pageSection: PageSection,
  ): Promise<void> {
    await this.pageSectionService.reorderPageSection(id, pageSection);
  }
}
