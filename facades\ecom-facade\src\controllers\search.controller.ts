import {PermissionKeys} from '@local/core';
import {STATUS_CODE, CONTENT_TYPE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {FilterGroup, ProductVariant} from '../models';
import {get, getModelSchemaRef, param} from '@loopback/openapi-v3';
import {service} from '@loopback/core';
import {SearchService} from '../services';
import {Filter} from '@loopback/repository';

const basePath = 'search';
export class SearchController {
  constructor(
    @service(SearchService)
    private readonly searchService: SearchService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}/suggestions`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Asset model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(ProductVariant, {
                partial: true,
              }),
            },
          },
        },
      },
    },
  })
  async getSuggestions(
    @param.query.string('keyword') keyword: string,
  ): Promise<Partial<ProductVariant[]>> {
    return this.searchService.searchSuggestions(keyword);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Search model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(ProductVariant, {
                partial: true,
              }),
            },
          },
        },
      },
    },
  })
  async search(
    @param.filter(ProductVariant)
    filter: Filter<ProductVariant>,
    @param.query.string('keyword') keyword?: string,
    @param.query.string('facetValueIds') facetValueIdsStr?: string,
    @param.query.string('collectionIds') collectionIdsStr?: string,
  ): Promise<Partial<ProductVariant[]>> {
    const facetValueIds = facetValueIdsStr?.split(',').filter(Boolean);
    const collectionIds = collectionIdsStr?.split(',').filter(Boolean);

    return this.searchService.search({
      keyword,
      facetValueIds,
      collectionIds,
      filter,
    });
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewProduct]})
  @get(`${basePath}/filters`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Filter model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(FilterGroup),
            },
          },
        },
      },
    },
  })
  async getFilters(
    @param.query.string('keyword') keyword?: string,
    @param.query.string('facetValueIds') facetValueIds?: string,
    @param.query.string('collectionIds') collectionIds?: string,
  ): Promise<FilterGroup[]> {
    return this.searchService.getFilters(keyword, facetValueIds, collectionIds);
  }
}
