import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {CreateDiscountDto, Discount} from '../models';
import {DiscountRepository} from '../repositories';
import {STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {PermissionKeys} from '@local/core';
import {authorize} from 'loopback4-authorization';
import {service} from '@loopback/core';
import {DiscountService} from '../services';

const basePath = '/discounts';

export class DiscountController {
  constructor(
    @repository(DiscountRepository)
    public discountRepository: DiscountRepository,
    @service(DiscountService)
    public discountService: DiscountService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateDiscount]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Discount model instance',
    content: {'application/json': {schema: getModelSchemaRef(Discount)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CreateDiscountDto, {
            title: 'NewDiscountWithConditions',
            exclude: ['id'],
          }),
        },
      },
    })
    discountDto: CreateDiscountDto,
  ): Promise<Discount> {
    return this.discountService.createDiscountWithConditions(discountDto);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDiscount]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'Discount model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(@param.where(Discount) where?: Where<Discount>): Promise<Count> {
    return this.discountRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDiscount]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Discount model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Discount, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Discount) filter?: Filter<Discount>,
  ): Promise<Discount[]> {
    return this.discountRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateDiscount]})
  @patch(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Discount PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Discount, {partial: true}),
        },
      },
    })
    discount: Discount,
    @param.where(Discount) where?: Where<Discount>,
  ): Promise<Count> {
    return this.discountRepository.updateAll(discount, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDiscount]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'Discount model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Discount, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Discount, {exclude: 'where'})
    filter?: FilterExcludingWhere<Discount>,
  ): Promise<Discount> {
    return this.discountRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateDiscount]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Discount PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CreateDiscountDto, {partial: true}),
        },
      },
    })
    discount: CreateDiscountDto,
  ): Promise<void> {
    await this.discountService.updateDiscountWithConditions(id, discount);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDiscount]})
  @post(`${basePath}/check-eligibility`)
  @response(STATUS_CODE.OK, {
    description: 'Check first-order discount eligibility',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            isEligible: {type: 'boolean'},
            appliedDiscount: {
              type: ['object', 'null'],
              properties: {
                conditionId: {type: 'string'},
                discountValue: {type: 'number'},
                discountType: {type: 'string'},
              },
            },
            nextTierMessage: {type: 'string'},
            discountBreakdownMessage: {type: 'string'},
          },
        },
      },
    },
  })
  async checkFirstOrderDiscountEligibility(
    @requestBody({
      description: 'Cart and user context for discount evaluation',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              customerId: {type: 'string'},
              cartTotal: {type: 'number'},
              isFromApp: {type: 'boolean'},
            },
          },
        },
      },
    })
    body: {
      customerId: string;
      cartTotal: number;
      isFromApp: boolean;
    },
  ): Promise<unknown> {
    return this.discountService.getEligibleFirstOrderDiscount(body);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateDiscount]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Discount PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() discount: Discount,
  ): Promise<void> {
    await this.discountRepository.replaceById(id, discount);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteDiscount]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Discount DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.discountRepository.deleteById(id);
  }
}
