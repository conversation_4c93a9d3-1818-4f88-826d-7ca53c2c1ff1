import {
  GetObjectCommand,
  PutObjectCommand,
  PutObjectCommandInput,
  S3Client,
} from '@aws-sdk/client-s3';
import {injectable, BindingScope} from '@loopback/core';
import {v4 as uuidv4} from 'uuid';
import {getSignedUrl} from '@aws-sdk/s3-request-presigner';
import {promisify} from 'util';
import fs from 'fs';

const unlinkAsync = promisify(fs.unlink);

@injectable({scope: BindingScope.TRANSIENT})
export class S3HelperService {
  s3Client: S3Client;
  private readonly expiresIn: number;
  constructor() {
    this.s3Client = new S3Client({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
      },
    });
    this.expiresIn =
      Number(process.env.ASSES_EXPIRY_TIME_IN_HOUR ?? 1) * 60 * 60;
  }

  async uploadFileToS3(file: Express.Multer.File, bucket: string) {
    const originalName = file.originalname;
    const fileExtension = originalName.split('.').pop();
    const key = `${uuidv4()}.${fileExtension}`;
    const params: PutObjectCommandInput = {
      Bucket: bucket,
      Key: key,
      Body: file.buffer,
      ContentType: file.mimetype,
    };
    await this.s3Client.send(new PutObjectCommand(params));
    return key;
  }

  async getPresignedUrl(key: string, bucket: string): Promise<string> {
    if (!key) return '';

    const command = new GetObjectCommand({
      Bucket: bucket,
      Key: key,
    });

    return getSignedUrl(this.s3Client, command, {
      expiresIn: this.expiresIn,
    });
  }

  async uploadFileToS3FromDisk(filePath: string, bucket: string) {
    try {
      // Extract original filename from path
      const originalName = filePath.split('/').pop() ?? filePath;

      // Process filename and extension
      const fileNameWithoutExt =
        originalName.substring(0, originalName.lastIndexOf('.')) ||
        originalName;
      const fileExtension = originalName.split('.').pop();
      const key = `${fileNameWithoutExt}-${uuidv4()}.${fileExtension}`;

      // Read file from local filesystem
      const fileContent = fs.readFileSync(filePath);

      // Upload to S3
      const params: PutObjectCommandInput = {
        Bucket: bucket,
        Key: key,
        Body: fileContent,
        ContentType: this.getMimeType(fileExtension ?? ''),
      };

      await this.s3Client.send(new PutObjectCommand(params));

      // Delete the file after successful upload
      await unlinkAsync(filePath);

      return key;
    } catch (error) {
      // Log the error but don't delete the file if upload failed
      console.error('Error uploading file to S3:', error);
      throw error; // Re-throw the error for the caller to handle
    }
  }

  private getMimeType(extension: string): string {
    // Implement your MIME type mapping logic here
    const mimeTypes: Record<string, string> = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      pdf: 'application/pdf',
      // Add more as needed
    };
    return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
  }
}
