import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

@model({name: 'user_fcms'})
export class UserFcm extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'fcm_token',
  })
  fcmToken: string;

  @property({
    type: 'string',
    name: 'device_id',
  })
  deviceId?: string;

  @property({
    type: 'string',
    name: 'user_tenant_id',
    required: true,
  })
  userTenantId?: string;

  constructor(data?: Partial<UserFcm>) {
    super(data);
  }
}

export interface UserFcmRelations {
  // describe navigational properties here
}

export type UserFcmWithRelations = UserFcm & UserFcmRelations;
