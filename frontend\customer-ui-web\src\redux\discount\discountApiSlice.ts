import {apiSlice} from 'redux/apiSlice';
import {ApiSliceIdentifier} from 'enums/api.enum';
import {Discount, DiscountEligibilityResponse} from 'types/discount';
import {buildFilterParams, IFilter} from 'types/api';

export const discountApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getDiscountCount: builder.query<{count: number}, void>({
      query: () => ({
        url: '/discounts/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),

    getDiscountList: builder.query<
      Discount[],
      {
        limit?: number;
        skip?: number;
        order?: Array<Record<string, unknown> | string>;
        where?: Record<string, unknown>;
        fields?: Record<string, boolean>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({limit, skip, where, fields, include, order} = {}) => ({
        url: '/discounts',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            limit,
            offset: skip,
            where,
            order,
            fields,
            include,
          }),
        },
      }),
    }),

    getDiscountById: builder.query<Discount, {id: string; filter?: IFilter}>({
      query: ({id, filter}) => ({
        url: `/discounts/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    checkDiscountEligibility: builder.query<
      DiscountEligibilityResponse,
      {
        cartTotal: number;
        isFromApp: boolean;
      }
    >({
      query: body => ({
        url: '/discounts/check-eligibility',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body,
      }),
    }),
  }),
});

export const {
  useGetDiscountByIdQuery,
  useGetDiscountListQuery,
  useGetDiscountCountQuery,
  useCheckDiscountEligibilityQuery,
} = discountApiSlice;
