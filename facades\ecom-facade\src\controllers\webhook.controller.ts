import {post, requestBody} from '@loopback/openapi-v3';
import {
  CONTENT_TYPE,
  rateLimitKeyGenPublic,
  STATUS_CODE,
} from '@sourceloop/core';
import {authorize} from 'loopback4-authorization';
import {ratelimit} from 'loopback4-ratelimiter';
import {CashfreeWebhookService} from '../services';
import {service} from '@loopback/core';
import {AnyObject} from '@loopback/repository';
const basePath = '/webhooks';
export class WebhookController {
  constructor(
    @service(CashfreeWebhookService)
    private cashfreeWebhookService: CashfreeWebhookService,
  ) {}

  @ratelimit(true, {
    max: parseInt(process.env.PUBLIC_API_MAX_ATTEMPTS ?? '5'),
    keyGenerator: rateLimitKeyGenPublic,
  })
  @authorize({permissions: ['*']})
  // @intercept('interceptors.CashfreeWebhookInterceptor')
  @post(`${basePath}/payment`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Webhook Success response',
      },
    },
  })
  async webhook(
    @requestBody({
      description: 'Webhook request body',
      required: true,
      content: {
        [CONTENT_TYPE.JSON]: {
          'x-parser': 'raw',
          schema: {type: 'object'},
        },
      },
    })
    event: AnyObject,
  ): Promise<void> {
    await this.cashfreeWebhookService.handlePaymentWebhook(event);
  }
}
