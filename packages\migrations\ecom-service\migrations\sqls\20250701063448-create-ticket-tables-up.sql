/* Replace with your SQL commands */
CREATE TABLE IF NOT EXISTS main.tickets (
    id             uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    category       varchar(100) NOT NULL,
    title          varchar(255) NOT NULL,
    description    text NOT NULL,
    priority       integer DEFAULT 0 NOT NULL,  
    short_code     varchar(50) UNIQUE NOT NULL,
    status         varchar(100) NOT NULL,
    assignee       uuid, 
    attachments    text, 
    created_on     timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on    timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by     uuid,
    modified_by    uuid,
    deleted        boolean DEFAULT false NOT NULL,
    deleted_by     uuid,
    deleted_on     timestamptz,
    CONSTRAINT pk_legals_id PRIMARY KEY (id)
);

ALTER TABLE main.legals
ADD COLUMN visibility integer DEFAULT 0 NOT NULL;


