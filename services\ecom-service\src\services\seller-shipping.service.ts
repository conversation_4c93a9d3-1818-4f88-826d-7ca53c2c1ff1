import {BindingScope, injectable} from '@loopback/core';
import {
  SellerShippingProfile,
  SellerShippingCharge,
  WeightBasedShippingRule,
  ProductShippingCharge,
} from '../models';
import {SellerShippingProfileDto} from '../models/dto/seller-shipping-profile.dto';
import {
  SellerShippingProfileRepository,
  SellerShippingChargeRepository,
  WeightBasedShippingRuleRepository,
  ProductShippingChargeRepository,
  ShippingMethodRepository,
} from '../repositories';
import {HttpErrors} from '@loopback/rest';
import {Transaction} from '@loopback/sequelize';
import {FilterExcludingWhere, repository} from '@loopback/repository';

@injectable({scope: BindingScope.TRANSIENT})
export class SellerShippingService {
  constructor(
    @repository(SellerShippingProfileRepository)
    private sellerShippingProfileRepository: SellerShippingProfileRepository,
    @repository(SellerShippingChargeRepository)
    private sellerShippingChargeRepository: SellerShippingChargeRepository,
    @repository(WeightBasedShippingRuleRepository)
    private weightBasedShippingRuleRepository: WeightBasedShippingRuleRepository,
    @repository(ProductShippingChargeRepository)
    private productShippingChargeRepository: ProductShippingChargeRepository,
    @repository(ShippingMethodRepository)
    private shippingMethodRepository: ShippingMethodRepository,
  ) {}

  /**
   * Create a shipping profile with associated charges and rules
   */
  async createShippingProfile(
    profileDto: SellerShippingProfileDto,
  ): Promise<SellerShippingProfile> {
    // Validate shipping method exists
    const shippingMethod = await this.shippingMethodRepository.findById(
      profileDto.shippingMethodId,
    );

    if (!shippingMethod) {
      throw new HttpErrors.NotFound('Shipping method not found');
    }
    this.isWithoutAllCharges(profileDto);

    // Check if seller already has a profile with this shipping method type
    const existingProfile = await this.sellerShippingProfileRepository.findOne({
      where: {
        sellerId: profileDto.sellerId,
        shippingMethodId: profileDto.shippingMethodId,
      },
    });

    if (existingProfile) {
      throw new HttpErrors.Conflict(
        'Seller already has a shipping profile with this shipping method',
      );
    }
    const transaction =
      await this.sellerShippingProfileRepository.dataSource.beginTransaction({
        isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,
      });
    try {
      const profile = await this.sellerShippingProfileRepository.create(
        new SellerShippingProfile({
          name: profileDto.name,
          description: profileDto.description,
          isDefault: profileDto.isDefault,
          isActive: profileDto.isActive,
          sellerId: profileDto.sellerId,
          shippingMethodId: profileDto.shippingMethodId,
        }),
        {transaction},
      );
      // Create shipping charges if provided
      if (profileDto.shippingCharges && profileDto.shippingCharges.length > 0) {
        const chargePromises = profileDto.shippingCharges.map(charge =>
          this.sellerShippingChargeRepository.create(
            new SellerShippingCharge({
              ...charge,
              shippingProfileId: profile.id,
            }),
            {transaction},
          ),
        );
        await Promise.all(chargePromises);
      }

      // Create weight-based rules if provided
      if (
        profileDto.weightBasedRules &&
        profileDto.weightBasedRules.length > 0
      ) {
        const rulePromises = profileDto.weightBasedRules.map(rule =>
          this.weightBasedShippingRuleRepository.create(
            new WeightBasedShippingRule({
              ...rule,
              shippingProfileId: profile.id,
            }),
            {transaction},
          ),
        );
        await Promise.all(rulePromises);
      }

      // Create product-specific shipping charges if provided
      if (
        profileDto.productShippingCharges &&
        profileDto.productShippingCharges.length > 0
      ) {
        const productChargePromises = profileDto.productShippingCharges.map(
          charge =>
            this.productShippingChargeRepository.create(
              new ProductShippingCharge({
                ...charge,
                shippingProfileId: profile.id,
              }),
              {transaction},
            ),
        );
        await Promise.all(productChargePromises);
      }

      await transaction.commit();
      // Return the created profile with relations
      return profile;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Update a shipping profile with associated charges and rules
   */
  async updateShippingProfile(
    id: string,
    profileDto: Partial<SellerShippingProfileDto>,
  ): Promise<void> {
    // Check if profile exists
    const existingProfile =
      await this.sellerShippingProfileRepository.findById(id);

    if (!existingProfile) {
      throw new HttpErrors.NotFound('Shipping profile not found');
    }
    this.isWithoutAllCharges(profileDto);
    const transaction =
      await this.sellerShippingProfileRepository.dataSource.beginTransaction({
        isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,
      });
    try {
      // Update the profile
      await this.sellerShippingProfileRepository.updateById(
        id,
        {
          name: profileDto.name,
          description: profileDto.description,
          isDefault: profileDto.isDefault,
          isActive: profileDto.isActive,
        },
        {transaction},
      );

      // Update shipping charges if provided
      if (profileDto.shippingCharges && profileDto.shippingCharges.length > 0) {
        // Delete existing charges
        await this.sellerShippingChargeRepository.deleteAllHard(
          {shippingProfileId: id},
          {transaction},
        );

        // Create new charges
        const chargePromises = profileDto.shippingCharges.map(charge =>
          this.sellerShippingChargeRepository.create(
            new SellerShippingCharge({
              ...charge,
              shippingProfileId: id,
            }),
            {transaction},
          ),
        );
        await Promise.all(chargePromises);
      }

      // Update weight-based rules if provided
      if (
        profileDto.weightBasedRules &&
        profileDto.weightBasedRules.length > 0
      ) {
        // Delete existing rules
        await this.weightBasedShippingRuleRepository.deleteAllHard(
          {shippingProfileId: id},
          {transaction},
        );

        // Create new rules
        const rulePromises = profileDto.weightBasedRules.map(rule =>
          this.weightBasedShippingRuleRepository.create(
            new WeightBasedShippingRule({
              ...rule,
              shippingProfileId: id,
            }),
            {transaction},
          ),
        );
        await Promise.all(rulePromises);
      }

      // Update product-specific shipping charges if provided
      if (
        profileDto.productShippingCharges &&
        profileDto.productShippingCharges.length > 0
      ) {
        // Delete existing product charges
        await this.productShippingChargeRepository.deleteAllHard(
          {shippingProfileId: id},
          {transaction},
        );

        // Create new product charges
        const productChargePromises = profileDto.productShippingCharges.map(
          charge =>
            this.productShippingChargeRepository.create(
              new ProductShippingCharge({
                ...charge,
                shippingProfileId: id,
              }),
              {transaction},
            ),
        );
        await Promise.all(productChargePromises);
      }

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get shipping profile with all related entities
   */
  async getShippingProfileWithRelations(
    id: string,
  ): Promise<SellerShippingProfile> {
    return this.sellerShippingProfileRepository.findById(id, {
      include: [
        {relation: 'shippingCharges'},
        {relation: 'weightBasedRules'},
        {relation: 'productShippingCharges'},
        {relation: 'shippingMethod'},
      ],
    });
  }

  /**
   * Get all shipping profiles for a seller
   */
  async getSellerShippingProfiles(
    sellerId: string,
    filter?: FilterExcludingWhere<SellerShippingProfile>,
  ): Promise<SellerShippingProfile[]> {
    const defaultFilter = {
      where: {sellerId, isActive: true},
      include: [
        {relation: 'shippingCharges'},
        {relation: 'weightBasedRules'},
        {relation: 'productShippingCharges'},
        {relation: 'shippingMethod'},
      ],
    };
    filter = filter ? {...defaultFilter, ...filter} : defaultFilter;
    return this.sellerShippingProfileRepository.find(filter);
  }

  private isWithoutAllCharges(profile: Partial<SellerShippingProfileDto>) {
    if (
      !profile.shippingCharges?.length &&
      !profile.weightBasedRules?.length &&
      !profile.productShippingCharges?.length
    ) {
      throw new HttpErrors.BadRequest(
        'At least one shipping option is required',
      );
    }
  }
}
