import React from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  StyleSheet,
  ImageStyle,
} from 'react-native';
import customColors from '../../../../theme/customColors';

type SubCategoryItem = {
  id: number;
  name: string;
  image: any;
};

type Props = {
  data?: SubCategoryItem[];
  showNames?: boolean;
  imageStyle?: ImageStyle;
  imageContainerStyle?: any;
  overlayNames?: boolean;
};

const SubCategoryList = ({
  data = [],
  showNames = true,
  imageStyle,
  overlayNames = false,
  imageContainerStyle,
}: Props) => (
  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
    {data.map(item => (
      <View key={item.id} style={imageContainerStyle}>
        <View style={styles.imageWrapper}>
          <Image
            source={{uri: item.image}}
            style={[styles.image, imageStyle]}
            resizeMode="cover"
          />
          {overlayNames && (
            <View style={styles.overlayTextContainer}>
              <Text style={styles.overlayText}>{item.name}</Text>
            </View>
          )}
        </View>

        {showNames && (
          <Text style={styles.subCategoryName} numberOfLines={2}>
            {item.name}
          </Text>
        )}
      </View>
    ))}
  </ScrollView>
);

export default SubCategoryList;
const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
    paddingHorizontal: 8,
    paddingBottom: 8,
    backgroundColor: customColors.white,
  },
  imageContainer: {
    alignItems: 'center',
    marginHorizontal: 5,
    marginTop: 10,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 10,
  },
  subCategoryName: {
    marginTop: 5,
    fontSize: 14,
    textAlign: 'center',
    maxWidth: 70,
    flexWrap: 'wrap',
  },
  imageWrapper: {
    position: 'relative',
  },
  overlayTextContainer: {
    position: 'absolute',
    top: 5,
    left: 5,
    right: 5,
    paddingVertical: 2,
    paddingHorizontal: 4,
    borderRadius: 4,
  },
  overlayText: {
    color: customColors.white,
    fontSize: 12,
    fontWeight: 'bold',
  },
});
