import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from '../apiSlice';
import { ChangePasswordRequest, Faq, SignupDto, TokenResponse } from 'types/auth';
import { ILoginForm } from './types';
import { User } from 'types/user-profile';
import { ApiTagTypes } from 'redux/types';
import { FaqVisibility } from 'enums/faqvisibility.enum';

export const authApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    login: builder.mutation({
      query: (body: ILoginForm) => ({
        url: '/auth/login',
        method: 'POST',
        body: {
          ...body,
          client_id: process.env.NEXT_PUBLIC_LOCAL_CLIENT_ID,
          client_secret: process.env.NEXT_PUBLIC_LOCAL_CLIENT_SECRET
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    exchangeToken: builder.mutation<TokenResponse, { code: string; clientId?: string }>({
      query: ({ code, clientId }) => ({
        url: '/auth/token',
        method: 'POST',
        body: {
          clientId: clientId ?? process.env.NEXT_PUBLIC_LOCAL_CLIENT_ID,
          code
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        headers: {
          Authorization: `Bearer ${code}`
        }
      })
    }),
    logout: builder.mutation({
      query: (refreshToken: string | null) => ({
        url: '/logout',
        method: 'POST',
        body: { refreshToken },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    createSignUpToken: builder.mutation<{ code: string }, string>({
      query: (email: string) => ({
        url: '/auth/sign-up/create-token',
        method: 'POST',
        cache: 'no-cache',
        body: {
          email,
          data: {
            client_id: process.env.NEXT_PUBLIC_LOCAL_CLIENT_ID,
            client_secret: process.env.NEXT_PUBLIC_LOCAL_CLIENT_SECRET
          }
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    signUp: builder.mutation({
      query: ({ token, signupData }: { token: string; signupData: SignupDto }) => ({
        url: '/auth/sign-up/create-user',
        method: 'POST',
        body: {
          ...signupData,
          clientId: process.env.NEXT_PUBLIC_LOCAL_CLIENT_ID,
          clientSecret: process.env.NEXT_PUBLIC_LOCAL_CLIENT_SECRET
        },
        headers: {
          Authorization: `Bearer ${token}`
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    getUser: builder.query<User, void>({
      query: () => ({
        url: '/auth/me',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        providesTags: [ApiTagTypes.User]
      })
    }),

    updateUser: builder.mutation<void, { userId: string; body: FormData }>({
      query: ({ userId, body }) => ({
        url: `/profile/${userId}`,
        method: 'PATCH',
        body,
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),

    getFaqs: builder.query<Faq[], void>({
      query: () => ({
        url: `/faqs`,
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              visibility: { inq: [FaqVisibility.SELLER, FaqVisibility.ALL] }
            },
            order: ['priority DESC']
          })
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    updatePassword: builder.mutation<void, Omit<ChangePasswordRequest, 'refreshToken'>>({
      query: (body) => {
        const refreshToken = localStorage.getItem('refreshToken');
        return {
          url: '/auth/change-password',
          method: 'PATCH',
          body: {
            ...body,
            refreshToken
          },
          apiSliceIdentifier: ApiSliceIdentifier.AUTH_SERVICE
        };
      }
    }),
    createReferralCode: builder.mutation<{ referralCode: string }, { referrerId: string }>({
      query: ({ referrerId }) => ({
        url: '/referrals',
        method: 'POST',
        body: { referrerId },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    saveFCMToken: builder.mutation<void, { fcmToken: string | null; deviceId?: string; userTenantId: string }>({
      query: ({ fcmToken, deviceId, userTenantId }) => ({
        url: '/user-fcms',
        method: 'POST',
        body: {
          fcmToken: fcmToken,
          deviceId: deviceId,
          userTenantId: userTenantId
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    })
  })
});

export const {
  useGetUserQuery,
  useLoginMutation,
  useExchangeTokenMutation,
  useLogoutMutation,
  useCreateSignUpTokenMutation,
  useSignUpMutation,
  useUpdateUserMutation,
  useGetFaqsQuery,
  useUpdatePasswordMutation,
  useCreateReferralCodeMutation,
  useSaveFCMTokenMutation
} = authApiSlice;
