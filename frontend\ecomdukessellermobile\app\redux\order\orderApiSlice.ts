import {ApiSliceIdentifier} from '../../constants/enums';
import {IFilter} from '../../types/filter';
import {OrderLineItem} from '../../types/order';
import {buildFilterParams} from '../../utils/buildFilterParams';
import {apiSlice} from '../apiSlice';

export const orderApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getOrder: builder.query<OrderLineItem[], {filter?: IFilter}>({
      query: ({filter}) => ({
        url: '/order-line-items',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
    getOrderDetailsById: builder.query<
      OrderLineItem,
      {id: string; filter: IFilter}
    >({
      query: ({id, filter}) => ({
        url: `/order-line-items/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
    getOrderCount: builder.query<{count: number}, void>({
      query: () => ({
        url: '/order-line-items/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
    updateOrder: builder.mutation<
      void,
      {id: string; rejectionReason: string; data: Partial<OrderLineItem>}
    >({
      query: ({id, data, rejectionReason}) => ({
        url: `/order-line-items/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: {rejectionReason, ...data},
      }),
    }),
  }),
});

export const {
  useGetOrderQuery,
  useLazyGetOrderQuery,
  useGetOrderDetailsByIdQuery,
  useLazyGetOrderDetailsByIdQuery,
  useGetOrderCountQuery,
  useUpdateOrderMutation,
} = orderApiSlice;
