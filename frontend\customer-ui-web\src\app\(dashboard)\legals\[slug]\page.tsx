'use client';

import {Box, Grid} from '@mui/material';
import {useParams} from 'next/navigation';
import UserCard from 'views/order-details/UserCard';
import AccountMenu from 'views/order-details/AccountMenu';
import {Legals} from 'types/legal-category.enum';
import LegalContent from 'views/legals/LegalContent';

const slugToTypeMap: Record<string, Legals> = {
  terms: Legals.TermsofUse,
  privacy: Legals.PrivacyPolicy,
  affiliate: Legals.AffiliatePolicy,
  refund: Legals.ReturnRefundPolicy,
  infringement: Legals.InfringementPolicy,
  agreements: Legals.Agreements,
  licence: Legals.Licence,
  disclaimer: Legals.Disclaimer,
  guidelines: Legals.Guideline,
};

const LegalPage = () => {
  const {slug} = useParams();
  const legalType = slugToTypeMap[slug as string];

  return (
    <Grid container spacing={3} px={6}>
      <Grid item xs={12} sm={3}>
        <UserCard imageUrl={''} name={''} />
        <Box mt={2}>
          <AccountMenu />
        </Box>
      </Grid>

      <Grid item xs={12} sm={9}>
        <LegalContent type={legalType} />
      </Grid>
    </Grid>
  );
};

export default LegalPage;
