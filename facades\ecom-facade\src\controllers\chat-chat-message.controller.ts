import {Count, CountSchema, Filter, Where} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
  RestBindings,
  Request,
} from '@loopback/rest';
import {Chat, ChatMessage} from '../models';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {inject} from '@loopback/core';
import {
  CONTENT_TYPE,
  ModifiedRestService,
  restService,
  STATUS_CODE,
} from '@sourceloop/core';

export class ChatChatMessageController {
  private token: string;

  constructor(
    @restService(ChatMessage)
    private readonly chatService: ModifiedRestService<ChatMessage>,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    }
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewChatMessage]})
  @get('/chats/{id}/chat-messages', {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Chat has many ChatMessage',
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(ChatMessage)},
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<ChatMessage>,
  ): Promise<ChatMessage[]> {
    const finalFilter: Filter<ChatMessage> = {
      where: {
        chatId: id,
        ...(filter?.where ?? {}),
      },
      ...filter,
    };

    return this.chatService.find(finalFilter, this.token);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateChatMessage]})
  @post('/chats/{id}/chat-messages', {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'ChatMessage model instance',
        content: {
          [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(ChatMessage)},
        },
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Chat.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ChatMessage, {
            title: 'NewChatMessageInChat',
            exclude: ['id', 'chatId'],
          }),
        },
      },
    })
    chatMessage: Omit<ChatMessage, 'id'>,
  ): Promise<ChatMessage> {
    return this.chatService.create({...chatMessage, chatId: id}, this.token);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateChatMessage]})
  @patch('/chats/{id}/chat-messages', {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'ChatMessage PATCH success count',
        content: {
          [CONTENT_TYPE.JSON]: {schema: CountSchema},
        },
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(ChatMessage, {partial: true}),
        },
      },
    })
    chatMessage: Partial<ChatMessage>,
    @param.query.object('where', getWhereSchemaFor(ChatMessage))
    where?: Where<ChatMessage>,
  ): Promise<Count> {
    const finalWhere: Where<ChatMessage> = {
      chatId: id,
      ...(where ?? {}),
    };

    return this.chatService.update(chatMessage, finalWhere, this.token);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteChatMessage]})
  @del('/chats/{id}/chat-messages', {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'ChatMessage DELETE success coun',
        content: {
          [CONTENT_TYPE.JSON]: {schema: CountSchema},
        },
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(ChatMessage))
    where?: Where<ChatMessage>,
  ): Promise<void> {
    const finalWhere: Where<ChatMessage> = {
      chatId: id,
      ...(where ?? {}),
    };

    return this.chatService.delete(finalWhere, this.token);
  }
}
