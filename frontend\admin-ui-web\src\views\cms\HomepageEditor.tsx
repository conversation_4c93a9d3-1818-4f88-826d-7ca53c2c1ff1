import React, { useEffect, useState } from 'react';
import { useFormik } from 'formik';
import { Button, TextField, MenuItem, Typography, IconButton, Grid, Box, CircularProgress, FormControlLabel, Switch } from '@mui/material';
import { v4 as uuidv4 } from 'uuid';
import { Add, Save2, Trash } from 'iconsax-react';
import { SectionType, CardStyle, PageSectionDto, SectionItemType, BulkSection, SectionItem, PageType } from 'types/cms';
import MainCard from 'components/MainCard';
import { CarouselItem } from './Carousel';
import { sectionsValidationSchema } from '../../../validations/cms';
import { BannerItem } from './BannerItem';
import { FeaturedProductsItem } from './FeaturedProductsItem';
import { TextBlockItem } from './TextBlockItem';
import { FeaturedCollectionItem } from './FeaturedCollectionItem';
import { ProductCardsItem } from './ProductCardsItem';
import { useSnackbar } from 'notistack';
import Loader from 'components/Loader';
import { useUploadFileMutation } from 'redux/app/contents/fileApiSlice';
import {
  useGetPageSectionsQuery,
  useRemovePageSectionMutation,
  useReorderPageSectionMutation,
  useUpsertPageSectionMutation
} from 'redux/app/contents/page-section/cmsApiSlice';
import { fieldsExcludeMetaFields } from 'constants/shared';
import { ProductFilterItem } from './ProductFilterItem';
import { ConfirmDeleteModal } from './ConfirmSectionDeleteModal';
import { FacetItem } from './FacetItem';

const HomepageEditor = () => {
  const [fileMap, setFileMap] = useState<Record<string, File>>({});
  const [isUploading, setIsUploading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [sectionToDeleteIndex, setSectionToDeleteIndex] = useState<number | null>(null);

  const { enqueueSnackbar } = useSnackbar();

  const [updatePageSections] = useUpsertPageSectionMutation();
  const [uploadFiles] = useUploadFileMutation();
  const {
    data: pageSections,
    isLoading,
    refetch
  } = useGetPageSectionsQuery({
    where: { pageType: 'home' },
    order: ['displayOrder ASC'],
    include: [{ relation: 'sectionItems', scope: { fields: fieldsExcludeMetaFields } }]
  });
  const [deletePageSection] = useRemovePageSectionMutation();
  const [reorderPageSection] = useReorderPageSectionMutation();
  const [editedDisplayOrders, setEditedDisplayOrders] = useState<Record<string, number | null>>({});
  const [displayOrderErrors, setDisplayOrderErrors] = useState<Record<string, string | null>>({});

  const formik = useFormik<BulkSection>({
    initialValues: {
      sections: []
    },
    validationSchema: sectionsValidationSchema,
    onSubmit: async (values) => {
      await handleSaveAll(values);
    }
  });

  useEffect(() => {
    if (pageSections && !isLoading) {
      formik.setValues({
        sections: pageSections.map((section) => ({
          ...section,
          items: section.sectionItems || []
        }))
      });
    }
  }, [pageSections, isLoading]);

  const handleFileUpload = async (file: File, id: string, fileUrlMap: Record<string, string>) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('ids', id);
    const result = await uploadFiles(formData).unwrap();
    fileUrlMap[id] = result.key;
    return fileUrlMap;
  };

  const handleFileUploads = async (): Promise<Record<string, string>> => {
    if (Object.keys(fileMap).length === 0) return {};

    setIsUploading(true);
    const fileUrls: Record<string, string> = {};

    try {
      const promiseArray: Promise<Record<string, string>>[] = [];
      Object.entries(fileMap).forEach(([id, file]) => {
        promiseArray.push(handleFileUpload(file, id, fileUrls));
      });

      const res = await Promise.all(promiseArray);
      return res[0];
    } catch (error) {
      console.error('Error uploading files:', error);
      enqueueSnackbar('Error uploading files', { variant: 'error' });
      return {};
    } finally {
      setIsUploading(false);
    }
  };

  const handleSaveAll = async (values: BulkSection) => {
    setIsSaving(true);

    // First upload all files
    const fileUrls = await handleFileUploads();

    // Update image URLs with the uploaded file URLs
    const updatedSections = values.sections.map((section) => ({
      ...section,
      items: section.items?.map((item) => {
        if (fileUrls[item.id]) {
          return {
            ...item,
            imageUrl: fileUrls[item.id]
          };
        }
        return item;
      })
    }));

    // Save sections to API
    await updatePageSections({
      sections: updatedSections
    }).unwrap();

    enqueueSnackbar('Homepage sections saved successfully', { variant: 'success' });

    // Clear file map after successful upload
    setFileMap({});
    setIsSaving(false);
  };

  const handleAddSection = () => {
    const newSection = {
      type: SectionType.CAROUSEL,
      title: 'New Section',
      displayOrder: formik.values.sections.length + 1,
      isActive: true,
      items: [],
      pageType: PageType.HOME
    };

    formik.setFieldValue('sections', [...formik.values.sections, newSection]);
  };

  const handleAddItem = (sectionIndex: number) => {
    const section = formik.values.sections[sectionIndex];
    const newItem = {
      id: uuidv4(),
      entityType: SectionItemType.IMAGE,
      title: 'New Item',
      displayOrder: (section.items?.length ?? 0) + 1
    };
    section.items = section.items ?? [];
    formik.setFieldValue(`sections.${sectionIndex}.items`, [...section.items, newItem]);
  };

  const handleItemChange = (sectionIndex: number, itemIndex: number, updatedItem: SectionItem) => {
    const sections = formik.values.sections || [];
    const section = sections[sectionIndex] || { items: [] };
    const items = section.items || [];
    const existingItem = items[itemIndex] || {};

    const updatedSection = {
      ...section,
      items: [...items.slice(0, itemIndex), { ...existingItem, ...updatedItem }, ...items.slice(itemIndex + 1)]
    };

    const updatedSections = [...sections.slice(0, sectionIndex), updatedSection, ...sections.slice(sectionIndex + 1)];

    formik.setFieldValue('sections', updatedSections);
  };

  const handleConfirmDelete = async () => {
    if (sectionToDeleteIndex === null) return;

    const section = formik.values.sections[sectionToDeleteIndex];

    if (section.id) {
      await deletePageSection(section.id).unwrap();
    }

    const updatedSections = [...formik.values.sections];
    updatedSections.splice(sectionToDeleteIndex, 1);
    formik.setFieldValue('sections', updatedSections);
    enqueueSnackbar('Section deleted successfully', { variant: 'success' });
    setConfirmModalOpen(false);
    setSectionToDeleteIndex(null);
  };

  const handleDeleteItem = (sectionIndex: number, itemIndex: number) => {
    const sections = formik.values.sections || [];
    const section = sections[sectionIndex] || { items: [] };
    const items = section.items || [];

    // Remove the item at itemIndex
    const updatedItems = items.filter((_, idx) => idx !== itemIndex);

    // Update display order
    const reorderedItems = updatedItems.map((item, index) => ({
      ...item,
      displayOrder: index + 1
    }));

    // Build updated section
    const updatedSection = {
      ...section,
      items: reorderedItems
    };

    // Build updated sections array
    const updatedSections = [...sections.slice(0, sectionIndex), updatedSection, ...sections.slice(sectionIndex + 1)];

    // Update Formik
    formik.setFieldValue('sections', updatedSections);
  };

  const createSectionDefaultItem = (sectionIndex: number) => {
    const sections = formik.values.sections || [];
    const section = sections[sectionIndex] || { type: '', items: [] };
    const sectionType = section.type;

    let defaultItem: SectionItem = {
      id: uuidv4(),
      entityType: SectionItemType.IMAGE,
      title: '',
      displayOrder: 1
    };

    switch (sectionType) {
      case SectionType.CAROUSEL:
        defaultItem = {
          ...defaultItem,
          metadata: { redirectUrl: '' }
        };
        break;
      case SectionType.FEATURED_PRODUCTS:
        defaultItem = {
          ...defaultItem,
          entityType: SectionItemType.PRODUCT,
          metadata: { productIds: [] }
        };
        break;

      case SectionType.TEXT_BLOCK:
        defaultItem = {
          ...defaultItem,
          entityType: SectionItemType.TEXT,
          metadata: { richText: '' }
        };
        break;
      // Add more cases for other section types as needed
    }

    const existingItems = section.items || [];

    // Only add default item if no items exist
    if (existingItems.length === 0) {
      const updatedSection = {
        ...section,
        items: [defaultItem]
      };

      const updatedSections = [...sections.slice(0, sectionIndex), updatedSection, ...sections.slice(sectionIndex + 1)];

      formik.setFieldValue('sections', updatedSections);
    }
  };

  const { handleChange } = formik;

  const handleDisplayOrderChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, sectionId: string) => {
    const newValue = Number(e.target.value);
    const maxAllowed = Math.max(...formik.values.sections.map((s) => s.displayOrder ?? 0));

    setEditedDisplayOrders((prev) => ({
      ...prev,
      [sectionId]: newValue
    }));

    if (newValue < 1) {
      setDisplayOrderErrors((prev) => ({
        ...prev,
        [sectionId]: 'Display order must be at least 1'
      }));
    } else if (newValue > maxAllowed + 1) {
      setDisplayOrderErrors((prev) => ({
        ...prev,
        [sectionId]: `Display order cannot exceed ${maxAllowed + 1}`
      }));
    } else {
      setDisplayOrderErrors((prev) => ({
        ...prev,
        [sectionId]: null
      }));
    }
  };

  const handleDisplayOrderUpdate = async (sectionId: string) => {
    const newValue = editedDisplayOrders[sectionId];
    const error = displayOrderErrors[sectionId];

    if (!newValue || error) return;

    await reorderPageSection({
      id: sectionId,
      displayOrder: newValue
    }).unwrap();

    enqueueSnackbar('Display order updated successfully', { variant: 'success' });

    setEditedDisplayOrders((prev) => ({ ...prev, [sectionId]: null }));
    setDisplayOrderErrors((prev) => ({ ...prev, [sectionId]: null }));
    refetch();
  };

  const ItemFormRenderer = ({
    sectionIndex,
    itemData,
    onChange
  }: {
    sectionIndex: number;
    itemData: any;
    onChange: (updatedItem: any) => void;
  }) => {
    const sectionType = formik.values.sections[sectionIndex]?.type;
    switch (sectionType) {
      case SectionType.CAROUSEL:
        return <CarouselItem data={itemData} onChange={onChange} setFileMap={setFileMap} fileMap={fileMap} />;
      case SectionType.FEATURED_PRODUCTS:
        return <FeaturedProductsItem data={itemData} onChange={onChange} />;
      case SectionType.BANNER:
        return <BannerItem data={itemData} onChange={onChange} setFileMap={setFileMap} fileMap={fileMap} />;
      case SectionType.TEXT_BLOCK:
        return <TextBlockItem data={itemData} onChange={onChange} />;
      case SectionType.FEATURED_COLLECTION:
        return <FeaturedCollectionItem data={itemData} onChange={onChange} setFileMap={setFileMap} fileMap={fileMap} />;
      case SectionType.PRODUCT_CARDS:
        return <ProductCardsItem data={itemData} onChange={onChange} />;
      case SectionType.PRODUCT_FILTER:
        return <ProductFilterItem data={itemData} onChange={onChange} setFileMap={setFileMap} fileMap={fileMap} />;
      case SectionType.FACETS:
        return <FacetItem data={itemData} onChange={onChange} setFileMap={setFileMap} fileMap={fileMap} />;
      default:
        return null;
    }
  };

  const renderSectionForm = (section: PageSectionDto, secIndex: number) => (
    <Grid container spacing={2}>
      <Grid item xs={12}>
        <TextField fullWidth name={`sections.${secIndex}.title`} label="Title" value={section.title} onChange={handleChange} />
      </Grid>
      <Grid item xs={12}>
        <TextField
          select
          fullWidth
          name={`sections.${secIndex}.type`}
          label="Section Type"
          value={section.type}
          onChange={(event) => {
            createSectionDefaultItem(secIndex);
            handleChange(event);
          }}
        >
          {Object.entries(SectionType).map(([key, val]) => (
            <MenuItem key={key} value={val}>
              {val}
            </MenuItem>
          ))}
        </TextField>
      </Grid>

      {/* Add Card Style selection */}
      {shouldShowCardStyle(section.type) && (
        <Grid item xs={12}>
          <TextField
            select
            fullWidth
            name={`sections.${secIndex}.cardStyle`}
            label="Card Style"
            value={section.cardStyle || ''}
            onChange={handleChange}
          >
            {getCardStylesForSectionType(section.type).map((style) => (
              <MenuItem key={style} value={style}>
                {style}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
      )}

      {/* Toggle for showing title when cardStyle is AVATAR_WITH_TITLE */}
      {section.cardStyle === CardStyle.AVATAR_WITH_TITLE && (
        <Grid item xs={12}>
          <FormControlLabel
            control={
              <Switch
                checked={section.metadata?.showTitle !== false}
                onChange={(e) => {
                  const updatedMetadata = {
                    ...section.metadata,
                    showTitle: e.target.checked
                  };
                  formik.setFieldValue(`sections.${secIndex}.metadata`, updatedMetadata);
                }}
              />
            }
            label="Display Title Below Avatar"
          />
        </Grid>
      )}

      {section.id && (
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="subtitle1" sx={{ mr: 2, minWidth: 120 }}>
              Display Order:
            </Typography>
            <TextField
              type="number"
              size="small"
              value={editedDisplayOrders[section.id!] !== undefined ? editedDisplayOrders[section.id!] : section.displayOrder}
              onChange={(e) => handleDisplayOrderChange(e, section.id!)}
              error={Boolean(displayOrderErrors[section.id!])}
              helperText={displayOrderErrors[section.id!]}
              inputProps={{ min: 1, style: { textAlign: 'center' } }}
              sx={{ width: 100, mr: 2 }}
            />
            <Button
              variant="contained"
              color="primary"
              size="small"
              disabled={
                editedDisplayOrders[section.id!] === null ||
                editedDisplayOrders[section.id!] === section.displayOrder ||
                Boolean(displayOrderErrors[section.id!])
              }
              onClick={() => handleDisplayOrderUpdate(section.id!)}
            >
              Update
            </Button>
          </Box>
        </Grid>
      )}
    </Grid>
  );

  const shouldShowCardStyle = (sectionType: SectionType) => {
    return [SectionType.FEATURED_COLLECTION, SectionType.PRODUCT_CARDS, SectionType.FACETS].includes(sectionType);
  };

  const getCardStylesForSectionType = (sectionType: SectionType) => {
    switch (sectionType) {
      case SectionType.FEATURED_COLLECTION:
        return [CardStyle.IMAGE_TITLE_SUBTITLE, CardStyle.IMAGE_TITLE, CardStyle.AVATAR_WITH_TITLE];
      case SectionType.PRODUCT_CARDS:
        return [CardStyle.PRODUCT_CARD, CardStyle.OFFER_CARD, CardStyle.OFFER_CARD_INVERSE];
      case SectionType.FACETS:
        return [CardStyle.IMAGE_ONLY, CardStyle.IMAGE_TITLE, CardStyle.IMAGE_TITLE_SUBTITLE, CardStyle.AVATAR_WITH_TITLE];
      default:
        return Object.values(CardStyle);
    }
  };

  return (
    <MainCard title="Homepage Editor">
      {false ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <Loader />
        </Box>
      ) : (
        <form onSubmit={formik.handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="h4">Sections</Typography>
              </Box>
            </Grid>

            {formik.values.sections.map((section, secIndex) => (
              <Grid item xs={12} key={section.id}>
                <MainCard
                  title={section.title || `Section ${secIndex + 1}`}
                  secondary={
                    <IconButton
                      color="error"
                      onClick={() => {
                        const section = formik.values.sections[secIndex];
                        if (section.id) {
                          setSectionToDeleteIndex(secIndex);
                          setConfirmModalOpen(true);
                        } else {
                          const updatedSections = [...formik.values.sections];
                          updatedSections.splice(secIndex, 1);
                          formik.setFieldValue('sections', updatedSections);
                        }
                      }}
                    >
                      <Trash />
                    </IconButton>
                  }
                >
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      {renderSectionForm(section, secIndex)}
                    </Grid>
                    <Grid item xs={12} md={8}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                        <Typography variant="h5">Items</Typography>
                        {section.items?.length !== 0 && (
                          <Button variant="outlined" startIcon={<Add />} onClick={() => handleAddItem(secIndex)}>
                            Add Item
                          </Button>
                        )}
                      </Box>

                      {section.items?.map((item, itemIndex) => (
                        <Box key={item.id} sx={{ mb: 2, position: 'relative' }}>
                          <Box sx={{ position: 'absolute', top: 8, right: 8, zIndex: 1 }}>
                            <IconButton color="error" size="small" onClick={() => handleDeleteItem(secIndex, itemIndex)}>
                              <Trash />
                            </IconButton>
                          </Box>
                          <ItemFormRenderer
                            sectionIndex={secIndex}
                            itemData={item}
                            onChange={(updatedItem) => handleItemChange(secIndex, itemIndex, updatedItem)}
                          />
                        </Box>
                      ))}

                      {section.items?.length === 0 && (
                        <Box sx={{ p: 3, textAlign: 'center', bgcolor: 'background.paper', borderRadius: 1 }}>
                          <Typography color="textSecondary">No items added yet</Typography>
                          <Button variant="text" startIcon={<Add />} onClick={() => handleAddItem(secIndex)} sx={{ mt: 1 }}>
                            Add Item
                          </Button>
                        </Box>
                      )}
                    </Grid>
                  </Grid>
                </MainCard>
              </Grid>
            ))}

            {formik.values.sections.length === 0 && (
              <Grid item xs={12}>
                <Box sx={{ p: 4, textAlign: 'center', bgcolor: 'background.paper', borderRadius: 1 }}>
                  <Typography variant="h6" color="textSecondary" gutterBottom>
                    No sections added yet
                  </Typography>
                  <Button variant="contained" startIcon={<Add />} onClick={handleAddSection}>
                    Add First Section
                  </Button>
                </Box>
              </Grid>
            )}

            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                {formik.values.sections.length !== 0 && (
                  <Button variant="contained" sx={{ mr: 2 }} color="primary" startIcon={<Add />} onClick={handleAddSection}>
                    Add Section
                  </Button>
                )}
                <Button
                  variant="contained"
                  color="primary"
                  type="submit"
                  disabled={isUploading || isSaving || !formik.dirty || !formik.isValid}
                  startIcon={isSaving || isUploading ? <CircularProgress size={20} /> : <Save2 />}
                >
                  {isSaving || isUploading ? 'Saving...' : 'Save All Changes'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      )}
      <ConfirmDeleteModal
        open={confirmModalOpen}
        onClose={() => setConfirmModalOpen(false)}
        onConfirm={handleConfirmDelete}
        title={formik.values.sections[sectionToDeleteIndex!]?.title}
      />
    </MainCard>
  );
};

export default HomepageEditor;
