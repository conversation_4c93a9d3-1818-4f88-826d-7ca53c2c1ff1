import * as yup from 'yup';
export const productSchema = yup.object({
  name: yup.string().required('Product name is required'),
  description: yup.string().required('Product Description is required'),
  enabled: yup.boolean().required('Enabled status is required'),
  assets: yup.array().of(yup.string().uuid('Invalid media ID')),
  featuredAssetId: yup.string().uuid('Invalid featured media ID').required('Featured media ID is required'),
  sellerId: yup.string().required('Seller is required'),
  collectionId: yup.string().uuid('Invalid category ID').required('category is required'),
  isGiftWrapCharge: yup
    .number()
    .transform((value, originalValue) => {
      return originalValue === '' ? undefined : value;
    })
    .when('isGiftWrapAvailable', {
      is: true,
      then: (schema) => schema.required('Gift wrap charge is required').min(0),
      otherwise: (schema) => schema.strip() // removes from final value
    }),

  options: yup
    .array()
    .of(
      yup.object({
        id: yup.number().required('Option ID is required'),
        name: yup.string().required('Option name is required'),
        unit: yup.string().required('Option Unit is required'),
        values: yup
          .array()
          .of(
            yup.object({
              id: yup.number().required('Value ID is required'),
              name: yup.string().required('Value name is required')
            })
          )
          .min(1)
          .required('Option values are required')
      })
    )
    .optional(),
  variants: yup
    .array()
    .of(
      yup.object({
        id: yup.number().required('Variant ID is required'),
        name: yup.string().required('Variant name is required'),
        price: yup.number().required('Variant Price is required').min(0, 'Price must be positive'),
        mrp: yup.number().required('Variant MRP is required').min(0, 'MRP must be positive'),
        outOfStockThreshold: yup.number().min(0, 'Out of stock threshold cannot be negative'),
        optionIds: yup.array().of(yup.number().required('Option ID is required'))
      })
    )
    .optional(),
  facets: yup.array().of(yup.string().uuid('Invalid Tag ID')).optional(),
  boxContents: yup
    .array()
    .of(
      yup.object({
        itemName: yup.string().required('Box content item is required'),
        quantity: yup.number().required('Box content quantity is required').min(1, 'Quantity must be at least 1')
      })
    )
    .optional(),
  details: yup
    .object({
      details: yup.string().required('Please provide product details.')
    })
    .nullable()
    .default(undefined),

  uniqueness: yup
    .object({
      uniqueness: yup.string().required('Please provide product unique features.')
    })
    .nullable()
    .default(undefined),

  suitability: yup
    .object({
      suitableFor: yup.string().required('Please provide information about the product suitability.')
    })
    .nullable()
    .default(undefined),

  personalWork: yup
    .object({
      workLevel: yup.string().required('Please describe how you personalize or craft this product.')
    })
    .nullable()
    .default(undefined),

  specifications: yup
    .array()
    .of(
      yup.object({
        name: yup.string().required('Please provide a name for this specification.'),
        value: yup.string().required('Please provide a value for this specification.')
      })
    )
    .nullable()
    .default(undefined),

  returnPolicy: yup
    .object({
      returnPolicy: yup.string().required('Please fill in the return policy.')
    })
    .nullable()
    .default(undefined),

  terms: yup
    .object({
      terms: yup.string().required('Please provide terms and conditions.')
    })
    .nullable()
    .default(undefined),

  disclaimer: yup
    .object({
      disclaimer: yup.string().required('Please provide Disclaimer.')
    })
    .nullable()
    .default(undefined)
});
