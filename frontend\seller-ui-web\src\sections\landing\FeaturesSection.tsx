'use client';

// next
import Image from 'next/image';

// material-ui
import Grid from '@mui/material/Grid';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';

// project-imports
const store = '/assets/images/landing/store.svg';
const brand = '/assets/images/landing/star.svg';
const product = '/assets/images/landing/product.svg';
const calculator = '/assets/images/landing/calculator.svg';
const direction = '/assets/images/landing/arrow.svg';
const payment = '/assets/images/landing/card.svg';
const delivery = '/assets/images/landing/boxes.svg';
const application = '/assets/images/landing/mobile.svg';
const analytics = '/assets/images/landing/analytics.svg';

const features = [
  {
    icon: store,
    title: 'EXCLUSIVE STORE',
    description:
      'Get an Exclusive store to sell your Hand made and Personalised Products. Add Banner Image, Pin Products to top and much more.'
  },
  {
    icon: brand,
    title: 'BUILD IDENTITY AND GROW AS A BRAND',
    description: 'Become a GST Registered seller, setup your store with your Brand Name & Logo. Sell all over India.'
  },
  {
    icon: product,
    title: 'MORE VISIBILITY FOR YOUR PRODUCTS',
    description:
      'Get more Product Visibility in ECOMDUKES compared to other marketplaces which sell products in many categories like Electronics, Grocery, Books etc.'
  },
  {
    icon: calculator,
    title: 'PROFIT CALCULATOR',
    description: 'Determine your profit even before listing the product and eliminates any chance of going into loss.'
  },
  {
    icon: direction,
    title: 'NO REDIRECTION LINK (NRL)',
    description:
      'A Sharable Unique Link for your Store which when accessed only shows your store and your products. No other sellers’ store or products will be shown to a user visiting your store through NRL.'
  },
  {
    icon: payment,
    title: 'MULTIPLE PAYMENT OPTIONS',
    description: 'Multiple payment options like UPI, Wallets, Credit/Debit Card Payments to collect payments securely.'
  },
  {
    icon: delivery,
    title: 'PAN INDIA DELIVERY',
    description: 'Our Delivery Partners pick products from your doorsteps and deliver for you to more than 25000+ pin codes in India.'
  },
  {
    icon: application,
    title: 'ECOMDUKES SELLER APP',
    description:
      'Manage your store, products and orders wherever you go with EcomDukes Seller App. Easily manage inventory, track sales, and communicate with customers.'
  },
  {
    icon: analytics,
    title: 'BASIC ANALYTICS',
    description:
      'Get basic insights and data about the actions happening inside your store and helps you make plans to grow your business strategically.'
  }
];

export default function FeaturesPage() {
  return (
    <Container>
      <Grid container spacing={2} justifyContent="center" textAlign={'center'} sx={{ mb: { md: 5, xs: 2.5 } }}>
        <Grid item xs={12}>
          <Typography variant="h2" sx={{ color: '#00004F', fontWeight: 'bold' }}>
            Why Sell on EcomDukes?
          </Typography>
        </Grid>
      </Grid>
      <Grid container spacing={3}>
        {features.map((feature, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card
              sx={{
                p: 2,
                height: '100%',
                width: '100%',
                borderRadius: 2,
                boxShadow: '0px 4px 10px rgba(0,0,0,0.1)',
                textAlign: 'left',
                transition: 'all 0.3s ease',
                '&:hover': { boxShadow: '0px 6px 15px rgba(0,0,0,0.15)' }
              }}
            >
              <CardContent>
                <Image src={feature.icon} alt={feature.title} width={40} height={40} style={{ marginBottom: 16 }} />
                <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1, color: '#00004F', fontSize: { xs: '1rem', md: '1.15rem' } }}>
                  {feature.title}
                </Typography>
                <Typography variant="body2" color="textSecondary" sx={{ fontSize: { xs: '1rem', md: '0.95rem' } }}>
                  {feature.description}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );
}
