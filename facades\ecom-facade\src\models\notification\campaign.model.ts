import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

export enum NotificationType {
  EMAIL = 'email',
  PUSH = 'push',
}

@model({name: 'campaigns'})
export class Campaign extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'number',
    id: true,
  })
  type?: number;

  @property({
    type: 'string',
    required: true,
  })
  subject: string;

  @property({
    type: 'string',
    default: '',
    name: 'click_action',
  })
  clickAction?: string;

  @property({
    type: 'string',
    default: '',
    name: 'body',
  })
  body?: string;

  @property({
    type: 'boolean',
    default: false,
    name: 'isDraft',
  })
  isDraft?: boolean;

  @property({
    type: 'string',
    default: '',
    name: 'sent',
  })
  sent?: string;

  @property({
    type: 'string',
    required: true,
    name: 'group_id',
  })
  groupId: string;

  constructor(data?: Partial<Campaign>) {
    super(data);
  }
}

export interface CampaignRelations {
  // describe navigational properties here
}
