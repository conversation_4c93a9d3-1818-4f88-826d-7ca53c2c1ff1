'use client';

import { useState, SyntheticEvent, useMemo } from 'react';
import { Tab, Tabs, Grid, Stack, Divider, Typography, Box } from '@mui/material';
import { convertFromRaw } from 'draft-js';
import Image from 'next/image';

import MainCard from 'components/MainCard';
import CircularLoader from 'components/CircularLoader';

import { ProductDisclaimer, ProductTabsProps } from 'types/product';
import { useGetproductByIdQuery } from 'redux/app/products/productApiSlice';
import { customIncludes } from 'constants/product';

import ProductImages from './product-details/ProductImages';
import ProductInfo from './product-details/ProductInfo';
import ProductSpecifications from './product-details/ProductSpecifications';
import ProductDetails from './product-details/ProductDetails';
import ProductVariantsList from './product-details/ProductVariantsList';
import ProductCustomizationFieldsTable from './product-details/ProductCustomizationFieldsTable';

function TabPanel({ children, value, index, ...other }: ProductTabsProps) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`product-details-tabpanel-${index}`}
      aria-labelledby={`product-details-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `product-details-tab-${index}`,
    'aria-controls': `product-details-tabpanel-${index}`
  };
}

type Props = {
  id: string;
};

export default function ProductView({ id }: Props) {
  const {
    data: product,
    isLoading,
    isFetching
  } = useGetproductByIdQuery(
    { id, filter: { include: customIncludes } },
    {
      refetchOnMountOrArgChange: true
    }
  );

  const [value, setValue] = useState(0);
  const handleChange = (event: SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  const renderRichText = (rawContent?: string) => {
    if (!rawContent) return '';
    try {
      const contentState = convertFromRaw(JSON.parse(rawContent));
      return contentState.getPlainText();
    } catch (err) {
      console.log(err);
      return rawContent;
    }
  };

  const productImages = useMemo(() => <ProductImages product={product!} />, [product]);
  const pinnedVariant = useMemo(() => product?.productVariants?.find((v) => v.isPinned), [product]);

  const loader = (
    <Box sx={{ height: 504 }}>
      <CircularLoader />
    </Box>
  );

  const isLoader = isLoading || isFetching || product === null;

  return (
    <Grid container spacing={1}>
      <Grid item xs={12}>
        {isLoader ? (
          <MainCard>{loader}</MainCard>
        ) : (
          <Grid container spacing={3}>
            <Grid item xs={12} sm={8} md={5} lg={4}>
              <Stack spacing={2}>
                {productImages}
                <Box
                  sx={{
                    bgcolor: 'secondary.lighter',
                    p: 2,
                    borderRadius: 1,
                    boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
                    height: 200,
                    overflow: 'auto'
                  }}
                >
                  <ProductInfo product={product!} />
                </Box>
              </Stack>
            </Grid>

            <Grid item xs={12} md={7} lg={8}>
              <MainCard sx={{ maxHeight: '100%', display: 'flex', flexDirection: 'column' }}>
                <Stack spacing={3} sx={{ flex: 1, overflow: 'hidden' }}>
                  {/* Pinned Variant Display */}
                  {pinnedVariant && (
                    <Box
                      sx={{
                        p: 2,
                        mt: 1,
                        mx: 2,
                        borderRadius: 2,
                        backgroundColor: 'primary.lighter',
                        border: '1px dashed',
                        borderColor: 'primary.main'
                      }}
                    >
                      <Typography variant="h6" color="primary" gutterBottom>
                        📌 Pinned Variant
                      </Typography>
                      <Grid container spacing={2} alignItems="center">
                        <Grid item>
                          {pinnedVariant.featuredAsset?.previewUrl ? (
                            <Image
                              src={pinnedVariant.featuredAsset.previewUrl}
                              alt={pinnedVariant.name}
                              width={60}
                              height={60}
                              style={{ objectFit: 'cover', borderRadius: 8 }}
                            />
                          ) : (
                            <Box
                              sx={{
                                width: 60,
                                height: 60,
                                bgcolor: 'grey.200',
                                borderRadius: 1
                              }}
                            />
                          )}
                        </Grid>
                        <Grid item xs>
                          <Typography variant="subtitle1">{pinnedVariant.name}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            SKU: {pinnedVariant.sku}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Box>
                  )}

                  {/* Tabs */}
                  <Box>
                    <Tabs
                      value={value}
                      indicatorColor="primary"
                      onChange={handleChange}
                      aria-label="product description tabs"
                      variant="scrollable"
                      sx={{
                        '& .MuiTab-root': {
                          minWidth: 'auto',
                          px: 2,
                          py: 1,
                          fontSize: '0.875rem',
                          fontWeight: 500,
                          textTransform: 'none'
                        }
                      }}
                    >
                      <Tab label="Details" {...a11yProps(0)} />
                      <Tab label="Specifications" {...a11yProps(1)} />
                      <Tab label="Return Policy" {...a11yProps(2)} />
                      <Tab label="Terms & Conditions" {...a11yProps(3)} />
                      <Tab label="Disclaimers" {...a11yProps(4)} />
                      <Tab label="Variants" {...a11yProps(5)} />
                      <Tab label="Customisations" {...a11yProps(6)} />
                    </Tabs>
                    <Divider />
                  </Box>

                  {/* Tab Panels */}
                  <Box
                    sx={{
                      flex: 1,
                      overflow: 'auto',
                      pr: 1,
                      '&::-webkit-scrollbar': {
                        width: '6px'
                      },
                      '&::-webkit-scrollbar-thumb': {
                        backgroundColor: 'divider',
                        borderRadius: '3px'
                      }
                    }}
                  >
                    <TabPanel value={value} index={0}>
                      <ProductDetails product={product!} />
                    </TabPanel>
                    <TabPanel value={value} index={1}>
                      <ProductSpecifications product={product!} />
                    </TabPanel>
                    <TabPanel value={value} index={2}>
                      <Typography sx={{ whiteSpace: 'pre-line', lineHeight: 1.7 }}>
                        {product?.productReturnPolicy?.returnPolicy
                          ? renderRichText(product.productReturnPolicy.returnPolicy)
                          : 'No data found'}
                      </Typography>
                    </TabPanel>
                    <TabPanel value={value} index={3}>
                      <Typography sx={{ whiteSpace: 'pre-line', lineHeight: 1.7 }}>
                        {product?.productTermsAndCondition?.terms
                          ? renderRichText(product.productTermsAndCondition.terms)
                          : 'No data found'}
                      </Typography>
                    </TabPanel>
                    <TabPanel value={value} index={4}>
                      <Typography sx={{ whiteSpace: 'pre-line', lineHeight: 1.7 }}>
                        {product?.productDisclaimer && (product.productDisclaimer as ProductDisclaimer).disclaimer
                          ? renderRichText((product.productDisclaimer as ProductDisclaimer).disclaimer)
                          : 'No data found'}
                      </Typography>
                    </TabPanel>
                    <TabPanel value={value} index={5}>
                      <ProductVariantsList productVariants={product?.productVariants ?? []} />
                    </TabPanel>
                    <TabPanel value={value} index={6}>
                      <ProductCustomizationFieldsTable productCustomizationFields={product?.productCustomizationFields ?? []} />
                    </TabPanel>
                  </Box>
                </Stack>
              </MainCard>
            </Grid>
          </Grid>
        )}
      </Grid>
    </Grid>
  );
}
