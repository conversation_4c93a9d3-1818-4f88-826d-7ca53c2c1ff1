import {createSlice} from '@reduxjs/toolkit';
import {getItem, setItem} from '../mmkvStorage';
import {WritableDraft} from 'immer';

const intialState: {cartList: Array<{id: string; [key: string]: any}>} = {
  cartList: getItem('carList') || [],
};
const cartSlice = createSlice({
  name: 'cart',
  initialState: intialState, // or whatever your default is

  reducers: {
    // ✅ Replace the entire cart list
    setCartList: (state, action) => {
      state.cartList = action.payload;
      setItem('cartList', action.payload);
    },

    // ✅ Add or update a single item
    upsertCartItem: (state, action) => {
      const newItem = action.payload;
      const existingItem = state.cartList.find(item => item.id === newItem.id);

      if (existingItem) {
        Object.assign(existingItem, newItem);
      } else {
        state.cartList.push(newItem);
      }
    },

    // ✅ Add multiple items (merge style)
    upsertCartItems: (state, action) => {
      const newItems = action.payload;
      newItems.forEach(
        (newItem: WritableDraft<{[key: string]: any; id: string}>) => {
          const existingItem = state.cartList.find(
            item => item.id === newItem.id,
          );
          if (existingItem) {
            Object.assign(existingItem, newItem);
          } else {
            state.cartList.push(newItem);
          }
        },
      );
    },

    // ✅ Remove an item by ID
    removeCartItem: (state, action) => {
      const idToRemove = action.payload;
      state.cartList = state.cartList.filter(item => item.id !== idToRemove);
    },

    // ✅ Clear the entire cart
    clearCart: state => {
      state.cartList = [];
    },
  },
});
export const {clearCart, setCartList} = cartSlice.actions;
export default cartSlice.reducer;
