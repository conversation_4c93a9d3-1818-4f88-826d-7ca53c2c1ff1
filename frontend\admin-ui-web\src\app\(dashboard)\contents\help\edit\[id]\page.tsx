'use client';

import { Container, CircularProgress, Typography } from '@mui/material';
import { FaqVisibility } from 'enums/faq-visibility.enum';
import { FaqStatus } from 'enums/faq-status.enum';
import HelpCreate from 'views/help/HelpCreate';
import { useGetHelpByIdQuery } from 'redux/app/contents/help/helpApiSlice';

const EditHelpPage = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const { data: faqData, isLoading, error } = useGetHelpByIdQuery({ id });

  if (isLoading) {
    return <CircularProgress />;
  }

  if (error || !faqData) {
    return <Typography color="error">Failed to load FAQ</Typography>;
  }

  return (
    <Container maxWidth="md">
      <HelpCreate
        initialValues={{
          ...faqData,
          visibility: (faqData.visibility ?? FaqVisibility.ALL) as FaqVisibility,
          status: (faqData.status ?? FaqStatus.ACTIVE) as FaqStatus
        }}
        isEdit
        faqId={id}
      />
    </Container>
  );
};

export default EditHelpPage;
