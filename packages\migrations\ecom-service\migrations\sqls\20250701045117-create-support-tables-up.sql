/* Replace with your SQL commands */
CREATE TABLE IF NOT EXISTS main.supports (
    id              uuid DEFAULT md5(random()::text || clock_timestamp()::text)::uuid NOT NULL,
    support_email   varchar(255) NOT NULL,
    support_phone   varchar(20) NOT NULL,
    status          integer DEFAULT 0 NOT NULL, 
    visibility      integer DEFAULT 0 NOT NULL, 
    created_on      timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on     timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by      uuid,
    modified_by     uuid,
    deleted         boolean DEFAULT false NOT NULL,
    deleted_by      uuid,
    deleted_on      timestamptz,
    CONSTRAINT pk_supports_id PRIMARY KEY (id)
);

