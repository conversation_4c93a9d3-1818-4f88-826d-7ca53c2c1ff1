'use client';

// import { useEffect, useMemo, useState } from 'react';
// import { useTheme } from '@mui/material/styles';
// import Stack from '@mui/material/Stack';
// import Tooltip from '@mui/material/Tooltip';
// import Typography from '@mui/material/Typography';
// import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';
// import IconButton from 'components/@extended/IconButton';
// import { Eye, ArrowLeft, Add, Send, Send2 } from 'iconsax-react';
// import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from 'utils/table-filter';
// import Loader from 'components/Loader';
// import { NotificationType } from 'enums/notification-type.enum';
// import { Chip } from '@mui/material';
import withPermission from 'hoc/withPermission';
import { PermissionKeys } from 'enums/permission-keys.enum';
// import { ThemeMode } from 'config';
// import CampaignTable from './CampaignTable';
// import { useGetCampaignsCountQuery, useGetCampaignsQuery } from 'redux/app/campaigns/campaignApiSlice';
// import { Campaign } from 'types/campaign';
// import { EditorState } from 'draft-js';

// interface ApiCampaign {
//   id?: string;
//   name: string;
//   subject?: string;
//   body?: EditorState | string;
//   type?: number;
//   isDraft?: boolean;
// }

// const CampaignListPage = () => {
//   const theme = useTheme();

//   const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
//   const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
//   const [globalFilter, setGlobalFilter] = useState<string>('');
//   const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
//   const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null);

//   const {
//     data: campaignList,
//     isLoading: campaignListLoading,
//     refetch
//   } = useGetCampaignsQuery({
//     order: convertSortingToLoopbackSort(sorting),
//     where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['subject', 'name']),
//     ...convertPaginationToLoopback(pagination)
//   });

//   useEffect(() => {
//     refetch();
//   }, [refetch]);

//   const trasnformCampaignList = (list: ApiCampaign[] = []): Campaign[] => {
//     return list.map((item) => ({
//       id: item.id ?? '',
//       name: item.name,
//       subject: item.subject ?? '',
//       body: item.body ?? '',
//       type: item.type ?? 0,
//       isDraft: item.isDraft ?? false
//     }));
//   };

//   const { data: campaignCount, isLoading: campaignCountLoading } = useGetCampaignsCountQuery({
//     where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['subject', 'message']),
//     include: []
//   });

//   const columns = useMemo<ColumnDef<Campaign>[]>(
//     () => [
//       {
//         header: 'Name',
//         accessorKey: 'name',
//         enableSorting: true,
//         cell: ({ row }) => <Typography>{row.original?.subject ?? '-'}</Typography>
//       },
//       {
//         header: 'Subject',
//         accessorKey: 'subject',
//         enableSorting: true,
//         cell: ({ row }) => <Typography>{row.original?.subject ?? '-'}</Typography>
//       },
//       {
//         header: 'Notification Type',
//         accessorKey: 'notificationType',
//         cell: ({ row }) => {
//           const typeValue = row.original?.type as NotificationType;
//           const typeLabels: Record<number, string> = {
//             [NotificationType.PUSH]: 'Push Notification',
//             [NotificationType.EMAIL]: 'Email',
//             [NotificationType.SMS]: 'SMS'
//           };
//           return <Typography>{typeLabels[typeValue] ?? 'Unknown'}</Typography>;
//         }
//       },
//       {
//         header: 'Sent',
//         accessorKey: 'sent',
//         cell: ({ row }) => {
//           return (
//             <Chip
//               color={row.original.isDraft ? 'error' : 'success'}
//               label={row.original.isDraft ? 'Draft' : 'Sent'}
//               size="small"
//               variant="outlined"
//             />
//           );
//         }
//       },
//       {
//         header: 'Actions',
//         meta: {
//           className: 'cell-center'
//         },
//         enableSorting: false,
//         cell: ({ row }) => {
//           const collapseIcon =
//             row.getCanExpand() && row.getIsExpanded() ? (
//               <Add
//                 style={{
//                   color: theme.palette.error.main,
//                   transform: 'rotate(45deg)'
//                 }}
//               />
//             ) : (
//               <Eye />
//             );

//           const collapseSendIcon = row.getCanExpand() && row.getIsExpanded() ? <Send2 /> : <Send />;

//           return (
//             <Stack direction="row" alignItems="center" justifyContent="center" spacing={0}>
//               <Tooltip title="View">
//                 <IconButton
//                   color="secondary"
//                   onClick={row.getToggleExpandedHandler()}
//                   sx={{
//                     color: theme.palette.primary.light,
//                     ':hover': {
//                       backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
//                     }
//                   }}
//                 >
//                   {collapseIcon}
//                 </IconButton>
//               </Tooltip>
//               <Tooltip title="Send Campaign">
//                 <IconButton
//                   color="secondary"
//                   onClick={row.getToggleExpandedHandler()}
//                   disabled={!row.original.isDraft}
//                   sx={{
//                     color: theme.palette.primary.light,
//                     ':hover': {
//                       backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
//                     }
//                   }}
//                 >
//                   {collapseSendIcon}
//                 </IconButton>
//               </Tooltip>
//             </Stack>
//           );
//         }
//       }
//     ],
//     [theme]
//   );

//   return (
//     <>
//       {selectedCampaign ? (
//         <>
//           <IconButton onClick={() => setSelectedCampaign(null)} sx={{ mb: 2 }}>
//             <ArrowLeft />
//           </IconButton>
//         </>
//       ) : campaignCountLoading || campaignListLoading ? (
//         <Loader />
//       ) : (
//         <CampaignTable
//           {...{
//             data: trasnformCampaignList(campaignList || []),
//             columns,
//             setSorting,
//             sorting,
//             columnFilters,
//             setColumnFilters,
//             loading: campaignListLoading,
//             globalFilter,
//             setGlobalFilter,
//             pagination,
//             setPagination,
//             totalRows: campaignCount?.count ?? 0,
//             refetch
//           }}
//         />
//       )}
//     </>
//   );
// };

const CampaignListPage = () => {
  return <>campaign List</>;
};

export default withPermission(PermissionKeys.ViewNotification)(CampaignListPage);
