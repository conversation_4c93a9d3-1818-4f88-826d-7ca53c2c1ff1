/** @type {import('next').NextConfig} */
const nextConfig = {
  modularizeImports: {
    '@mui/material': {
      transform: '@mui/material/{{member}}',
    },
    '@mui/lab': {
      transform: '@mui/lab/{{member}}',
    },
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'flagcdn.com',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: '**.ecomdukes.in',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'assets.ecomdukes.in',
        pathname: '/**', // allows all paths
      },
    ],
  },
  env: {
    NEXT_APP_VERSION: 'v2.0.0',
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    NEXTAUTH_URL: process.env.NEXT_AUTH_URL,
    NEXT_APP_JWT_SECRET: process.env.NEXT_APP_JWT_SECRET,
    NEXT_APP_JWT_TIMEOUT: '86400',
    NEXTAUTH_SECRET_KEY: process.env.NEXTAUTH_SECRET,
    NEXT_PUBLIC_AUTH_SERVICE_URL:
      process.env.NEXT_PUBLIC_AUTH_SERVICE_URL ??
      'https://api.auth-service.ecomdukes.in',
    NEXT_PUBLIC_GOOGLE_CLIENT_ID:
      process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID ?? 'google_seller_client',
    NEXT_PUBLIC_GOOGLE_CLIENT_SECRET:
      process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET ??
      '294057929665-4irbsji58pl7qslijbc5k9j2791hfbmp',
    NEXT_PUBLIC_GOOGLE_CALLBACK_URL:
      process.env.NEXT_PUBLIC_GOOGLE_CALLBACK_URL,
    NEXT_PUBLIC_LOCAL_CLIENT_ID:
      process.env.NEXT_PUBLIC_LOCAL_CLIENT_ID ?? 'email_password_client',
    NEXT_PUBLIC_LOCAL_CLIENT_SECRET:
      process.env.NEXT_PUBLIC_LOCAL_CLIENT_SECRET ??
      '6df2ad849a05d30804bac2d965646aba1538473536174101b5fd0c65b07c93447fc1b5f0ba0b7dba',
    NEXT_PUBLIC_ECOM_API_SERVICE_URL:
      process.env.NEXT_PUBLIC_ECOM_API_SERVICE_URL ??
      'https://api.ecom-service.ecomdukes.in',
    NEXT_PUBLIC_ECOM_FACADE_URL:
      process.env.NEXT_PUBLIC_ECOM_FACADE_URL ??
      'https://api.ecom-facade.ecomdukes.in/',
    NEXT_PUBLIC_CUSTOMER_UI_URL:
      process.env.NEXT_PUBLIC_CUSTOMER_UI_URL ?? 'https://demo.ecomdukes.in',
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Forwarded-Proto',
            value: 'https',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
