import React, {useCallback, useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import {Share} from 'react-native';
import {ActivityIndicator, Divider, Icon, IconButton} from 'react-native-paper';
import CustomButton from '../../../components/CustomButton/CustomButton';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';
import DeliveryDetails from './components/DeliveryDetails/DeliveryDetails';
import {
  RouteProp,
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import {SCREEN_NAME} from '../../../constants/screenNames';
import {
  OnboardStackParamList,
  OrderConfirmScreenNavigationProp,
} from '../../../navigations/types';
import {fieldsExcludeMetaFields} from '../../../types/filter';
import {
  useGetProductVariantByIdQuery,
  useLazyGetProductCustomizationsQuery,
} from '../../../redux/product/productApiSlice';
import ProductCustomizationForm, {
  CustomizationField,
} from '../Products/Components/ProductCustomization';
import {useAddItemToCartMutation} from '../../../redux/cart/cartApiSlice';
import Toast from 'react-native-toast-message';
import {CartItem} from '../../../types/cartApi';
import {ProductCustomField, ReviewStatus} from '../../../redux/product/product';
import {
  useAddItemToWishlistMutation,
  useRemoveItemFromWishlistMutation,
} from '../../../redux/wishlist/wishlistApiSlice';
import {useGetUserQuery} from '../../../redux/auth/authApiSlice';
import {useTypedSelector} from '../../../redux/store';
import ImageSwiper from './components/ImageSwiper/ImageSwiper';
import ReviewCard from './components/ReviewCard';
import {Images} from '../../../assets/images';
import DetailSection from './components/DetailSection';
import ExpandableText from './components/ExpandableTextSection';
import ProductInfoTabs from './components/ProductInfoTab';
import TextModal from './components/TextModal';
import {getModalContent, infoList} from '../../../constants/productInfoHelpers';
import SellerInfoCard from './components/SellerInfoCard';
type ProductRouteProp = RouteProp<OnboardStackParamList, 'products'>;
type Props = {
  fields: ProductCustomField[];
  productVariantId: string;
  isInCart?: boolean;
  cartId?: string;
};
const ProductDetailScreen: React.FC<Props> = () => {
  const navigation = useNavigation<OrderConfirmScreenNavigationProp>();
  const route = useRoute<ProductRouteProp>();
  const {productId} = route.params;
  const [addItemToWishlist] = useAddItemToWishlistMutation();
  const [removeItemFromWishlist] = useRemoveItemFromWishlistMutation();
  const [isAddedToCart, setIsAddedToCart] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [showModal, setShowModal] = useState(false);

  const [selectedModalType, setSelectedModalType] = useState<string | null>(
    null,
  );

  const {isLoggedIn} = useTypedSelector(state => state.auth);

  const [, setSelectedCustomizations] = useState<
    {customizationFieldId: string; value: any}[]
  >([]);

  const handleCartAction = () => {
    if (isAddedToCart) {
      navigation.navigate('mainHome', {screen: SCREEN_NAME.CART});
    } else {
      setIsAddedToCart(true);
    }
  };
  const [addItemToCart] = useAddItemToCartMutation();
  const [buttonLabel, setButtonLabel] = useState('ADD TO CART');
  const handleCustomizationSubmit = (values: any) => {
    const customizationValue = customizationFormFields.map(field => ({
      customizationFieldId: field.id,
      value: values[field.name],
    }));

    setSelectedCustomizations(customizationValue);
    handleAddToCart(customizationValue);
  };

  const [addedToCartIds, setAddedToCartIds] = useState<Set<string>>(new Set());
  const handleAddToCart = async (customizationValues?: any) => {
    setButtonLabel('GO TO CART');
    if (addedToCartIds.has(productId)) {
      return handleCartAction();
    }

    const cartItem: Partial<CartItem> = {
      productVariantId: productId,
      quantity: 1,
      ...(customizationValues ? {customizationValues} : []),
    };
    await addItemToCart(cartItem).unwrap();
    Toast.show({type: 'success', text1: 'Added to cart!'});
    setAddedToCartIds(prev => new Set(prev).add(productId));
    handleCartAction();
  };
  const {data: user} = useGetUserQuery();

  const {
    data: productVariant,
    isLoading: productLoading,
    refetch,
  } = useGetProductVariantByIdQuery({
    id: productId,
    filter: {
      fields: fieldsExcludeMetaFields,
      include: [
        {
          relation: 'productVariantPrice',
          scope: {
            fields: fieldsExcludeMetaFields,
          },
        },
        {
          relation: 'reviews',
          scope: {
            fields: fieldsExcludeMetaFields,
            where: {
              deleted: false,
              status: ReviewStatus.APPROVED,
            },
            include: [
              {
                relation: 'customer',
              },
            ],
          },
        },
        {
          relation: 'productVariantOptions',
          scope: {
            fields: fieldsExcludeMetaFields,
            include: [
              {
                relation: 'productOption',
                scope: {
                  fields: fieldsExcludeMetaFields,
                  include: [
                    {
                      relation: 'productOptionGroup',
                      scope: {
                        fields: fieldsExcludeMetaFields,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          relation: 'productVariantAssets',
          scope: {
            fields: fieldsExcludeMetaFields,
            include: [
              {
                relation: 'asset',
                scope: {
                  fields: fieldsExcludeMetaFields,
                },
              },
            ],
          },
        },
        {
          relation: 'featuredAsset',
          scope: {
            fields: fieldsExcludeMetaFields,
          },
        },
        {
          relation: 'productSpecifications',
          scope: {
            fields: fieldsExcludeMetaFields,
          },
        },
        {
          relation: 'productCustomizationFields',
          scope: {
            fields: fieldsExcludeMetaFields,
          },
        },
        {
          relation: 'productBoxContents',
          scope: {
            fields: fieldsExcludeMetaFields,
          },
        },
        {
          relation: 'productDetail',
          scope: {
            fields: fieldsExcludeMetaFields,
          },
        },
        {
          relation: 'productUniqueness',
          scope: {
            fields: fieldsExcludeMetaFields,
          },
        },
        {
          relation: 'productSuitability',
          scope: {
            fields: fieldsExcludeMetaFields,
          },
        },
        {
          relation: 'productMoreInfo',
          scope: {
            fields: fieldsExcludeMetaFields,
          },
        },
        {
          relation: 'productReturnPolicy',
          scope: {
            fields: fieldsExcludeMetaFields,
          },
        },
        {
          relation: 'productTermsAndCondition',
          scope: {
            fields: fieldsExcludeMetaFields,
          },
        },
        {
          relation: 'productDisclaimer',
          scope: {
            fields: fieldsExcludeMetaFields,
          },
        },
        {
          relation: 'productPersonalWork',
          scope: {
            fields: fieldsExcludeMetaFields,
          },
        },
        ...(isLoggedIn
          ? [
              {
                relation: 'wishlist',
                scope: {
                  where: {
                    deleted: false,
                    customerId: user?.profileId,
                  },
                  fields: {id: true},
                },
              },
            ]
          : []),
        {
          relation: 'product',
          scope: {
            fields: fieldsExcludeMetaFields,
            include: [
              {relation: 'seller'},
              {
                relation: 'productVariants',
                scope: {
                  fields: fieldsExcludeMetaFields,
                },
              },
              {
                relation: 'productDetail',
                scope: {
                  fields: fieldsExcludeMetaFields,
                },
              },
              {
                relation: 'productReturnPolicy',
                scope: {
                  fields: fieldsExcludeMetaFields,
                },
              },
              {
                relation: 'productMoreInfo',
                scope: {
                  fields: fieldsExcludeMetaFields,
                },
              },
              {
                relation: 'productTermsAndCondition',
                scope: {
                  fields: fieldsExcludeMetaFields,
                },
              },
              {
                relation: 'productSpecifications',
                scope: {
                  fields: fieldsExcludeMetaFields,
                },
              },
              {
                relation: 'productBoxContents',
                scope: {
                  fields: fieldsExcludeMetaFields,
                },
              },
              {
                relation: 'productUniqueness',
                scope: {
                  fields: fieldsExcludeMetaFields,
                },
              },
              {
                relation: 'productSuitability',
                scope: {
                  fields: fieldsExcludeMetaFields,
                },
              },
              {
                relation: 'productDisclaimer',
                scope: {
                  fields: fieldsExcludeMetaFields,
                },
              },
              {
                relation: 'productCustomizationFields',
                scope: {
                  fields: fieldsExcludeMetaFields,
                },
              },
              {
                relation: 'productPersonalWork',
                scope: {
                  fields: fieldsExcludeMetaFields,
                },
              },
            ],
          },
        },
      ],
    },
  });
  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch]),
  );

  const previewUrls =
    (productVariant?.productVariantAssets ?? []).length > 0
      ? productVariant?.productVariantAssets
          .map(item => item.asset?.previewUrl)
          .filter(
            (url): url is string =>
              typeof url === 'string' && url.trim() !== '',
          )
      : productVariant?.featuredAsset?.previewUrl
      ? [productVariant.featuredAsset.previewUrl]
      : [''];

  const [isWishlisted, setIsWishlisted] = useState(
    productVariant?.wishlist?.id ? true : false,
  );
  const [wishlistId, setWishlistId] = useState<string | undefined>(
    productVariant?.wishlist?.id,
  );

  // eslint-disable-next-line @typescript-eslint/no-shadow
  const handleToggleWishlist = async (productId: string) => {
    if (isWishlisted && wishlistId) {
      await removeItemFromWishlist(wishlistId).unwrap();
      setIsWishlisted(false);
      setWishlistId(undefined);
      Toast.show({text1: 'Removed from wishlist', type: 'info'});
    } else {
      const result = await addItemToWishlist(productId).unwrap();
      Toast.show({text1: 'Added to wishlist', type: 'success'});
      setIsWishlisted(true);
      setWishlistId(result.id);
    }
  };
  useEffect(() => {
    if (productVariant?.wishlist?.id) {
      setIsWishlisted(true);
      setWishlistId(productVariant.wishlist.id);
    } else {
      setIsWishlisted(false);
      setWishlistId(undefined);
    }
  }, [productVariant?.wishlist?.id]);
  const [triggerGetCustomizations, {data: customizationFields}] =
    useLazyGetProductCustomizationsQuery();

  // Step 1: Fetch variant-specific customizations
  useEffect(() => {
    if (!productVariant?.id) {
      return;
    }

    const fetchCustomizationFields = async () => {
      const res = await triggerGetCustomizations({
        where: {
          productVariantId: productVariant.id,
        },
        include: [
          {
            relation: 'productCustomizationOptions',
            scope: {
              fields: fieldsExcludeMetaFields,
            },
          },
        ],
      }).unwrap();

      if (!res.length && productVariant.product?.id) {
        await triggerGetCustomizations({
          where: {
            productId: productVariant.product.id,
          },
          include: [
            {
              relation: 'productCustomizationOptions',
              scope: {
                fields: fieldsExcludeMetaFields,
              },
            },
          ],
        });
      }
    };

    fetchCustomizationFields();
  }, [productVariant, triggerGetCustomizations]);
  const customizationFormFields: CustomizationField[] = (
    customizationFields || []
  ).map(field => ({
    ...field,
    fieldType: field.fieldType as CustomizationField['fieldType'],
  }));
  const price = parseFloat(productVariant?.productVariantPrice.price || '0');
  const mrp = parseFloat(productVariant?.productVariantPrice.mrp || '0');

  let discountPercentage = 0;

  if (mrp > 0 && price > 0 && mrp > price) {
    discountPercentage = Math.round(((mrp - price) / mrp) * 100);
  }
  const rating = Number(productVariant?.reviews?.[0]?.rating ?? 0);

  const mappedReviews = productVariant?.reviews
    ?.filter(r => !r.deleted)
    .map(r => {
      const firstName = r?.customer?.userTenant?.user?.firstName || '';
      const lastName = r?.customer?.userTenant?.user?.lastName || '';
      return {
        id: r.id,
        name: `${firstName} ${lastName}`.trim(),
        avatar: Images.men,
        isVerified: true,
        rating: r.rating,
        review: r.review,
        images: Array.isArray(r.previewAssets) ? r.previewAssets : [],
      };
    });
  const productVariantId = productVariant?.id;
  const productName = productVariant?.product.slug ?? 'Product Name';
  const productLink = `https://demo.ecomdukes.in/product-details/${productVariantId}`;

  const shareProduct = async () => {
    const shareMessage = `Check out this product: ${productName}\n${productLink}`;

    const shareOptions = {
      title: 'Share Product',
      message: shareMessage,
      url: productVariant?.featuredAsset.previewUrl || '',
      failOnCancel: false,
    };

    await Share.share(shareOptions);
  };
  const badgeText = getFirstTextFromDraftJS(
    productVariant?.productPersonalWork?.workLevel ||
      productVariant?.product?.productPersonalWorks?.[0]?.workLevel,
  );

  function getFirstTextFromDraftJS(workLevelJson: any): string {
    if (!workLevelJson?.blocks) {
      return '';
    }
    return (
      workLevelJson.blocks.find((b: any) => b.text.trim() !== '')?.text || ''
    );
  }
  return (
    <>
      <ScrollView contentContainerStyle={styles.container}>
        {productLoading && <ActivityIndicator size={20} />}
        <View>
          <ImageSwiper
            images={(previewUrls || []).filter((url): url is string => !!url)}
          />

          <IconButton
            icon="heart"
            onPress={() =>
              productVariant && handleToggleWishlist(productVariant.id)
            }
            iconColor={isWishlisted ? colors.favourites : colors.gray.medium}
            size={20}
            style={styles.favoriteIcon}
          />
        </View>

        <View style={styles.shareContainer}>
          <Text style={styles.stockText}>20+ Left</Text>
          <View style={styles.shareText}>
            <TouchableOpacity onPress={shareProduct}>
              <Text>Share</Text>
              <IconButton icon="share" size={25} />
            </TouchableOpacity>
          </View>
        </View>
        <Text style={styles.title}>{productVariant?.name}</Text>

        {productVariant?.product?.description && (
          <>
            <Text
              numberOfLines={expanded ? undefined : 3}
              style={[styles.review]}>
              {productVariant.product.description}
            </Text>

            {productVariant.product.description.length > 100 && (
              <TouchableOpacity onPress={() => setExpanded(!expanded)}>
                <Text style={styles.seeMoreView}>
                  {expanded ? 'See Less' : 'See More'}
                </Text>
              </TouchableOpacity>
            )}
          </>
        )}

        <View style={styles.priceSection}>
          <Text style={styles.currentPrice}>
            {productVariant?.productVariantPrice?.price}
          </Text>
          <Text style={styles.mrp}>
            M.R.P :{productVariant?.productVariantPrice?.mrp}
          </Text>
          <Text style={styles.deal}>Deal : {discountPercentage}%</Text>
        </View>

        {productVariant?.reviews?.length ? (
          <View style={styles.ratingContainer}>
            {[...Array(5)].map((_, i) => (
              <Icon
                key={i}
                source={i < rating ? 'star' : 'star-outline'}
                size={20}
                color={colors.tertiary}
              />
            ))}
          </View>
        ) : null}

        <View style={styles.countryText}>
          <Text style={styles.originText}>
            Country Of Origin : <Text style={styles.bold}>India</Text>
          </Text>
          <Text style={styles.originText}>
            ({productVariant?.reviews?.length ?? 0} Ratings &{' '}
            {productVariant?.reviews?.length ?? 0} Reviews )
          </Text>
        </View>

        {/* <View style={styles.pincodeContainer}>
          <Icon
            source="map-marker-radius-outline"
            size={20}
            color={colors.tertiary}
          />
          <TextInput
            style={styles.pincodeInput}
            placeholder="Enter Delivery Pincode"
          />
          <Button mode="text" onPress={() => {}}>
            Check
          </Button>
        </View> */}

        <View style={styles.deliveryRow}>
          <Text>
            Delivery: <Text style={styles.green}>Free</Text>
          </Text>
          <Text>COD: Available</Text>
          <TouchableOpacity onPress={() => setShowModal(true)}>
            <Text>
              Refund Policy <Icon source="information-outline" size={18} />
            </Text>
          </TouchableOpacity>
        </View>
        <TextModal
          visible={showModal}
          onClose={() => setShowModal(false)}
          title="Refund Policy"
          content={'Refund Policy...'}
          fallback="No policy available."
        />

        <Divider style={styles.divider} />
        <DeliveryDetails
          isGiftWrapAvailable={
            productVariant?.product?.isGiftWrapAvailable || false
          }
          giftWrapCharge={productVariant?.product?.isGiftWrapCharge || ''}
        />
        {customizationFields && customizationFields.length > 0 && (
          <ProductCustomizationForm
            fields={customizationFormFields}
            onSubmit={handleCustomizationSubmit}
            submitButtonTitle={buttonLabel}
            submitButtonMode="step"
            onSecondSubmit={handleCartAction}
          />
        )}
        <ReviewCard reviews={mappedReviews || []} />
        <ProductInfoTabs
          tabContent={{
            Specifications: (
              <DetailSection
                details={productVariant?.productSpecifications || []}
                title="Specifications"
              />
            ),
            'Box Content': (
              <DetailSection
                details={(productVariant?.productBoxContents || []).map(
                  content => ({
                    name: content.itemName || '',
                    value: String(content.quantity || ''),
                  }),
                )}
                title="Box Content"
              />
            ),
            Details: (
              <ExpandableText
                title="Details"
                content={productVariant?.productDetail?.details}
                fallback="No terms available."
                previewCharLimit={300}
              />
            ),
            'More Info': (
              <ExpandableText
                title="More Information"
                content={productVariant?.productMoreInfo?.info}
                fallback="No Info available."
                previewCharLimit={300}
              />
            ),
          }}
        />
        {infoList.map(item => (
          <TouchableOpacity
            key={item.type}
            onPress={() => setSelectedModalType(item.type)}
            style={styles.item}>
            <Icon source="chevron-right" size={20} color={colors.tertiary} />
            <Text style={styles.label}>{item.label}</Text>
            <Icon
              source="information-outline"
              size={20}
              color={colors.gray.medium}
            />
          </TouchableOpacity>
        ))}
        {selectedModalType && (
          <TextModal
            visible={!!selectedModalType}
            onClose={() => setSelectedModalType(null)}
            {...getModalContent(selectedModalType, productVariant)}
          />
        )}
        <SellerInfoCard
          sellerName={
            productVariant?.seller?.userTenant.user.firstName +
            ' ' +
            productVariant?.seller?.userTenant.user.lastName
          }
          firstName={productVariant?.seller?.userTenant.user.firstName}
          lastName={productVariant?.seller?.userTenant.user.lastName}
          role="Owner"
          soldCount={33}
          profileImageUrl={Images.men}
          badgeText={badgeText}
          onMessagePress={() =>
            navigation.navigate(SCREEN_NAME.MESSAGE, {
              sellerId: productVariant?.product?.sellerId ?? '',
            })
          }
        />
      </ScrollView>
      <View style={styles.buttonRow}>
        <CustomButton title={'BUY NOW'} buttonStyle={styles.button} />
        <CustomButton
          buttonColor={colors.tertiary}
          buttonStyle={styles.button}
          onPress={() => handleAddToCart()}
          title={isAddedToCart ? 'GO TO CART' : 'ADD TO CART'}
        />
      </View>
    </>
  );
};

export default ProductDetailScreen;

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: customColors.white,
    paddingBottom: 120,
  },

  productImage: {
    width: '100%',
    height: 250,
    borderRadius: 8,
    backgroundColor: colors.gray.light,
    objectFit: 'contain',
  },
  favoriteIcon: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: customColors.white,
  },
  editIcon: {
    position: 'absolute',
    bottom: 35,
    right: 10,
    backgroundColor: customColors.white,
  },

  stockText: {
    color: customColors.primaryContainer,
    marginBottom: 4,
    marginTop: 10,
  },
  title: {
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 8,
  },
  review: {
    color: colors.gray.dark,
    marginBottom: 12,
  },
  priceSection: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  currentPrice: {
    fontWeight: 'bold',
    fontSize: 18,
    color: customColors.textBlack,
  },
  mrp: {
    textDecorationLine: 'line-through',
    color: colors.gray.medium,
  },
  deal: {
    color: customColors.primaryContainer,
    fontSize: 13,
  },
  ratingContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  originText: {
    fontSize: 12,
    color: colors.gray.dark,
    marginBottom: 12,
  },
  bold: {
    fontWeight: 'bold',
  },
  pincodeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.gray.medium,
    borderRadius: 20,
    paddingHorizontal: 8,
    marginTop: 10,
  },
  pincodeInput: {
    flex: 1,
    marginLeft: 4,
    paddingVertical: 6,
  },
  deliveryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  green: {
    color: colors.green.success,
  },
  buttonRow: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: customColors.white,
    flexDirection: 'row',
    justifyContent: 'space-around',
    padding: 16,
    marginBottom: 10,
    borderTopWidth: 1,
    borderTopColor: colors.gray.light,
    zIndex: 999,
  },
  shareContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  shareText: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  button: {width: 150},
  countryText: {
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
  },
  divider: {
    height: 1,
    backgroundColor: colors.gray.light,
    marginTop: 10,
  },
  seeMoreView: {
    color: customColors.primaryContainer,
    fontWeight: 'bold',
    marginTop: 4,
  },
  item: {
    flexDirection: 'row',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray.light,
    backgroundColor: customColors.white,
    alignItems: 'center',
    gap: 10,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.tertiary,
  },
});
