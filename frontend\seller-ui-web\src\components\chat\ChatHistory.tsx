'use client';

import { useEffect, useRef } from 'react';
import { Avatar, Box, Stack, Typography, useTheme } from '@mui/material';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { ChatMessage } from 'redux/app/types/chat';
import { User } from 'types/user-profile';
import { SenderType } from 'types/chat';

dayjs.extend(relativeTime);

interface ChatHistoryProps {
  user: User;
  messages: ChatMessage[];
}

export default function ChatHistory({ user, messages }: ChatHistoryProps) {
  const bottomRef = useRef<HTMLDivElement>(null);
  const theme = useTheme();

  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: 'smooth', block: 'end' });
  }, [messages]);

  return (
    <Stack spacing={2}>
      {messages.map((msg, idx) => {
        const isCustomer = msg.senderType === SenderType.CUSTOMER;
        const isSeller = !isCustomer;

        const sender = msg.sender || {};
        const initials = `${sender?.firstName?.[0] ?? ''}${sender?.lastName?.[0] ?? ''}`.toUpperCase();
        const avatarUrl = sender?.presignedPhotoUrl || sender?.photoUrl || '';
        const time = dayjs(msg.createdOn || msg.modifiedOn).format('hh:mm A');

        return (
          <Box key={msg.id || idx} display="flex" justifyContent={isSeller ? 'flex-end' : 'flex-start'}>
            <Stack
              direction="row"
              spacing={1}
              alignItems="flex-end"
              justifyContent={isSeller ? 'flex-end' : 'flex-start'}
              sx={{ maxWidth: '75%' }}
            >
              {!isSeller && (
                <Avatar src={avatarUrl || undefined} sx={{ width: 32, height: 32, fontSize: '0.875rem' }}>
                  {!avatarUrl && initials}
                </Avatar>
              )}

              <Stack spacing={0.5} alignItems={isSeller ? 'flex-end' : 'flex-start'}>
                <Box
                  sx={{
                    px: 2,
                    py: 1,
                    bgcolor: isSeller ? theme.palette.primary.main : 'grey.100',
                    color: isSeller ? '#fff' : theme.palette.text.primary,
                    borderRadius: 2,
                    maxWidth: '100%'
                  }}
                >
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                    {msg.message}
                  </Typography>
                </Box>
                <Typography variant="caption" color="text.secondary">
                  {time}
                </Typography>
              </Stack>

              {isSeller && (
                <Avatar src={avatarUrl || undefined} sx={{ width: 32, height: 32, fontSize: '0.875rem' }}>
                  {!avatarUrl && initials}
                </Avatar>
              )}
            </Stack>
          </Box>
        );
      })}
      <Box ref={bottomRef} />
    </Stack>
  );
}
