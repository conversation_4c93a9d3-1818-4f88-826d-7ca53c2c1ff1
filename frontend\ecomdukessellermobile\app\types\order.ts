import {ProductVariant} from './product';
import {PromoCode} from './promo';

export interface Order {
  id: string;
  customerId: string;
  orderId?: string;
  orderReferenceId?: string;
  totalAmount: number;
  currency: string;
  status: string;
  cartId: string;
  shippingAddressId: string;
  billingAddressId: string;
  orderLineItems: OrderLineItem[];
  promoCodeId: string;
  promoCode: PromoCode;
  shippingAddress: Address;
  billingAddress: Address;
}
export interface OrderLineItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  orderId: string;
  productVariantId: string;
  warehouseId: string;
  status: string;
  rejectionReason?: string;
  productVariant: ProductVariant;
  order: Order;
  customizationValues?: CustomizationValue[];
}
export interface Address {
  id: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  addressType: string;
  customerId: string;
  locality: string;
  name: string;
  phoneNumber: string;
  landmark: string;
  alternativePhoneNumber: string;
}
export interface CustomizationField {
  id: string;
  name: string;
  label: string;
  placeholder: string;
  fieldType: 'text' | 'number' | 'textarea' | 'dropdown' | 'checkbox' | 'radio';
  isRequired: boolean;
}

export type CustomizationValue = {
  id?: string;
  value: string;
  cartId: string;
  customizationFieldId: string;
  orderLineItemId: string;
  customizationField: CustomizationField;
};
