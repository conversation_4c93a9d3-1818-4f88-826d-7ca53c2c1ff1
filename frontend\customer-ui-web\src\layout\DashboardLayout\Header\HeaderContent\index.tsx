// material-ui
import {Theme, useTheme} from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import Box from '@mui/material/Box';

// project-imports

import MobileSection from './MobileSection';

import {ThemeMode} from 'config';
import {
  Autocomplete,
  Avatar,
  Button,
  ButtonBase,
  CardContent,
  ClickAwayListener,
  Grid,
  InputAdornment,
  ListItemIcon,
  OutlinedInput,
  Paper,
  Popper,
  Stack,
  Typography,
} from '@mui/material';
import {Heart, Notification, SearchNormal1, ShoppingCart} from 'iconsax-react';
import Image from 'next/image';
import {
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {useRouter, useSearchParams} from 'next/navigation';
import {useLazyGetSearchSuggestionsQuery} from 'redux/search/searchApiSlice';
import {AutoCompleteOption} from 'types/shared';
import MainCard from 'components/MainCard';
import ProfileTab from './Profile/ProfileTab';
import Transitions from 'components/@extended/Transitions';
import {unsetCredentials} from 'redux/auth/authSlice';
import {useAppDispatch, useAppSelector} from 'redux/hooks';
import {ApiTagTypes} from 'redux/types';
import {apiSlice} from 'redux/apiSlice';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import AnimateButton from 'components/@extended/AnimateButton';
import {unsetMonitor} from 'redux/apimonitor/apiMonitorSlice';
import Link from 'next/link';

// ==============================|| HEADER - CONTENT ||============================== //
interface TabPanelProps {
  children?: ReactNode;
  dir?: string;
  index: number;
  value: number;
}

// tab panel wrapper
function TabPanel({children, value, index, ...other}: TabPanelProps) {
  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
      {...other}
      sx={{p: 1}}
    >
      {value === index && children}
    </Box>
  );
}

export default function HeaderContent() {
  const theme = useTheme();
  const router = useRouter();
  const [openDropdown, setOpenDropdown] = useState(false);
  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);
  const guestToken = useAppSelector(state => state.auth.guestToken);
  const downLG = useMediaQuery((theme: Theme) => theme.breakpoints.down('lg'));
  const search = useSearchParams();
  const {data: user, refetch} = useGetUserQuery(undefined, {
    skip: !isLoggedIn,
  });

  const [getSearchSuggestions, {data, isLoading}] =
    useLazyGetSearchSuggestionsQuery();
  const anchorRef = useRef<any>(null);
  const [open, setOpen] = useState(false);
  const dispatch = useAppDispatch();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const [searchQuery, setSearchQuery] = useState<string>(
    search.get('search') ?? '',
  );
  const onSubmitSearch = (searchQuery: string) => {
    if (searchQuery.trim() !== '') {
      router.push(`/listing?search=${searchQuery.trim()}`);
    }
  };
  useEffect(() => {
    if (isLoggedIn) {
      refetch();
    }
  }, [isLoggedIn, refetch]);

  const loadSearchSuggestions = useCallback(() => {
    if (searchQuery.trim() !== '') {
      getSearchSuggestions(searchQuery);
    }
  }, [searchQuery, getSearchSuggestions]);

  useEffect(() => {
    const debounceTimeout = setTimeout(loadSearchSuggestions, 300);
    return () => clearTimeout(debounceTimeout);
  }, [searchQuery]);

  const suggestions: AutoCompleteOption[] = useMemo(() => {
    if (!data) return [];
    return data.map(item => ({
      value: item.id,
      label: item.name.split(' ').slice(0, 4).join(' '),
    }));
  }, [data]);

  const handleLogout = async () => {
    dispatch(unsetCredentials());
    dispatch(unsetMonitor());
    dispatch(apiSlice.util.invalidateTags([ApiTagTypes.User]));
    router.push('/login');
  };
  const handleClose = (event: MouseEvent | TouchEvent) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }
    setOpen(false);
  };
  const [value] = useState(0);

  return (
    <>
      {!downLG && (
        <Box sx={{display: 'flex', alignItems: 'center', ml: 0}}>
          <Link href="/" passHref>
            <Image
              src="/assets/images/Logo without tagline.png"
              alt="icon logo"
              width={200}
              height={100}
              style={{cursor: 'pointer'}}
            />
          </Link>
        </Box>
      )}
      {!downLG && (
        <Autocomplete
          fullWidth
          freeSolo
          disableClearable
          open={openDropdown}
          onOpen={() => setOpenDropdown(true)}
          onClose={() => setOpenDropdown(false)}
          openOnFocus
          options={suggestions}
          value={searchQuery}
          loading={isLoading}
          onInputChange={(_, newInputValue) => {
            setSearchQuery(newInputValue);
          }}
          onChange={(_, newValue) => {
            const selectedValue =
              typeof newValue === 'string' ? newValue : newValue?.label;
            if (selectedValue) {
              setSearchQuery(selectedValue);
              onSubmitSearch(selectedValue);
              setOpenDropdown(false);
            }
          }}
          getOptionLabel={(option: AutoCompleteOption | string) =>
            typeof option === 'string' ? option : option.label
          }
          filterOptions={x => x}
          renderOption={(props, option) => (
            <li {...props}>
              <ListItemIcon sx={{minWidth: 32}}>
                <SearchNormal1 size="16" style={{color: '#888'}} />
              </ListItemIcon>
              <Typography variant="body2">
                {typeof option === 'string' ? option : option.label}
              </Typography>
            </li>
          )}
          sx={{width: '80%', maxWidth: '50%'}}
          renderInput={params => (
            <OutlinedInput
              {...params.InputProps}
              inputProps={{
                ...params.inputProps,
                onKeyDown: e => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    onSubmitSearch(searchQuery);
                    setOpenDropdown(false);
                  }
                },
              }}
              placeholder="Search for shop"
              fullWidth
              endAdornment={
                <InputAdornment position="end">
                  <Button
                    type="submit"
                    style={{
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      padding: 0,
                    }}
                    onClick={() => {
                      onSubmitSearch(searchQuery);
                      setOpenDropdown(false);
                    }}
                  >
                    <SearchNormal1 size="20" style={{color: '#888'}} />
                  </Button>
                </InputAdornment>
              }
            />
          )}
        />
      )}
      {downLG && <Box sx={{width: '100%', ml: 1}} />}
      {!isLoggedIn && (
        <AnimateButton>
          <Button
            disableElevation
            size="large"
            type="submit"
            variant="contained"
            href="/login"
            rel="noopener"
            color="primary"
            sx={{width: 100, mt: '-3px', ml: 3}}
          >
            Login
          </Button>
        </AnimateButton>
      )}
      {!downLG && (
        <Box sx={{flexShrink: 0, ml: 3}}>
          <a href="https://seller.ecomdukes.in/sell-on-ecomdukes">
            <Button
              variant="outlined"
              sx={{
                color: '#00004F',
                borderColor: '#00004F',
                '&:hover': {
                  borderColor: '#00004F',
                },
              }}
            >
              Sell on Ecomdukes
            </Button>
          </a>
        </Box>
      )}
      {downLG && <Box sx={{width: '100%', ml: 1}} />}
      <Box sx={{flexShrink: 0, ml: 2}}>
        <Button
          disableRipple
          disableElevation
          sx={{
            p: 0,
            minWidth: 'unset',
            backgroundColor: 'transparent',
            '&:hover': {
              backgroundColor: 'transparent',
            },
          }}
        >
          <Typography variant="h6" fontSize="1rem" sx={{color: '#00004F'}}>
            Download App
          </Typography>
        </Button>
      </Box>{' '}
      {!downLG && (isLoggedIn || !guestToken) && (
        <Box sx={{display: 'flex', flexShrink: 0, ml: 9.5, gap: 2}}>
          <ButtonBase
            onClick={() => {
              router.push('/cart');
            }}
            sx={{
              p: 0.25,
              borderRadius: 1,
              '&:hover': {
                bgcolor:
                  theme.palette.mode === ThemeMode.DARK
                    ? 'secondary.light'
                    : 'secondary.lighter',
              },
              '&:focus-visible': {
                outline: `2px solid ${theme.palette.secondary.dark}`,
                outlineOffset: 2,
              },
            }}
            aria-label="open cart"
          >
            <ShoppingCart size="30" color="#00004F" variant="Bold" />
          </ButtonBase>
          {/* )} */}

          <ButtonBase
            onClick={() => router.push('/wish-list')}
            sx={{
              p: 0.25,
              borderRadius: 1,
              '&:hover': {
                bgcolor:
                  theme.palette.mode === ThemeMode.DARK
                    ? 'secondary.light'
                    : 'secondary.lighter',
              },
              '&:focus-visible': {
                outline: `2px solid ${theme.palette.secondary.dark}`,
                outlineOffset: 2,
              },
            }}
            aria-label="open wishlist"
          >
            <Heart size="30" color="#00004F" variant="Bold" />
          </ButtonBase>

          <ButtonBase
            sx={{
              p: 0.25,
              borderRadius: 1,
              '&:hover': {
                bgcolor:
                  theme.palette.mode === ThemeMode.DARK
                    ? 'secondary.light'
                    : 'secondary.lighter',
              },
              '&:focus-visible': {
                outline: `2px solid ${theme.palette.secondary.dark}`,
                outlineOffset: 2,
              },
            }}
            aria-label="open notifications"
          >
            <Notification size="30" color="#00004F" variant="Bold" />
          </ButtonBase>
          <Box sx={{flexShrink: 0, ml: 0.75}}>
            <ButtonBase
              ref={anchorRef}
              onClick={() => setOpen(prev => !prev)}
              sx={{
                p: 0.25,
                borderRadius: 1,
                '&:hover': {
                  bgcolor:
                    theme.palette.mode === ThemeMode.DARK
                      ? 'secondary.light'
                      : 'secondary.lighter',
                },
                '&:focus-visible': {
                  outline: `2px solid ${theme.palette.secondary.dark}`,
                  outlineOffset: 2,
                },
              }}
              aria-label="open profile"
              aria-haspopup="true"
            >
              <Avatar
                sx={{
                  color: '#fff',
                }}
                src={user?.photoUrl || undefined}
              >
                {!user?.photoUrl && user?.firstName?.charAt(0)?.toUpperCase()}
              </Avatar>
            </ButtonBase>

            <Popper
              placement="bottom-end"
              open={open}
              anchorEl={anchorRef.current}
              role={undefined}
              transition
              disablePortal
              popperOptions={{
                modifiers: [
                  {
                    name: 'offset',
                    options: {
                      offset: [0, 9],
                    },
                  },
                ],
              }}
            >
              {({TransitionProps}) => (
                <Transitions
                  type="grow"
                  position="top-right"
                  in={open}
                  {...TransitionProps}
                >
                  <ClickAwayListener onClickAway={handleClose}>
                    <Box>
                      <Paper
                        sx={{
                          boxShadow: theme.customShadows.z1,
                          width: 290,
                          minWidth: 240,
                          maxWidth: 290,
                          [theme.breakpoints.down('md')]: {
                            maxWidth: 250,
                          },
                          borderRadius: 1.5,
                        }}
                      >
                        <MainCard border={false} content={false}>
                          <CardContent sx={{px: 2.5, pt: 3}}>
                            <Grid
                              container
                              justifyContent="space-between"
                              alignItems="center"
                            >
                              <Grid item>
                                <Stack
                                  direction="row"
                                  spacing={1.25}
                                  alignItems="center"
                                >
                                  <Avatar
                                    sx={{
                                      color: '#fff',
                                    }}
                                    src={user?.photoUrl || undefined}
                                  >
                                    {!user?.photoUrl &&
                                      user?.firstName?.charAt(0)?.toUpperCase()}
                                  </Avatar>{' '}
                                  <Stack>
                                    <Typography variant="subtitle1">
                                      Hello
                                    </Typography>

                                    <Typography
                                      variant="h5"
                                      color="textPrimary"
                                    >
                                      {`${user?.firstName} ${user?.lastName}`}
                                    </Typography>
                                  </Stack>
                                </Stack>
                              </Grid>
                            </Grid>
                          </CardContent>
                        </MainCard>
                      </Paper>
                      <Paper
                        sx={{
                          boxShadow: theme.customShadows.z1,
                          width: 290,
                          minWidth: 240,
                          maxWidth: 290,
                          [theme.breakpoints.down('md')]: {
                            maxWidth: 250,
                            mt: 1,
                          },
                          [theme.breakpoints.up('md')]: {
                            mt: 2,
                          },
                          borderRadius: 1.5,
                          maxHeight: '60vh',
                          overflowY: 'auto',
                        }}
                      >
                        <MainCard border={false} content={false}>
                          <CardContent>
                            <TabPanel
                              value={value}
                              index={0}
                              dir={theme.direction}
                            >
                              <ProfileTab handleLogout={handleLogout} />
                            </TabPanel>
                          </CardContent>
                        </MainCard>
                      </Paper>
                    </Box>
                  </ClickAwayListener>
                </Transitions>
              )}
            </Popper>
          </Box>
        </Box>
      )}
      {downLG && <MobileSection />}
    </>
  );
}
