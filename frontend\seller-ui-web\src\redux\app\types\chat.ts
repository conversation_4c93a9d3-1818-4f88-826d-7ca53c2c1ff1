import { ChatStatus, SenderType } from 'types/chat';

export interface ChatMessage {
  id: string;
  chatId: string;
  senderId: string;
  receiverId: string;
  senderType: SenderType;
  message: string;
  read: boolean;
  createdOn: string;
  modifiedOn: string;
  sender?: ChatSender;
}

export interface ChatSender {
  firstName?: string;
  lastName?: string;
  photoUrl?: string;
  presignedPhotoUrl?: string;
}

export interface Chat {
  id: string;
  customerId: string;
  sellerId: string;
  status: ChatStatus;
  createdOn: string;
  modifiedOn: string;
}

export interface ChatDto extends Chat {
  lastMessage?: string;
  unReadChatCount?: number;
  statusText?: string;

  userData?: {
    id: string;
    sellerId?: string;
    userTenantId: string;
    status: string;

    userTenant?: {
      id: string;
      status: number;
      userId: string;
      roleId: string;
      tenantId: string;
      deleted?: boolean;
      deletedOn?: string | null;
      deletedBy?: string | null;
      createdOn: string;
      modifiedOn: string;
      createdBy?: string | null;
      modifiedBy?: string | null;

      user?: {
        firstName?: string;
        lastName?: string;
        email?: string;
        photoUrl?: string;
      };
    };

    presignedPhotoUrl?: string;
  };
}

export interface Customer {
  id: string;
  userTenant?: UserTenant;
  status?: string;
}

export interface UserTenant {
  id: string;
  userId?: string;
  tenantId?: string;
  roleId?: string;
  status?: number;
  user?: User;
}

export interface User {
  id: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
}
