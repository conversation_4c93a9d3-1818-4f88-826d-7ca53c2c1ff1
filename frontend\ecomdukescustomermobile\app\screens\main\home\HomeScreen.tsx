import React, {useState} from 'react';
import {View, Text, StyleSheet, ScrollView, Dimensions} from 'react-native';
import {Searchbar} from 'react-native-paper';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';
import {SCREEN_NAME} from '../../../constants/screenNames';
import {HomeScreenNavigationProp} from '../../../navigations/types';
import {useNavigation} from '@react-navigation/native';
import SubCategoryList from '../Products/Components/SubCategoryList';
import CustomButton from '../../../components/CustomButton/CustomButton';
import VersionChecker from '../../../components/versionChecker/VersionChecker';
import {
  useGetMostViewedProductsQuery,
  useGetPageSectionsQuery,
  useGetRecentlyViewedProductsQuery,
  useGetTopSellingProductsQuery,
} from '../../../redux/pageSection/pageSectionApiSlice';
import {ReviewStatus} from '../../../redux/product/product';
import {fieldsExcludeMetaFields} from '../../../types/api';
import {useSelector} from 'react-redux';
import {RootState, useTypedSelector} from '../../../redux/store';
import {
  CardStyle,
  PageType,
  SectionType,
} from '../../../enums/page-section-enum';
import {PageSection} from '../../../types/page-section';
import {CarouselComponent} from './components/CarouselComponent';
import {Banner} from './components/Banner';
import {ProductList} from './components/ProductList';
import OfferList from './components/OfferList';
import {Images} from '../../../assets/images';

const {width} = Dimensions.get('window');
const HomeScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const isLoggedIn = useSelector((state: RootState) => state.auth.isLoggedIn);
  const user = useSelector((state: RootState) => state.auth.userDetails);

  const {data: sections = []} = useGetPageSectionsQuery({
    where: {isActive: true, pageType: PageType.HOME},
    order: ['displayOrder ASC'],
    include: [
      {relation: 'sectionItems', scope: {fields: fieldsExcludeMetaFields}},
    ],
    fields: fieldsExcludeMetaFields,
  });

  const productFilter = React.useMemo(() => {
    return {
      include: [
        {
          relation: 'featuredAsset',
          scope: {
            fields: {preview: true, id: true},
          },
        },
        {
          relation: 'product',
          scope: {
            fields: {description: true, id: true},
          },
        },
        {
          relation: 'productVariantPrice',
          scope: {
            fields: {
              price: true,
              mrp: true,
              currencyCode: true,
            },
          },
        },
        ...(isLoggedIn
          ? [
              {
                relation: 'wishlist',
                scope: {
                  where: {
                    deleted: false,
                    customerId: user?.profileId,
                  },
                  fields: {id: true},
                },
              },
            ]
          : []),
        {
          relation: 'reviews',
          scope: {
            fields: {
              rating: true,
            },
            where: {
              status: ReviewStatus.APPROVED,
            },
          },
        },
      ],
      fields: {
        name: true,
        id: true,
        featuredAssetId: true,
        productId: true,
      },
    };
  }, [isLoggedIn, user?.profileId]);

  const shouldFetchMostViewed = React.useMemo(
    () => sections.some(s => s.type === SectionType.MOST_VIEWED),
    [sections],
  );

  const shouldFetchRecentlyViewed = React.useMemo(
    () => sections.some(s => s.type === SectionType.RECENTLY_VIEWED),
    [sections],
  );

  const shouldFetchTopSelling = React.useMemo(
    () => sections.some(s => s.type === SectionType.TOP_SELLING),
    [sections],
  );

  const {data: mostViewed = []} = useGetMostViewedProductsQuery(productFilter, {
    skip: !isLoggedIn || !shouldFetchMostViewed,
  });

  const {data: recentlyViewed = []} = useGetRecentlyViewedProductsQuery(
    productFilter,
    {skip: !isLoggedIn || !shouldFetchRecentlyViewed},
  );

  const {data: topSelling = []} = useGetTopSellingProductsQuery(productFilter, {
    skip: !shouldFetchTopSelling,
  });

  // eslint-disable-next-line @typescript-eslint/no-shadow
  const onSubmitSearch = (searchQuery: string) => {
    if (searchQuery.trim() !== '') {
      navigation.navigate(SCREEN_NAME.SUGGESTION, {
        searchQuery,
      });
      setSearchQuery('');
    }
  };

  const handleFocus = () => {
    navigation.navigate(SCREEN_NAME.SUGGESTION, {searchQuery: ''});
    setSearchQuery('');
  };
  const store = useTypedSelector(state => state);
  console.log('Store', store);

  const renderSectionMobile = (section: PageSection) => {
    switch (section.type) {
      case SectionType.CAROUSEL:
        return (
          <View key={section.id} style={styles.carouselContainer}>
            <CarouselComponent items={section.sectionItems || []} />
          </View>
        );
      case SectionType.BANNER:
        const bannerItem = section.sectionItems?.[0];
        return bannerItem ? (
          <View key={section.id} style={styles.bannerContainer}>
            <Banner item={bannerItem} />
          </View>
        ) : null;
      case SectionType.FEATURED_COLLECTION:
        // eslint-disable-next-line @typescript-eslint/no-shadow
        const topCategories =
          section.sectionItems?.map(item => ({
            id: item.id,
            name: item.title,
            image: item.previewUrl || item.imageUrl,
          })) || [];

        return (
          <View key={section.id}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <SubCategoryList
              overlayNames
              data={topCategories.map(item => ({...item, id: Number(item.id)}))}
              showNames={false}
              imageStyle={styles.shopCategory}
              imageContainerStyle={styles.shopCategoryContainer}
            />
          </View>
        );
      case SectionType.FEATURED_PRODUCTS:
        const featuredVariants = (section.sectionItems ?? []).flatMap(
          item => item.productVariants || [],
        );

        return (
          <View key={section.id} style={styles.productSection}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <ProductList variants={featuredVariants} />
          </View>
        );
      case SectionType.RECENTLY_VIEWED:
        return (
          <View key={section.id}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>{section.title}</Text>
              <CustomButton
                mode={'outlined'}
                title={'View more'}
                labelStyle={styles.categoryLabel}
              />
            </View>
            <View style={styles.subCategory}>
              <SubCategoryList
                data={recentlyViewed.map(item => ({
                  id: Number(item.id),
                  name: item.name,
                  image: item.featuredAsset?.previewUrl || '',
                }))}
                showNames={false}
                imageContainerStyle={styles.subCategoryContainer}
              />
            </View>
          </View>
        );
      case SectionType.MOST_VIEWED:
        return (
          <View key={section.id} style={styles.mostViewdContainer}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <ProductList variants={mostViewed} />
          </View>
        );
      case SectionType.TOP_SELLING:
        return (
          <View key={section.id} style={styles.topSellingProduct}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <ProductList variants={topSelling} />
          </View>
        );
      case SectionType.GIFT_PRODUCTS:
        const giftProducts = (section.sectionItems ?? []).flatMap(
          item => item.productVariants || [],
        );
        return (
          <View key={section.id} style={styles.topSellingProduct}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <ProductList variants={giftProducts} />
          </View>
        );
      case SectionType.FACETS:
        const facetItems = section.sectionItems ?? [];
        const mappedData = facetItems.map(item => {
          const hasTitle = !!item.title;
          const hasSubtitle = !!item.subtitle;

          let styleVariant = CardStyle.IMAGE_ONLY;
          if (hasTitle && hasSubtitle) {
            styleVariant = CardStyle.IMAGE_TITLE_SUBTITLE;
          } else if (hasTitle) {
            styleVariant = CardStyle.IMAGE_TITLE;
          }

          return {
            id: item.id,
            title: item.title,
            subtitle: item.subtitle,
            image: item.previewUrl ? {uri: item.previewUrl} : Images.backpack,
            styleVariant,
          };
        });
        return (
          <View key={section.id} style={styles.productSection}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            <OfferList data={mappedData} />
          </View>
        );
      case SectionType.ALL_CATEGORIES:
        return (
          <View key={section.id} style={styles.productSection}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
          </View>
        );
      default:
        return null;
    }
  };

  const featuredCollectionSection = sections.find(
    section => section.type === 'featured-collection',
  );

  const topCategoriess =
    featuredCollectionSection?.sectionItems?.map(item => ({
      id: Number(item.id),
      name: item.title,
      image: item.previewUrl || item.imageUrl,
    })) || [];
  const cartList = useTypedSelector(state => state.cart.cartList);
  console.log('cartList mostViewed', cartList);

  return (
    <ScrollView style={styles.container}>
      <VersionChecker />

      <Searchbar
        placeholder="Search products..."
        onChangeText={text => {
          setSearchQuery(text);
          onSubmitSearch(text);
        }}
        onFocus={() => handleFocus()}
        onSubmitEditing={() => onSubmitSearch(searchQuery)}
        onIconPress={() => onSubmitSearch(searchQuery)}
        value={searchQuery}
        style={styles.searchbar}
        placeholderTextColor={colors.gray.medium}
      />
      <View style={styles.subCategory}>
        <SubCategoryList
          data={topCategoriess}
          imageStyle={styles.categoryImage}
          imageContainerStyle={styles.categoryContainer}
        />
      </View>
      {sections.map(section => renderSectionMobile(section))}

      {/* <CustomButton
        title={'Explore all products'}
        mode={'outlined'}
        style={styles.exlporeButton}
        labelStyle={{color: customColors.textBlack}}
      /> */}
      {/* <View style={styles.similarProducts}>
        <Text style={styles.sectionTitle}>Similar Products</Text>
        <SubCategoryList
          data={categories}
          showNames={false}
          imageContainerStyle={styles.subCategoryContainer}
        />
      </View>
      <View style={styles.bannerContainer2}>
        <Image
          source={Images.banner}
          style={styles.banner}
          resizeMode="cover"
        />
      </View> */}
      {/* <View style={styles.mostViewdContainer}>
        <Text style={styles.mostViewd}>Most Viewed Products</Text>

        <FlatList
          data={mostViewed}
          horizontal
          keyExtractor={item => item.id.toString()}
          contentContainerStyle={styles.flatListContainer}
          showsHorizontalScrollIndicator={false}
          renderItem={({item}) => (
            <View style={styles.productCardWidth}>
              <ProductCard
                onPress={() => handleProductClick(item.id)}
                id={Number(item.id)}
                image={item.featuredAsset?.previewUrl || ''}
                name={item.name}
                price={item.productVariantPrice?.price.toString()}
                originalPrice={item.productVariantPrice?.mrp.toString()}
                discount={
                  item.productVariantPrice?.mrp &&
                  item.productVariantPrice?.price
                    ? `${Math.round(
                        ((Number(item.productVariantPrice.mrp) -
                          Number(item.productVariantPrice.price)) /
                          Number(item.productVariantPrice.mrp)) *
                          100,
                      )}%`
                    : '0%'
                }
                rating={
                  item.reviews?.length
                    ? item.reviews.reduce((acc, r) => acc + r.rating, 0) /
                      item.reviews.length
                    : 0
                }
                onWishlistToggle={() =>
                  handleToggleWishlist(item.id.toString())
                }
                shortDescription={item.product?.description}
                isloading={false}
                isWishlisted={wishlistMap.has(item.id.toString())}
              />
            </View>
          )}
        />
      </View>
      
      {/* <View style={styles.subCategory}>
        <SubCategoryList
          overlayNames={true}
          data={subCategories}
          showNames={false}
          imageStyle={styles.occasionCategory}
          imageContainerStyle={styles.OccationCategoryContainer}
        />
      </View> */}

      {/* <FlatList
        data={sampleData}
        keyExtractor={item => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.flatListContent}
        renderItem={({item}) => (
          <OccasionCard
            title={item.title}
            description={item.description}
            image={item.image}
          />
        )}
      />
      <View style={styles.bannerContainer2}>
        <Image
          source={Images.banner}
          style={styles.banner}
          resizeMode="cover"
        />
      </View>
      <View style={styles.subCategory}>
        <SubCategoryList
          data={categories}
          showNames={false}
          imageContainerStyle={styles.subCategoryContainer}
        />
      </View>
      <FlatList
        data={mostViewedProducts}
        horizontal
        keyExtractor={item => item.id.toString()}
        contentContainerStyle={styles.flatListContainer}
        showsHorizontalScrollIndicator={false}
        renderItem={({item}) => (
          <View style={styles.productCardWidth}>
            <ProductCard
              onPress={() => {}}
              id={item.id}
              image={item.image}
              name={item.name}
              price={item.price.toString()}
              originalPrice={item.originalPrice.toString()}
              discount={item.discount}
              rating={item.rating}
              onWishlistToggle={() => {}}
              shortDescription={item.shortDescription}
              isloading={false}
            />
          </View>
        )}
      />

      <View style={styles.bannerContainer2}>
        <Image
          source={Images.banner}
          style={styles.banner}
          resizeMode="cover"
        />
      </View>
      <FlatList
        data={OfferData}
        keyExtractor={item => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.flatListContent}
        renderItem={({item}) => (
          <OfferCard amount={item.amount} image={item.image} />
        )}
      /> */}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {padding: 10, backgroundColor: customColors.white},
  flatListContent: {
    paddingHorizontal: 10,
    marginTop: 10,
  },
  flatListContainer: {gap: 10, paddingVertical: 10},

  productCardWidth: {width: 185},
  searchbar: {
    height: 50,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: colors.gray.medium,
    backgroundColor: customColors.white,
  },
  sectionTitle: {
    marginTop: 15,
    marginLeft: 15,
    fontSize: 15,
    fontWeight: '600',
    color: colors.tertiary,
  },
  mostViewdContainer: {marginTop: 10},
  mostViewd: {
    marginLeft: 4,
    marginBottom: 8,
    marginTop: 10,
    fontSize: 15,
    fontWeight: '600',
    color: colors.tertiary,
  },
  viewMore: {
    color: customColors.appBlue,
    fontSize: 14,
    height: 30,
    borderWidth: 1,
    borderRadius: 20,
    borderColor: colors.gray.medium,
    fontWeight: '500',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 1,
  },

  bannerText: {
    color: customColors.textBlack,
  },
  exploreButton: {
    marginTop: 24,
    borderRadius: 25,
    paddingVertical: 8,
  },
  subCategory: {marginBottom: 0, paddingBottom: 0},
  similarProducts: {
    marginTop: 20,
  },

  imageContainerStyle: {
    alignItems: 'center',
    marginHorizontal: 20,
    marginTop: 10,
  },
  categoryContainer: {
    alignItems: 'center',
    marginHorizontal: 10,
    marginTop: 10,
  },
  categoryImage: {
    borderRadius: 50,
    backgroundColor: customColors.white,
    height: 50,
    width: 50,
  },
  subCategoryContainer: {
    alignItems: 'center',
    marginHorizontal: 5,
    marginTop: 10,
  },
  shopCategoryContainer: {
    alignItems: 'center',
    marginHorizontal: 5,
    marginTop: 10,
  },
  OccationCategoryContainer: {
    alignItems: 'center',
    marginHorizontal: 5,
    marginTop: 10,
    marginLeft: 10,
  },

  exlporeButton: {
    width: 300,
    marginTop: 20,
    alignSelf: 'center',
    borderColor: colors.tertiary,
  },
  categoryLabel: {color: customColors.textBlack},
  shopCategory: {height: 120},
  occasionCategory: {height: 100, width: 120},
  bannerContainer: {marginBottom: 10, paddingBottom: 0, marginTop: 20},
  bannerContainer2: {marginBottom: 0, paddingBottom: 0, marginTop: 20},
  topSellingProduct: {padding: 0},
  carouselImage: {
    width: width,
    height: 180,
    resizeMode: 'cover',
  },
  productSection: {
    marginVertical: 20,
  },
  carouselContainer: {
    width: '100%',
    height: 180,
    marginVertical: 12,
  },
});

export default HomeScreen;
