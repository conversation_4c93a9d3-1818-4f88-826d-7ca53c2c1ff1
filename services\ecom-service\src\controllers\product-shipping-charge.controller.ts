import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {ProductShippingCharge} from '../models';
import {ProductShippingChargeRepository} from '../repositories';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {PermissionKeys} from '@local/core';

const basePath = '/product-shipping-charges';

export class ProductShippingChargeController {
  constructor(
    @repository(ProductShippingChargeRepository)
    public productShippingChargeRepository: ProductShippingChargeRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'ProductShippingCharge model instance',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(ProductShippingCharge)},
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ProductShippingCharge, {
            title: 'NewProductShippingCharge',
            exclude: ['id'],
          }),
        },
      },
    })
    productShippingCharge: Omit<ProductShippingCharge, 'id'>,
  ): Promise<ProductShippingCharge> {
    return this.productShippingChargeRepository.create(productShippingCharge);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'ProductShippingCharge model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(ProductShippingCharge) where?: Where<ProductShippingCharge>,
  ): Promise<Count> {
    return this.productShippingChargeRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of ProductShippingCharge model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ProductShippingCharge, {
            includeRelations: true,
          }),
        },
      },
    },
  })
  async find(
    @param.filter(ProductShippingCharge) filter?: Filter<ProductShippingCharge>,
  ): Promise<ProductShippingCharge[]> {
    return this.productShippingChargeRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(basePath)
  @response(STATUS_CODE.OK, {
    description: 'ProductShippingCharge PATCH success count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ProductShippingCharge, {partial: true}),
        },
      },
    })
    productShippingCharge: ProductShippingCharge,
    @param.where(ProductShippingCharge) where?: Where<ProductShippingCharge>,
  ): Promise<Count> {
    return this.productShippingChargeRepository.updateAll(
      productShippingCharge,
      where,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'ProductShippingCharge model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(ProductShippingCharge, {
          includeRelations: true,
        }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ProductShippingCharge, {exclude: 'where'})
    filter?: FilterExcludingWhere<ProductShippingCharge>,
  ): Promise<ProductShippingCharge> {
    return this.productShippingChargeRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ProductShippingCharge PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ProductShippingCharge, {partial: true}),
        },
      },
    })
    productShippingCharge: Partial<ProductShippingCharge>,
  ): Promise<void> {
    await this.productShippingChargeRepository.updateById(
      id,
      productShippingCharge,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ProductShippingCharge PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() productShippingCharge: ProductShippingCharge,
  ): Promise<void> {
    await this.productShippingChargeRepository.replaceById(
      id,
      productShippingCharge,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ProductShippingCharge DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.productShippingChargeRepository.deleteById(id);
  }
}
