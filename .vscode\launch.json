{"version": "0.2.0", "configurations": [{"name": "Debug Ecom Facade Service", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/facades/ecom-facade", "runtimeArgs": ["-r", "ts-node/register"], "program": "${workspaceFolder}/facades/ecom-facade/dist/index.js", "outFiles": ["${workspaceFolder}/facades/ecom-facade/dist/**/*.js"], "sourceMaps": true, "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "restart": true}, {"name": "Attach to Process (Ecom Facade Service)", "type": "node", "request": "attach", "processId": "${command:PickProcess}", "cwd": "${workspaceFolder}/facades/ecom-facade", "restart": true, "sourceMaps": true, "outFiles": ["${workspaceFolder}/facades/ecom-facade/dist/**/*.js"], "internalConsoleOptions": "neverOpen"}, {"name": "Debug Notification Service", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/services/notification-service", "runtimeArgs": ["-r", "ts-node/register"], "program": "${workspaceFolder}/services/notification-service/dist/index.js", "outFiles": ["${workspaceFolder}/services/notification-service/dist/**/*.js"], "sourceMaps": true, "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "restart": true}, {"name": "Attach to Process (Notification Service)", "type": "node", "request": "attach", "processId": "${command:PickProcess}", "cwd": "${workspaceFolder}/services/notification-service", "restart": true, "sourceMaps": true, "outFiles": ["${workspaceFolder}/services/notification-service/dist/**/*.js"], "internalConsoleOptions": "neverOpen"}, {"name": "Debug Authentication Service", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/services/authentication-service", "runtimeArgs": ["-r", "ts-node/register"], "program": "${workspaceFolder}/services/authentication-service/dist/index.js", "outFiles": ["${workspaceFolder}/services/authentication-service/dist/**/*.js"], "sourceMaps": true, "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "restart": true}, {"name": "Attach to Process (authentication Service)", "type": "node", "request": "attach", "processId": "${command:PickProcess}", "cwd": "${workspaceFolder}/services/authentication-service", "restart": true, "sourceMaps": true, "outFiles": ["${workspaceFolder}/services/authentication-service/dist/**/*.js"], "internalConsoleOptions": "neverOpen"}]}