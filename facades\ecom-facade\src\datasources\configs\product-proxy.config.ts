import {ModifiedRestService, RestOperationTemplate} from '@sourceloop/core';
import {
  Product,
  ProductDto,
  ProductOptionGroupDto,
  ProductVariant,
  BulkStatusUpdateDto,
  SingleStatusUpdateDto,
} from '../../models';
import {FilterExcludingWhere} from '@loopback/repository';
export type ProductProxyType = {
  createProduct(
    product: Omit<ProductDto, 'id' | 'slug'>,
    token: string,
  ): Promise<Product>;
  updateProductById(
    id: string,
    product: Partial<ProductDto>,
    token: string,
  ): Promise<Product>;
  createOptionGroupByProductId(
    id: string,
    option: Omit<ProductOptionGroupDto, 'id' | 'code' | 'productId'>,
    token: string,
  ): Promise<void>;
  updateOptionGroupByProductId(
    id: string,
    groupId: string,
    option: Partial<ProductOptionGroupDto>,
    token: string,
  ): Promise<void>;
  getRecentlyViewedProducts(
    token: string,
    filter?: FilterExcludingWhere<ProductVariant>,
  ): Promise<ProductVariant[]>;
  getMostViewedProducts(
    token: string,
    filter?: FilterExcludingWhere<ProductVariant>,
  ): Promise<ProductVariant[]>;
  getTopSellingProducts(
    token: string,
    filter?: FilterExcludingWhere<ProductVariant>,
  ): Promise<ProductVariant[]>;
  bulkStatusUpdate(
    bulkUpdate: BulkStatusUpdateDto,
    token: string,
  ): Promise<void>;
  updateProductStatus(
    id: string,
    statusUpdate: SingleStatusUpdateDto,
    token: string,
  ): Promise<void>;
} & ModifiedRestService<Product>;

export const ProductProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'POST',
      url: '/products',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      createProduct: ['body', 'token'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: '/products/{id}',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      updateProductById: ['id', 'body', 'token'],
    },
  },
  {
    template: {
      method: 'POST',
      url: '/products/{id}/option-group',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      createOptionGroupByProductId: ['id', 'body', 'token'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: '/products/{id}/option-group/{groupId}',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      updateOptionGroupByProductId: ['id', 'groupId', 'body', 'token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/products/recently-viewed',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      query: {
        filter: '{filter}',
      },
    },
    functions: {
      getRecentlyViewedProducts: ['token', 'filter'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/products/most-viewed',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      query: {
        filter: '{filter}',
      },
    },
    functions: {
      getMostViewedProducts: ['token', 'filter'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/products/top-selling',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      query: {
        filter: '{filter}',
      },
    },
    functions: {
      getTopSellingProducts: ['token', 'filter'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: '/products/bulk-status-update',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      bulkStatusUpdate: ['body', 'token'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: '/products/{id}/status',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      updateProductStatus: ['id', 'body', 'token'],
    },
  },
];
