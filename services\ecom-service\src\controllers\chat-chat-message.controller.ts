import {
  Count,
  CountSchema,
  Filter,
  Where,
  repository,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
  response,
} from '@loopback/rest';
import {Chat, ChatMessage} from '../models';
import {ChatRepository} from '../repositories';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';

const basePath = '/chats/{id}/chat-messages';

export class ChatChatMessageController {
  constructor(
    @repository(ChatRepository)
    protected chatRepository: ChatRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewChatMessage]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Chat has many ChatMessage',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ChatMessage, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.path.string('id') id: string,
    @param.query.object('filter') filter?: Filter<ChatMessage>,
  ): Promise<ChatMessage[]> {
    return this.chatRepository.messages(id).find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateChatMessage]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'ChatMessage model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(ChatMessage),
      },
    },
  })
  async create(
    @param.path.string('id') id: typeof Chat.prototype.id,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ChatMessage, {
            title: 'NewChatMessageInChat',
            exclude: ['id', 'chatId'],
          }),
        },
      },
    })
    chatMessage: Omit<ChatMessage, 'id'>,
  ): Promise<ChatMessage> {
    return this.chatRepository.messages(id).create(chatMessage);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateChatMessage]})
  @patch(basePath)
  @response(STATUS_CODE.OK, {
    description: 'ChatMessage PATCH success count',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: CountSchema,
      },
    },
  })
  async patch(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ChatMessage, {partial: true}),
        },
      },
    })
    chatMessage: Partial<ChatMessage>,
    @param.query.object('where', getWhereSchemaFor(ChatMessage))
    where?: Where<ChatMessage>,
  ): Promise<Count> {
    return this.chatRepository.messages(id).patch(chatMessage, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteChatMessage]})
  @del(basePath)
  @response(STATUS_CODE.OK, {
    description: 'ChatMessage DELETE success count',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: CountSchema,
      },
    },
  })
  async delete(
    @param.path.string('id') id: string,
    @param.query.object('where', getWhereSchemaFor(ChatMessage))
    where?: Where<ChatMessage>,
  ): Promise<Count> {
    return this.chatRepository.messages(id).delete(where);
  }
}
