import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from 'redux/apiSlice';

import { HelpItem } from 'types/admin';

export const helpApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getHelp: builder.query<
      HelpItem[],
      {
        limit: number;
        skip?: number;
        order?: Array<Record<string, unknown> | string>;
        where?: Record<string, unknown>;
        fields?: Record<string, boolean>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ limit, skip, order = ['createdOn DESC'], where, fields, include }) => ({
        url: '/helps',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            limit,
            offset: skip,
            order,
            where,
            fields,
            include
          })
        }
      })
    }),
    createHelp: builder.mutation<void, Partial<HelpItem>>({
      query: (payload) => ({
        url: '/helps',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: payload
      })
    }),
    getHelpById: builder.query<HelpItem, { id: string; include?: Array<Record<string, unknown> | string> }>({
      query: ({ id, include }) => ({
        url: `/helps/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: include ? { filter: JSON.stringify({ include }) } : undefined
      })
    }),
    removeHelp: builder.mutation<void, string>({
      query: (id) => ({
        url: `/helps/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    updateHelp: builder.mutation<void, { id: string; data: Partial<HelpItem> }>({
      query: ({ id, data }) => ({
        url: `/helps/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),
    getHelpsCount: builder.query<{ count: number }, void>({
      query: () => ({
        url: '/helps/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    })
  }),
  overrideExisting: false
});

export const {
  useCreateHelpMutation,
  useGetHelpByIdQuery,
  useRemoveHelpMutation,
  useUpdateHelpMutation,
  useGetHelpsCountQuery,
  useGetHelpQuery
} = helpApiSlice;
