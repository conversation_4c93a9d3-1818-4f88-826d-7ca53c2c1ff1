import React, {useEffect, useState} from 'react';
import {FlatList, ScrollView, StyleSheet, View} from 'react-native';
import {ActivityIndicator, Searchbar} from 'react-native-paper';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';
import MyOrderCard from './components/MyOrderCard';
import {useGetUserQuery} from '../../../redux/auth/authApiSlice';
import {useGetOrderLineQuery} from '../../../redux/order/orderApiSlice';
import {OrderLineItem, OrderStatus} from '../../../redux/order/order';
import {useNavigation} from '@react-navigation/native';
import {MyOrderScreenNavigationProp} from '../../../navigations/types';
import {SCREEN_NAME} from '../../../constants/screenNames';

const MyOrderScreen = () => {
  const navigation = useNavigation<MyOrderScreenNavigationProp>();

  const [searchQuery, setSearchQuery] = useState('');
  const [expandedCard, setExpandedCard] = useState<number | null>(null);
  const [filteredOrders, setFilteredOrders] = useState<
    OrderLineItem[] | undefined
  >([]);
  const handleToggle = (index: number) => {
    setExpandedCard(prev => (prev === index ? null : index));
  };
  const handleOrderClick = (productId: string) => {
    navigation.navigate(SCREEN_NAME.PRODUCTS, {productId});
  };
  const {data: user} = useGetUserQuery();
  const customerId = user?.profileId;

  const filter = {
    include: [
      {
        relation: 'order',
        required: true,
        scope: {
          where: {customerId},
          include: [
            {
              relation: 'promoCode',
            },
            {relation: 'shippingAddress'},
            {relation: 'billingAddress'},
          ],
        },
      },
      {
        relation: 'productVariant',
        required: true,
        scope: {
          where: {
            name: {ilike: `%${searchQuery}%`},
          },
          include: [{relation: 'product'}, {relation: 'featuredAsset'}],
        },
      },
      {
        relation: 'review',
      },
    ],
  };

  const {
    data: orderLineItems,
    isLoading: orderLoading,
    refetch,
  } = useGetOrderLineQuery({filter}, {skip: !customerId});

  useEffect(() => {
    if (orderLineItems) {
      const filtered = orderLineItems.filter(order =>
        order?.productVariant?.name
          .toLowerCase()
          .includes(searchQuery.toLowerCase()),
      );
      setFilteredOrders(filtered);
    }
  }, [searchQuery, orderLineItems]);

  const fullStepConfig = [
    {
      label: 'Order Placed',
      status: [OrderStatus.Pending, OrderStatus.Paid],
      message: 'Your order has been placed and is being processed.',
    },
    {
      label: 'Shipped',
      status: [OrderStatus.Picked],
      message: 'Your order has been picked and packed.',
    },
    {
      label: 'Out for Delivery',
      status: [OrderStatus.InTransit],
      message: 'Your order is on the way.',
    },
    {
      label: 'Delivered',
      status: [OrderStatus.Delivered],
      message: 'Your order has been delivered.',
    },
  ];

  const getActiveStepIndex = (status: OrderStatus): number => {
    return fullStepConfig.findIndex(step => step.status.includes(status));
  };

  return (
    <View style={styles.mainContainer}>
      <ScrollView contentContainerStyle={styles.container}>
        <Searchbar
          placeholder="Search Orders..."
          onChangeText={text => setSearchQuery(text)}
          onSubmitEditing={() => setSearchQuery(searchQuery)}
          onIconPress={() => setSearchQuery(searchQuery)}
          value={searchQuery}
          style={styles.searchbar}
          placeholderTextColor={colors.gray.medium}
        />
        {orderLoading && <ActivityIndicator />}
        <FlatList
          data={filteredOrders}
          keyExtractor={(_, index) => index.toString()}
          renderItem={({index, item}) => {
            const activeStep = getActiveStepIndex(
              item.order?.status as OrderStatus,
            );
            const address =
              item.order?.shippingAddress || item.order?.billingAddress;

            const deliveryAddress = address
              ? `${address.name}\n${address.phoneNumber}\n${
                  address.addressLine1
                }${address.addressLine2 ? ', ' + address.addressLine2 : ''}\n${
                  address.locality
                }, ${address.city}\n${address.state}, ${address.country}\n${
                  address.zipCode
                }${address.landmark ? '\nLandmark: ' + address.landmark : ''}`
              : '';

            return (
              <MyOrderCard
                productImage={
                  item.productVariant?.featuredAsset?.previewUrl ?? ''
                }
                productName={item.productVariant?.name || 'Product Name'}
                deliveredOn={''}
                deliveryAddress={deliveryAddress || 'No address provided'}
                quantity={item.quantity || 1}
                price={item?.order?.totalAmount?.toString() || '0'}
                showDetails={expandedCard === index}
                onToggleDetails={() => handleToggle(index)}
                isDelivered={item?.order?.status === OrderStatus.Delivered}
                status={item?.order?.status as string}
                stepConfig={fullStepConfig}
                activeStep={activeStep}
                onPress={() => handleOrderClick(item.productVariantId)}
                isReviewed={true}
                handleReview={() => {}}
                reviewId={item?.review?.id || ''}
                productVariantId={item?.productVariant?.id || ''}
                orderLineItemId={item.id}
                customerId={customerId || ''}
                isReview={item.review?.id ? true : false}
                refetch={refetch}
                orderLineItems={orderLineItems!}
              />
            );
          }}
          // eslint-disable-next-line react/no-unstable-nested-components
          ItemSeparatorComponent={() => <View style={styles.cardItemStyle} />}
        />
      </ScrollView>
    </View>
  );
};
export default MyOrderScreen;
const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: customColors.white,
    padding: 15,
  },
  card: {
    backgroundColor: customColors.white,
    borderRadius: 15,
    padding: 16,
    marginBottom: 20,
    shadowOffset: {width: 0, height: 0},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 6,
  },
  container: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: customColors.white,
  },
  searchbar: {
    height: 50,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: colors.gray.medium,
    backgroundColor: colors.gray.card,
    marginBottom: 20,
  },
  greetingCard: {
    backgroundColor: customColors.white,
    borderRadius: 10,
    marginBottom: 20,
    padding: 30,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardItemStyle: {height: 16},
});
