'use client';

import { useFormik } from 'formik';
import { TextField, Button, Select, MenuItem, Box, Typography, CardContent, Card, Grid, InputLabel, InputAdornment } from '@mui/material';
import { useRouter } from 'next/navigation';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { FaqVisibility } from 'enums/faq-visibility.enum';
import { FaqStatus } from 'enums/faq-status.enum';
import { supportValidationSchema } from '../../../validations/faq';
import { useEffect } from 'react';
import { SupportContactInfo } from 'types/admin';
import { useCreateSupportMutation, useUpdateSupportMutation } from 'redux/app/contents/support/supportApiSlice';

interface SupportCreateProps {
  initialValues?: Partial<SupportContactInfo>;
  isEdit?: boolean;
  faqId?: string;
  refetch?: () => Promise<void> | any;
}

function SupportCreate({ initialValues, isEdit = false, faqId, refetch }: SupportCreateProps) {
  const router = useRouter();
  const [createSupport] = useCreateSupportMutation();
  const [updateSupport] = useUpdateSupportMutation();

  const formatEnumKey = (key: string) => {
    return key
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };
  const formik = useFormik({
    initialValues: {
      supportEmail: initialValues?.supportEmail || '',
      supportPhone: initialValues?.supportPhone || '',
      visibility: initialValues?.visibility ?? FaqVisibility.ALL,
      status: initialValues?.status ?? FaqStatus.ACTIVE
    },
    validationSchema: supportValidationSchema,
    onSubmit: async (values) => {
      const supportData: Partial<SupportContactInfo> = {
        supportEmail: values.supportEmail,
        supportPhone: values.supportPhone,
        visibility: values.visibility,
        status: values.status
      };

      if (isEdit && faqId) {
        await updateSupport({ id: faqId, data: supportData }).unwrap();
        openSnackbar({
          open: true,
          message: 'Support updated successfully',
          variant: 'alert',
          alert: { color: 'success' }
        } as SnackbarProps);
        router.push('/contents/support');
      } else {
        await createSupport(supportData).unwrap();
        openSnackbar({
          open: true,
          message: 'Support created successfully',
          variant: 'alert',
          alert: { color: 'success' }
        } as SnackbarProps);
        router.push('/contents/support');
      }

      await refetch?.();
    }
  });
  useEffect(() => {
    const fetchData = async () => {
      if (refetch) {
        await refetch();
      }
    };
    fetchData();
  }, [refetch]);

  return (
    <Card sx={{ maxWidth: '100%', mx: 'auto', mt: 2, p: 2, boxShadow: 3 }}>
      <CardContent>
        <form onSubmit={formik.handleSubmit}>
          <Typography variant="h5" sx={{ mb: 2 }}>
            {isEdit ? 'Edit Support' : 'Create Support'}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 2 }}>
            <Grid item xs={6}>
              <Typography variant="body1" color="text.secondary">
                Email Address
              </Typography>
              <TextField
                name="supportEmail"
                type="email"
                placeholder="<EMAIL>"
                fullWidth
                variant="outlined"
                value={formik.values.supportEmail}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.supportEmail && Boolean(formik.errors.supportEmail)}
                helperText={formik.touched.supportEmail && typeof formik.errors.supportEmail === 'string' ? formik.errors.supportEmail : ''}
                InputProps={{ style: { borderRadius: '8px' } }}
              />
            </Grid>
            <Grid item xs={6}>
              <InputLabel htmlFor="supportPhone">Phone</InputLabel>
              <TextField
                name="supportPhone"
                fullWidth
                variant="outlined"
                value={formik.values.supportPhone}
                onChange={(e) => {
                  const { value } = e.target;
                  if (/^\d{0,10}$/.test(value)) {
                    formik.setFieldValue('supportPhone', value);
                  }
                }}
                onBlur={formik.handleBlur}
                error={formik.touched.supportPhone && Boolean(formik.errors.supportPhone)}
                sx={{ borderRadius: '20px' }}
                helperText={formik.touched.supportPhone && formik.errors.supportPhone ? formik.errors.supportPhone : ''}
                inputProps={{
                  maxLength: 10,
                  inputMode: 'numeric',
                  pattern: '[0-9]*'
                }}
                InputProps={{
                  startAdornment: <InputAdornment position="start">+91</InputAdornment>,
                  style: { borderRadius: '8px' }
                }}
              />
            </Grid>
          </Grid>
          <Grid container spacing={2} sx={{ mt: 2 }}>
            <Grid item xs={6}>
              <InputLabel htmlFor="visibility">Visibility</InputLabel>

              <Select
                fullWidth
                name="visibility"
                value={formik.values.visibility}
                onChange={(event) => formik.setFieldValue('visibility', Number(event.target.value))}
              >
                <MenuItem value="" disabled>
                  Visibility{' '}
                </MenuItem>
                {Object.entries(FaqVisibility)
                  .filter(([key]) => isNaN(Number(key)))
                  .map(([key, value]) => (
                    <MenuItem key={key} value={value}>
                      {formatEnumKey(key)}
                    </MenuItem>
                  ))}
              </Select>
            </Grid>

            <Grid item xs={6}>
              <InputLabel htmlFor="status">Status</InputLabel>

              <Select
                fullWidth
                name="status"
                value={formik.values.status}
                onChange={(event) => formik.setFieldValue('status', Number(event.target.value))}
              >
                <MenuItem value="" disabled>
                  Status{' '}
                </MenuItem>
                {Object.entries(FaqStatus)
                  .filter(([key]) => isNaN(Number(key)))
                  .map(([key, value]) => (
                    <MenuItem key={key} value={value}>
                      {formatEnumKey(key)}
                    </MenuItem>
                  ))}
              </Select>
            </Grid>
          </Grid>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button type="submit" variant="contained" color="primary" disableElevation>
              {isEdit ? 'Update Support' : 'Create Support'}
            </Button>
          </Box>
        </form>
      </CardContent>
    </Card>
  );
}

export default SupportCreate;
