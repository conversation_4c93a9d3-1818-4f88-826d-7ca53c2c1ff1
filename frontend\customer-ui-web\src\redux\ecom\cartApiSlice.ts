import {ApiSliceIdentifier} from 'enums/api.enum';
import {apiSlice} from 'redux/apiSlice';
import {buildFilterParams, IFilter} from 'types/api';
import {
  Cart,
  CartItem,
  CheckoutCartResponse,
  CustomizationValue,
} from 'types/cart';

export const cartApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    // Create a new cart
    createCart: builder.mutation<Cart, Partial<Cart>>({
      query: cart => ({
        url: '/carts',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: cart,
      }),
    }),

    // Add item to cart
    addItemToCart: builder.mutation<Cart, Partial<CartItem>>({
      query: cartItem => ({
        url: '/carts/items',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: cartItem,
      }),
    }),

    // Update cart item quantity
    updateCartItem: builder.mutation<void, Omit<CartItem, 'id' | 'cartId'>>({
      query: cartItem => ({
        url: '/carts/items',
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: cartItem,
      }),
    }),

    // Remove item from cart
    removeCartItem: builder.mutation<void, string>({
      query: productVariantId => ({
        url: `/carts/items/product/${productVariantId}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),

    getCarts: builder.query<Cart[], {filter?: IFilter}>({
      query: ({filter}) => ({
        url: '/carts',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    // Get cart by ID
    getCartById: builder.query<
      Cart,
      {
        id: string;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({id, include}) => ({
        url: `/carts/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({include}),
        },
      }),
    }),

    // Update cart by ID
    updateCart: builder.mutation<
      void,
      {
        id: string;
        data: Partial<Cart>;
      }
    >({
      query: ({id, data}) => ({
        url: `/carts/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data,
      }),
    }),

    // Delete cart by ID
    deleteCart: builder.mutation<void, string>({
      query: id => ({
        url: `/carts/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),

    // Checkout cart
    checkoutCart: builder.mutation<
      CheckoutCartResponse,
      {
        shippingAddressId: string;
        billingAddressId: string;
        useDukeCoins: boolean;
      }
    >({
      query: (body: {
        shippingAddressId: string;
        billingAddressId: string;
        useDukeCoins: boolean;
      }) => ({
        url: '/carts/checkout',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body,
      }),
    }),

    getActiveCart: builder.query<Cart | null, {filter?: IFilter}>({
      query: ({filter}) => ({
        url: '/carts/active',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    updateProductCustomizations: builder.mutation<
      void,
      {
        cartId: string;
        data: Partial<CustomizationValue>[];
      }
    >({
      query: ({cartId, data}) => ({
        url: `/carts/${cartId}/customizations`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data,
      }),
    }),
  }),
});

export const {
  useGetCartsQuery,
  useCreateCartMutation,
  useAddItemToCartMutation,
  useUpdateCartItemMutation,
  useRemoveCartItemMutation,
  useGetCartByIdQuery,
  useLazyGetCartByIdQuery,
  useUpdateCartMutation,
  useDeleteCartMutation,
  useCheckoutCartMutation,
  useGetActiveCartQuery,
  useUpdateProductCustomizationsMutation,
} = cartApiSlice;
