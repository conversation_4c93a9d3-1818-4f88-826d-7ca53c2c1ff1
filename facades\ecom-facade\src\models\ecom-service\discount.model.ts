import {model, property, hasMany} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {DiscountCondition} from './discount-condition.model';

@model({name: 'discounts'})
export class Discount extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'boolean',
    default: true,
    name: 'is_active',
  })
  isActive: boolean;

  @property({
    type: 'date',
    name: 'start_date',
  })
  startDate: string;

  @property({
    type: 'date',
    name: 'end_date',
  })
  endDate: string;

  @property({
    type: 'number',
    name: 'usage_limit_per_user',
    default: 1,
  })
  usageLimitPerUser: number;

  @property({
    type: 'boolean',
    default: false,
  })
  combinable: boolean;

  @hasMany(() => DiscountCondition, {keyTo: 'discountId'})
  discountConditions: DiscountCondition[];

  constructor(data?: Partial<Discount>) {
    super(data);
  }
}

export interface DiscountRelations {
  // describe navigational properties here
}

export type DiscountWithRelations = Discount & DiscountRelations;
