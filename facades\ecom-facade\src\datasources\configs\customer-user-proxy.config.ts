import {ModifiedRestService, RestOperationTemplate} from '@sourceloop/core';
import {Customer} from '../../models';

export interface CustomerUserProxyType extends ModifiedRestService<Customer> {
  createCustomer(customer: Partial<Customer>, token: string): Promise<Customer>;
}

export const CustomerUserProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'GET',
      url: '/customers/user/{userTenantId}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
      query: {
        filter: '{filter}',
      },
    },
    functions: {
      findCustomerByUserTenantId: ['userTenantId', 'filter', 'token'],
    },
  },
];
