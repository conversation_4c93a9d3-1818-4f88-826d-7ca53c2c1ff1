'use client';
import {Fragment, useEffect, useState} from 'react';
import {useTheme} from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Divider from '@mui/material/Divider';
import List from '@mui/material/List';
import Skeleton from '@mui/material/Skeleton';
import ListItem from '@mui/material/ListItem';
import Typography from '@mui/material/Typography';
import ListItemText from '@mui/material/ListItemText';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import ListItemButton from '@mui/material/ListItemButton';
import {TickCircle} from 'iconsax-react';
import Chance from 'chance';

import UserAvatar from './UserAvatar';
import Dot from 'components/@extended/Dot';
import {useGetUserChatsQuery} from 'redux/chat/chatApiSlice';
import {ChatDto} from 'types/chat';

const chance = new Chance();

interface UserListProps {
  setUser: (u: ChatDto) => void;
  search?: string;
  selectedUser: string | null;
}

export function UserList({setUser, search, selectedUser}: UserListProps) {
  const theme = useTheme();
  const [filteredData, setFilteredData] = useState<ChatDto[]>([]);
  const {data = [], isLoading: loading} = useGetUserChatsQuery({search});

  const users = data as ChatDto[];
  useEffect(() => {
    if (!loading) {
      let result = users;
      if (search) {
        const query = search.toLowerCase();
        result = users.filter(row => {
          const firstName = row?.userData?.userTenant?.user?.firstName || '';
          const lastName = row?.userData?.userTenant?.user?.lastName || '';
          const email = row?.userData?.userTenant?.user?.email || '';
          const fullName = `${firstName} ${lastName}`;
          return (
            fullName.toLowerCase().includes(query) ||
            email.toLowerCase().includes(query)
          );
        });
      }
      setFilteredData(result);
    }
  }, [loading, search, users]);

  if (loading) {
    return (
      <List>
        {[...Array(5)].map((_, index) => (
          <ListItem key={index} divider>
            <ListItemAvatar>
              <Skeleton variant="circular" width={40} height={40} />
            </ListItemAvatar>
            <ListItemText
              primary={<Skeleton animation="wave" height={24} />}
              secondary={<Skeleton animation="wave" height={16} width="60%" />}
            />
          </ListItem>
        ))}
      </List>
    );
  }

  return (
    <List component="nav">
      {filteredData.map(user => {
        const fullName =
          user?.userData?.userTenant?.user?.firstName &&
          user?.userData?.userTenant?.user?.lastName
            ? `${user.userData.userTenant.user.firstName} ${user.userData.userTenant.user.lastName}`
            : `User (${user.id})`;

        return (
          <Fragment key={user.id}>
            <ListItemButton
              sx={{pl: 1, borderRadius: 0, '&:hover': {borderRadius: 1}}}
              onClick={() => setUser(user)}
              selected={user.id === selectedUser}
            >
              <ListItemAvatar>
                <UserAvatar user={user} />
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Stack
                    direction="row"
                    alignItems="center"
                    justifyContent="space-between"
                    spacing={1}
                  >
                    <Typography
                      variant="subtitle1"
                      color="text.primary"
                      sx={{
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {fullName}
                    </Typography>
                    <Typography color="text.secondary" variant="caption">
                      {user.lastMessage || ''}
                    </Typography>
                  </Stack>
                }
                secondary={
                  <Typography
                    color="text.secondary"
                    sx={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    {user.statusText || 'online'}
                    {user.unReadChatCount ? (
                      <Dot />
                    ) : (
                      <TickCircle
                        size={16}
                        style={{
                          color: chance.bool()
                            ? theme.palette.secondary[400]
                            : theme.palette.primary.main,
                        }}
                      />
                    )}
                  </Typography>
                }
              />
            </ListItemButton>
            <Divider />
          </Fragment>
        );
      })}
    </List>
  );
}
