'use client';
// material-ui
import {styled, useTheme} from '@mui/material/styles';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

// third-party
import {motion} from 'framer-motion';

// project-imports
import Logo from 'components/logo';

// assets
import {Divider} from '@mui/material';

// link - custom style
const FooterLink = styled(Link)(({theme}) => ({
  color: theme.palette.text.primary,
  '&:hover, &:active': {
    color: theme.palette.primary.main,
  },
}));

type showProps = {
  isFull?: boolean;
};

// ==============================|| MAIN LAYOUT - FOOTER ||============================== //

export default function Footer({isFull}: showProps) {
  const theme = useTheme();

  return (
    <>
      <Box
        sx={{
          pt: isFull ? 5 : 10,
          pb: 5,
          bgcolor: 'secondary.200',
          borderTop: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Container
          sx={{
            py: 2.4,
            borderTop: '1.2px solid black',
            bgcolor: 'secondary.200',
          }}
        >
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <motion.div
                initial={{opacity: 0, translateY: 550}}
                animate={{opacity: 1, translateY: 0}}
                transition={{
                  type: 'spring',
                  stiffness: 150,
                  damping: 30,
                }}
              >
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Logo to="/" />
                  </Grid>
                  <Grid item xs={12}>
                    <Grid
                      container
                      spacing={2}
                      justifyContent="flex-start"
                      sx={{flexWrap: 'wrap'}}
                    >
                      {[
                        {
                          src: '/assets/images/footer/facebook.svg',
                          alt: 'Facebook',
                        },

                        {
                          src: '/assets/images/footer/instagram.svg',
                          alt: 'Instagram',
                        },
                        {
                          src: '/assets/images/footer/youtube.svg',
                          alt: 'YouTube',
                        },
                      ].map((item, index) => (
                        <Grid item key={index}>
                          <img
                            src={item.src}
                            alt={item.alt}
                            style={{width: '40px', height: 'auto'}}
                          />
                        </Grid>
                      ))}
                    </Grid>
                  </Grid>

                  <Grid item xs={12}>
                    <Typography
                      variant="subtitle1"
                      sx={{fontWeight: 400, maxWidth: 320}}
                    >
                      ECOMDUKES PRIVATE LIMITED
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography
                      variant="subtitle1"
                      sx={{fontWeight: 200, maxWidth: 350}}
                    >
                      37/2259,Kollamkudy Tower,Maleppally
                      Road,Thrikkakara,vazhakkala,Ernakulam ,Kerala -682021
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography
                      variant="subtitle1"
                      sx={{fontWeight: 200, maxWidth: 350}}
                    >
                      CIN:U74999KL2021PTCO72700
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography
                      variant="subtitle1"
                      sx={{fontWeight: 200, maxWidth: 350}}
                    >
                      ph:**********{' '}
                    </Typography>
                  </Grid>
                </Grid>
              </motion.div>
            </Grid>
            <Grid item xs={12} md={8}>
              <Grid container spacing={{xs: 2, md: 3}}>
                <Grid item xs={6} sm={3}>
                  <Stack spacing={2}>
                    <Typography variant="h5">Categories</Typography>
                    <Stack spacing={{xs: 0.5, md: 1}}>
                      <FooterLink target="_blank" underline="none">
                        Fashion
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Education
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Frozen Foods{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Bevereages
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Organic Grocery
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Office Supplies
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Beauty Products{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Books{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Electronic & gadgets{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Travel Acessories{' '}
                      </FooterLink>
                    </Stack>
                  </Stack>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Stack spacing={2}>
                    <Typography variant="h5">About Us</Typography>
                    <Stack spacing={{xs: 0.5, md: 1}}>
                      <FooterLink target="_blank" underline="none">
                        About Ecomdukes
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Careers{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        News & Blogs
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Help{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Press center{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Shop by Location{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Ecomdukes Brands{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Affiliate & Brands{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Ideas & Guides{' '}
                      </FooterLink>
                    </Stack>
                  </Stack>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Stack spacing={2}>
                    <Typography variant="h5">Services</Typography>
                    <Stack spacing={{xs: 0.5, md: 1}}>
                      <FooterLink target="_blank" underline="none">
                        Mobile App{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Shipping & Delivery
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Order Pickup
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Account Signup{' '}
                      </FooterLink>
                    </Stack>
                  </Stack>
                  <Stack spacing={2} mt={3}>
                    <Typography variant="h5">Legal</Typography>
                    <Stack spacing={{xs: 0.5, md: 1}}>
                      <FooterLink
                        href="terms-condition"
                        target="_blank"
                        underline="none"
                      >
                        Terms of Use{' '}
                      </FooterLink>
                      <FooterLink
                        href="privacy-policy"
                        target="_blank"
                        underline="none"
                      >
                        Privacy Policy{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Affiliate Policy{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Return & Refund Policy{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Infringement Policy{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Agreements{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Disclaimer{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Guidelines{' '}
                      </FooterLink>
                    </Stack>
                  </Stack>
                </Grid>

                <Grid item xs={6} sm={3}>
                  <Stack spacing={2}>
                    <Typography variant="h5">Help</Typography>
                    <Stack spacing={{xs: 0.5, md: 1}}>
                      <FooterLink target="_blank" underline="none">
                        Shopcart Help{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Returns
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Track Orders
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Contact Us
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Feedback{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Security & Fraud{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Report a Bug{' '}
                      </FooterLink>
                      <FooterLink target="_blank" underline="none">
                        Grievance Redressal{' '}
                      </FooterLink>
                    </Stack>
                  </Stack>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Container>
        <Box
          sx={{
            py: 2.4,
            bgcolor: 'secondary.200',
            display: 'flex',
            justifyContent: 'center',
          }}
        >
          <Container maxWidth="md">
            <Box
              sx={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: 2,
                p: 2,
                border: '0.5px solid black',
                borderRadius: 1,
                alignItems: 'center',
                maxWidth: '900px',
              }}
            >
              <Box sx={{display: 'flex', alignItems: 'center', gap: 2}}>
                <Typography variant="h5" sx={{fontWeight: 'bold'}}>
                  Accepted Payments
                </Typography>
                <Divider
                  orientation="vertical"
                  flexItem
                  sx={{height: 40, bgcolor: 'black'}}
                />
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  alignItems: 'center',
                  gap: 2,
                }}
              >
                {[
                  {name: 'visa', url: 'https://www.visa.com/'},
                  {name: 'mastercard', url: 'https://www.mastercard.com/'},
                  {name: 'amazon', url: 'https://pay.amazon.com/'},
                  {name: 'paypal', url: 'https://www.paypal.com/'},
                  {name: 'apple-pay', url: 'https://www.apple.com/apple-pay/'},
                  {name: 'google-pay', url: 'https://pay.google.com/'},
                  {name: 'phone-pe', url: 'https://www.phonepe.com/'},
                ].map(({name, url}) => (
                  <Link
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    component="a"
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: 60,
                        height: 50,
                        border: '0.5px solid black',
                        borderRadius: 1,
                        p: 1,
                        bgcolor: 'grey.100',
                        cursor: 'pointer',
                      }}
                    >
                      <img
                        src={`/assets/images/footer/${name}.svg`}
                        alt={`${name} Icon`}
                        style={{width: '40px', height: 'auto'}}
                      />
                    </Box>
                  </Link>
                ))}

                <Divider
                  orientation="vertical"
                  flexItem
                  sx={{height: 40, bgcolor: 'black'}}
                />
                {[
                  {name: 'upi', url: 'https://www.npci.org.in/'},
                  {name: 'rupay', url: 'https://www.rupay.co.in/'},
                ].map(({name, url}) => (
                  <Link
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    component="a"
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: 60,
                        height: 50,
                        border: '0.5px solid black',
                        borderRadius: 1,
                        p: 1,
                        bgcolor: 'grey.100',
                        cursor: 'pointer',
                      }}
                    >
                      <img
                        src={`/assets/images/footer/${name}.svg`}
                        alt={`${name} Icon`}
                        style={{width: '40px', height: 'auto'}}
                      />
                    </Box>
                  </Link>
                ))}
              </Box>
            </Box>
          </Container>
        </Box>

        <Container
          sx={{
            py: 2.4,
            borderTop: '1.2px solid black',
            bgcolor: 'secondary.200',
          }}
        >
          <Grid
            container
            spacing={2}
            alignItems="center"
            sx={{
              flexDirection: {xs: 'column', sm: 'row'},
            }}
          >
            <Grid item xs={12} sm={8}>
              <Grid
                container
                spacing={2}
                alignItems="center"
                sx={{
                  flexDirection: {xs: 'column', sm: 'row'},
                  textAlign: {xs: 'center', sm: 'left'},
                }}
              >
                <Grid
                  item
                  container
                  xs={6}
                  sm={3}
                  alignItems="center"
                  spacing={1}
                  sx={{
                    justifyContent: {xs: 'center', sm: 'flex-start'},
                  }}
                >
                  <Grid item>
                    <img
                      src="/assets/images/seller.svg"
                      alt="Seller Icon"
                      style={{width: '30px', height: 'auto'}}
                    />
                  </Grid>
                  <Grid item>
                    <Typography>Become Seller</Typography>
                  </Grid>
                </Grid>

                <Grid
                  item
                  container
                  xs={6}
                  sm={3}
                  alignItems="center"
                  spacing={1}
                  sx={{
                    justifyContent: {xs: 'center', sm: 'flex-start'},
                  }}
                >
                  <Grid item>
                    <img
                      src="/assets/images/help.svg"
                      alt="Help Icon"
                      style={{width: '30px', height: 'auto'}}
                    />
                  </Grid>
                  <Grid item>
                    <Typography>Help Center</Typography>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12} sm={4}>
              <Grid
                container
                spacing={2}
                alignItems="center"
                sx={{
                  justifyContent: {xs: 'center', sm: 'flex-end'},
                  textAlign: {xs: 'center', sm: 'right'},
                }}
              >
                <Grid item xs={6} sm={4}>
                  <Typography>Terms of Service</Typography>
                </Grid>
                <Grid item xs={6} sm={4}>
                  <Typography>Privacy & Policy</Typography>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </>
  );
}
