import {ApiSliceIdentifier} from '../../constants/enums';
import {StoreCreateDto} from '../../types/onboard';
import {ISellerStore} from '../../types/store';
import {apiSlice} from '../apiSlice';

export const storeApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    sellerOnboard: builder.mutation({
      query: ({sellerOnboardData}: {sellerOnboardData: StoreCreateDto}) => {
        const formData = new FormData();
        const {sampleProductImages, ...rest} = sellerOnboardData;
        Object.entries(rest).forEach(([key, value]) => {
          if (value) {
            formData.append(key, value as unknown as Blob);
          }
        });
        // Prepare and append metadata
        if (sellerOnboardData.sampleProductImages?.length) {
          // Prepare and append metadata only if there are products
          sellerOnboardData.sampleProductImages.forEach((item, index) => {
            formData.append(
              `sampleProductImages[${index}].name`,
              item.productName,
            );
            item.images.forEach(file => {
              const fileToUpload = {
                name: file?.filename,
                uri: file?.path,
                type: file?.mime,
              };
              formData.append(
                `sampleProductImages[${index}].thumbnails`,
                fileToUpload,
              );
            });
          });
        }

        return {
          url: '/sellers/on-board',
          method: 'POST',
          body: formData,
          formData: true,
          apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        };
      },
    }),

    updateSellerStoreById: builder.mutation<
      Partial<ISellerStore>,
      {sellerStoreId: string; body: Partial<ISellerStore>}
    >({
      query: ({sellerStoreId, body}) => {
        const assetKeys = ['logo', 'banner', 'dp', 'signature'];
        const formData = new FormData();

        Object.entries(body).forEach(([key, value]) => {
          if (assetKeys?.some(item => item === key)) {
            if (
              typeof value === 'object' &&
              value !== null &&
              'path' in value
            ) {
              formData.append(key, {
                uri: value.path,
                type: value.mime,
                name: value.filename,
              } as any);
            }
          } else {
            formData.append(key, String(value));
          }
        });

        return {
          url: `/seller-stores/${sellerStoreId}`,
          method: 'PATCH',
          body: formData,
          apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
          formData: true,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        };
      },
    }),

    getSellerStoreBySelleryId: builder.query<ISellerStore, string>({
      query: sellerId => ({
        url: `/seller-stores/seller/${sellerId}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            order: ['createdOn DESC'],
          }),
        },
      }),
    }),
  }),
});

export const {
  useSellerOnboardMutation,
  useUpdateSellerStoreByIdMutation,
  useLazyGetSellerStoreBySelleryIdQuery,
  useGetSellerStoreBySelleryIdQuery,
} = storeApiSlice;
