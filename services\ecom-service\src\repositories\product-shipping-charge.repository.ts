import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, repository} from '@loopback/repository';
import {PgDataSource} from '../datasources';
import {
  ProductShippingCharge,
  ProductShippingChargeRelations,
  SellerShippingProfile,
  Product,
  ProductVariant,
} from '../models';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from '@sourceloop/core';
import {SellerShippingProfileRepository} from './seller-shipping-profile.repository';
import {ProductRepository} from './product.repository';
import {ProductVariantRepository} from './product-variant.repository';

export class ProductShippingChargeRepository extends SequelizeUserModifyCrudRepositoryCore<
  ProductShippingCharge,
  typeof ProductShippingCharge.prototype.id,
  ProductShippingChargeRelations
> {
  public readonly shippingProfile: BelongsToAccessor<
    SellerShippingProfile,
    typeof ProductShippingCharge.prototype.id
  >;

  public readonly product: BelongsToAccessor<
    Product,
    typeof ProductShippingCharge.prototype.id
  >;

  public readonly productVariant: BelongsToAccessor<
    ProductVariant,
    typeof ProductShippingCharge.prototype.id
  >;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('SellerShippingProfileRepository')
    protected sellerShippingProfileRepositoryGetter: Getter<SellerShippingProfileRepository>,
    @repository.getter('ProductRepository')
    protected productRepositoryGetter: Getter<ProductRepository>,
    @repository.getter('ProductVariantRepository')
    protected productVariantRepositoryGetter: Getter<ProductVariantRepository>,
  ) {
    super(ProductShippingCharge, dataSource, getCurrentUser);

    this.shippingProfile = this.createBelongsToAccessorFor(
      'shippingProfile',
      sellerShippingProfileRepositoryGetter,
    );
    this.registerInclusionResolver(
      'shippingProfile',
      this.shippingProfile.inclusionResolver,
    );

    this.product = this.createBelongsToAccessorFor(
      'product',
      productRepositoryGetter,
    );
    this.registerInclusionResolver('product', this.product.inclusionResolver);

    this.productVariant = this.createBelongsToAccessorFor(
      'productVariant',
      productVariantRepositoryGetter,
    );
    this.registerInclusionResolver(
      'productVariant',
      this.productVariant.inclusionResolver,
    );
  }
}
