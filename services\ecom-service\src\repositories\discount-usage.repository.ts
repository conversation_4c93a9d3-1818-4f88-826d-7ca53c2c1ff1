import {Getter, inject} from '@loopback/core';
import {PgDataSource} from '../datasources';
import {DiscountUsage, DiscountUsageRelations, Discount, Order} from '../models';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {repository, BelongsToAccessor} from '@loopback/repository';
import {DiscountRepository} from './discount.repository';
import {OrderRepository} from './order.repository';

export class DiscountUsageRepository extends SequelizeUserModifyCrudRepositoryCore<
  DiscountUsage,
  typeof DiscountUsage.prototype.id,
  DiscountUsageRelations
> {

  public readonly discount: BelongsToAccessor<Discount, typeof DiscountUsage.prototype.id>;

  public readonly order: BelongsToAccessor<Order, typeof DiscountUsage.prototype.id>;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>, @repository.getter('DiscountRepository') protected discountRepositoryGetter: Getter<DiscountRepository>, @repository.getter('OrderRepository') protected orderRepositoryGetter: Getter<OrderRepository>,
  ) {
    super(DiscountUsage, dataSource, getCurrentUser);
    this.order = this.createBelongsToAccessorFor('order', orderRepositoryGetter,);
    this.registerInclusionResolver('order', this.order.inclusionResolver);
    this.discount = this.createBelongsToAccessorFor('discount', discountRepositoryGetter,);
    this.registerInclusionResolver('discount', this.discount.inclusionResolver);
  }
}
