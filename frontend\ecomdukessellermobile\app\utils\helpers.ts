import RNFS from 'react-native-fs';
import {viewDocument} from '@react-native-documents/viewer';
import mime from 'mime'; // auto-detects content type
export const openDocument = async (uri: string) => {
  try {
    let filePath = uri;
    console.log('filePath', filePath);

    // Get the filename
    const fileName = uri.startsWith('https')
      ? uri.split('/').pop()?.split('?')[0]
      : uri.split('/').pop();
    const extension = fileName?.split('.').pop()?.toLowerCase();
    const mimeType = extension
      ? mime.getType(extension)
      : 'application/octet-stream';

    // If it's remote, download locally
    if (uri.startsWith('https')) {
      const localPath = `${RNFS.DocumentDirectoryPath}/${fileName}`;
      const fileExists = await RNFS.exists(localPath);

      if (!fileExists) {
        const result = await RNFS.downloadFile({
          fromUrl: uri,
          toFile: localPath,
        }).promise;

        if (result.statusCode !== 200) {
          throw new Error(`Failed to download file: ${result.statusCode}`);
        }
      }

      filePath = 'file://' + localPath; // iOS needs file:// prefix
    }

    // Open the document
    await viewDocument({
      uri: filePath,
      mimeType: mimeType ? mimeType : undefined,
    });
  } catch (error) {
    console.log('Error viewing document:', error);
  }
};

export function encryptData(plainText: string) {
  return btoa(plainText);
}
