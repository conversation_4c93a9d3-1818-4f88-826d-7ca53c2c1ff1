'use client';

import {Box, Grid, Typography} from '@mui/material';
import {useAppSelector} from 'redux/hooks';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import {ReviewStatus} from 'types/review';
import ProductCard from 'views/products/ProductCard';
import {useGetMostViewedProductsQuery} from 'redux/ecom/pageSectionApiSlice';

export default function ViewAllMostViewed() {
  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);
  const {data: user} = useGetUserQuery(undefined, {skip: !isLoggedIn});

  const productFilter = {
    include: [
      {
        relation: 'featuredAsset',
        scope: {fields: {preview: true, id: true}},
      },
      {
        relation: 'product',
        scope: {fields: {description: true, id: true}},
      },
      {
        relation: 'productVariantPrice',
        scope: {
          fields: {price: true, mrp: true, currencyCode: true},
        },
      },
      ...(isLoggedIn
        ? [
            {
              relation: 'wishlist',
              scope: {
                where: {deleted: false, customerId: user?.profileId},
                fields: {id: true},
              },
            },
          ]
        : []),
      {
        relation: 'reviews',
        scope: {
          fields: {rating: true},
          where: {status: ReviewStatus.APPROVED},
        },
      },
    ],
    fields: {name: true, id: true, featuredAssetId: true, productId: true},
  };

  const {data = [], isLoading} = useGetMostViewedProductsQuery(productFilter, {
    skip: !isLoggedIn,
  });

  return (
    <Box p={3}>
      <Typography variant="h4" mb={2}>
        Most Viewed Products
      </Typography>
      <Grid container spacing={2}>
        {isLoading ? (
          <Typography>Loading...</Typography>
        ) : (
          data.map(variant => (
            <Grid item xs={12} sm={6} md={3} key={variant.id}>
              <ProductCard productVariant={variant} />
            </Grid>
          ))
        )}
      </Grid>
    </Box>
  );
}
