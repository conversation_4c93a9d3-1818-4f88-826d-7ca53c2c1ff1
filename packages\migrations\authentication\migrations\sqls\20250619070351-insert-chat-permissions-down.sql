/* Replace with your SQL commands */

-- Remove Chat permissions
UPDATE main.roles
SET permissions = array_remove(permissions, 'CreateChat')
WHERE role_type IN (0, 1, 2);

UPDATE main.roles
SET permissions = array_remove(permissions, 'ViewChat')
WHERE role_type IN (0, 1, 2);

UPDATE main.roles
SET permissions = array_remove(permissions, 'UpdateChat')
WHERE role_type IN (0, 1, 2);

UPDATE main.roles
SET permissions = array_remove(permissions, 'DeleteChat')
WHERE role_type IN (0, 1, 2);

-- Remove ChatMessage permissions
UPDATE main.roles
SET permissions = array_remove(permissions, 'CreateChatMessage')
WHERE role_type IN (0, 1, 2);

UPDATE main.roles
SET permissions = array_remove(permissions, 'ViewChatMessage')
WHERE role_type IN (0, 1, 2);

UPDATE main.roles
SET permissions = array_remove(permissions, 'UpdateChatMessage')
WHERE role_type IN (0, 1, 2);

UPDATE main.roles
SET permissions = array_remove(permissions, 'DeleteChatMessage')
WHERE role_type IN (0, 1, 2);
