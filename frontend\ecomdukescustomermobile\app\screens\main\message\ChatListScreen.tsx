import React, {useState} from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Image,
  StyleSheet,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import {Icon, IconButton, Searchbar} from 'react-native-paper';
import {ChatNavigationProp} from '../../../navigations/types';
import {SCREEN_NAME} from '../../../constants/screenNames';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';

dayjs.extend(relativeTime);

type ChatItem = {
  sellerId: string;
  name: string;
  avatarUrl: string | null;
  lastMessageAt: string;
};

type Props = {
  chats?: ChatItem[];
  onRefresh?: () => void;
};
dayjs.extend(relativeTime);
const ChatListScreen: React.FC<Props> = ({chats, onRefresh}) => {
  const navigation = useNavigation<ChatNavigationProp>();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const mockChats = [
    {
      sellerId: '123',
      name: 'nisha m',
      avatarUrl: null,
      lastMessageAt: '2025-06-24T12:00:00Z',
    },
    {
      sellerId: '456',
      name: 'james d',
      avatarUrl: null,
      lastMessageAt: '2025-06-23T16:00:00Z',
    },
  ];

  const filteredChats = mockChats?.filter(chat =>
    chat.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const renderChatItem = ({item}: {item: ChatItem}) => (
    <TouchableOpacity
      style={styles.chatCard}
      onPress={() =>
        navigation.navigate(SCREEN_NAME.MESSAGE, {
          sellerId: item.sellerId ?? '',
        })
      }>
      {item.avatarUrl ? (
        <Image source={{uri: item.avatarUrl}} style={styles.avatar} />
      ) : (
        <View style={styles.initialsCircle}>
          <Text style={styles.initialsText}>
            {item.name
              .split(' ')
              .map(n => n[0])
              .join('')
              .toUpperCase()}
          </Text>
        </View>
      )}

      <View>
        <Text style={styles.chatName}>{item.name}</Text>
        <Text style={styles.chatTime}>
          {dayjs(item.lastMessageAt).fromNow()}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.fullScreen}>
      <View style={styles.headerRow}>
        <IconButton
          icon="chevron-left"
          size={38}
          iconColor={colors.tertiary}
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        />
        <Text style={styles.headerText}>Messages</Text>
        <View style={styles.badge}>
          <Text style={styles.badgeText}>{chats?.length || 7}</Text>
        </View>
        <TouchableOpacity onPress={onRefresh}>
          <Icon source="refresh" size={20} color="#444" />
        </TouchableOpacity>
      </View>
      <ScrollView>
        <View style={styles.container}>
          <Searchbar
            placeholder="Search products..."
            onChangeText={text => {
              setSearchQuery(text);
            }}
            onSubmitEditing={() => setSearchQuery(searchQuery)}
            onIconPress={() => setSearchQuery(searchQuery)}
            value={searchQuery}
            style={styles.searchbar}
            placeholderTextColor={colors.gray.medium}
          />
          <FlatList
            data={filteredChats}
            keyExtractor={item => item.sellerId}
            renderItem={renderChatItem}
            contentContainerStyle={styles.contentStyle}
            showsVerticalScrollIndicator={false}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ChatListScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  headerText: {
    fontSize: 22,
    fontWeight: 'bold',
    color: customColors.textBlack,
    flex: 1,
    marginLeft: 4,
  },
  badge: {
    backgroundColor: colors.tertiary,
    marginHorizontal: 8,
    borderRadius: 10,
    paddingHorizontal: 6,
    paddingVertical: 2,
    padding: 10,
  },
  badgeText: {
    color: customColors.white,
    fontSize: 14,
    fontWeight: 'bold',
  },
  searchBox: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray.light,
    borderRadius: 26,
    paddingHorizontal: 14,
    paddingVertical: 6,
    marginBottom: 14,
  },

  chatCard: {
    backgroundColor: customColors.white,
    borderRadius: 18,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    marginRight: 12,
  },
  initialsCircle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.tertiary,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  initialsText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: customColors.white,
  },
  chatName: {
    fontSize: 16,
    color: customColors.textBlack,
    fontWeight: 'bold',
  },
  chatTime: {
    fontSize: 13,
    color: customColors.textBlack,
    marginTop: 2,
  },
  searchbar: {
    marginHorizontal: 10,
    borderRadius: 25,
    marginTop: 10,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: colors.gray.medium,
    backgroundColor: customColors.white,
    height: 50,
  },
  fullScreen: {
    flex: 1,
    backgroundColor: customColors.white,
  },
  contentStyle: {paddingBottom: 20},
  backButton: {
    margin: 0,
    padding: 0,
  },
});
