import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {ShippingMethod} from '../models';
import {ShippingMethodRepository} from '../repositories';
import {STATUS_CODE, CONTENT_TYPE} from '@sourceloop/core';

const basePath = '/shipping-methods';

export class ShippingMethodController {
  constructor(
    @repository(ShippingMethodRepository)
    public shippingMethodRepository: ShippingMethodRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'ShippingMethod model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(ShippingMethod)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ShippingMethod, {
            title: 'NewShippingMethod',
            exclude: ['id'],
          }),
        },
      },
    })
    shippingMethod: Omit<ShippingMethod, 'id'>,
  ): Promise<ShippingMethod> {
    return this.shippingMethodRepository.create(shippingMethod);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'ShippingMethod model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(ShippingMethod) where?: Where<ShippingMethod>,
  ): Promise<Count> {
    return this.shippingMethodRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of ShippingMethod model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ShippingMethod, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ShippingMethod) filter?: Filter<ShippingMethod>,
  ): Promise<ShippingMethod[]> {
    return this.shippingMethodRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(basePath)
  @response(STATUS_CODE.OK, {
    description: 'ShippingMethod PATCH success count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ShippingMethod, {partial: true}),
        },
      },
    })
    shippingMethod: ShippingMethod,
    @param.where(ShippingMethod) where?: Where<ShippingMethod>,
  ): Promise<Count> {
    return this.shippingMethodRepository.updateAll(shippingMethod, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'ShippingMethod model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(ShippingMethod, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ShippingMethod, {exclude: 'where'})
    filter?: FilterExcludingWhere<ShippingMethod>,
  ): Promise<ShippingMethod> {
    return this.shippingMethodRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ShippingMethod PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ShippingMethod, {partial: true}),
        },
      },
    })
    shippingMethod: Partial<ShippingMethod>,
  ): Promise<void> {
    await this.shippingMethodRepository.updateById(id, shippingMethod);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ShippingMethod PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() shippingMethod: ShippingMethod,
  ): Promise<void> {
    await this.shippingMethodRepository.replaceById(id, shippingMethod);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ShippingMethod DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.shippingMethodRepository.deleteById(id);
  }
}
