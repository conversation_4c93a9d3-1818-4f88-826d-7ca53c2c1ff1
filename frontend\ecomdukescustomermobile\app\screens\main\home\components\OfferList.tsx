import React from 'react';
import {FlatList, ViewStyle, StyleProp, StyleSheet} from 'react-native';
import OfferCard from '../../Products/Components/OfferCard';

type OfferItem = {
  id: string;
  title?: string;
  subtitle?: string;
  image: string;
};

type OfferListProps = {
  data: OfferItem[];
  containerStyle?: StyleProp<ViewStyle>;
};

const OfferList: React.FC<OfferListProps> = ({data, containerStyle}) => {
  return (
    <FlatList
      data={data}
      keyExtractor={item => item.id}
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={[styles.flatListContent, containerStyle]}
      renderItem={({item}) => {
        return (
          <OfferCard
            title={item.title}
            subtitle={item.subtitle}
            image={item.image}
          />
        );
      }}
    />
  );
};

export default OfferList;
export const styles = StyleSheet.create({
  flatListContent: {
    paddingVertical: 8,
  },
});
