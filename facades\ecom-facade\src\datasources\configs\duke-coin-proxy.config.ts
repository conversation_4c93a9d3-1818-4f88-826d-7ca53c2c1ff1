import {Duke<PERSON>oin} from '../../models/ecom-service/duke-coin.model';
import {ModifiedRestService, RestOperationTemplate} from '@sourceloop/core';

export type DukeCoinProxyType = {
  addCoins(
    userId: string,
    body: {amount: number; description?: string},
    token: string,
  ): Promise<{balance: number}>;
  getCoinBalance(token: string): Promise<{balance: number}>;
} & ModifiedRestService<DukeCoin>;
export const DukeCoinProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'POST',
      url: 'duke-coins/users/{userId}/coins/add',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      addCoins: ['userId', 'body', 'token'],
    },
  },

  // --- Get User Coin Balance ---
  {
    template: {
      method: 'GET',
      url: 'duke-coins/users/coins/balance',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      getCoinBalance: ['token'],
    },
  },
];
