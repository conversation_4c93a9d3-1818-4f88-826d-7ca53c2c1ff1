import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from 'redux/apiSlice';
import { Seller } from 'types/seller';
import { ChatDto, ChatMessage, Customer } from '../types/chat';

export const chatApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getChatMessages: builder.query<ChatMessage[], string>({
      query: (chatId) => ({
        url: `/chat-messages`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            where: { chatId },
            order: ['created_on ASC']
          })
        }
      })
    }),
    getSellerChats: builder.query<ChatDto[], { search?: string }>({
      query: ({ search }) => ({
        url: `/chats/my`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            include: [
              {
                relation: 'customer',
                scope: {
                  include: [
                    {
                      relation: 'userTenant',
                      scope: {
                        include: ['user']
                      }
                    }
                  ]
                }
              }
            ]
          }),
          ...(search ? { search } : {})
        }
      })
    }),

    // ✅ Send a message
    sendChatMessage: builder.mutation<ChatMessage, { chatId: string; message: { message: string } }>({
      query: ({ chatId, message }) => ({
        url: `/chat-messages`,
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: {
          ...message,
          chatId
        }
      })
    }),

    getCustomers: builder.query<Customer[], { search?: string }>({
      query: ({ search }) => ({
        url: `/customers`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            where: {
              userTenantId: {
                neq: null
              }
            },
            include: [
              {
                relation: 'userTenant',
                scope: {
                  include: ['user']
                }
              }
            ]
          }),
          ...(search ? { search } : {})
        }
      })
    }),

    // ✅ Optionally get seller by ID
    getSellerById: builder.query<Seller, { id: string; include?: Array<Record<string, unknown> | string> }>({
      query: ({ id, include }) => ({
        url: `/sellers/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            include: include || []
          })
        }
      })
    }),
    markChatAsRead: builder.mutation<void, string>({
      query: (chatId) => ({
        url: `/chat-messages/mark-read/${chatId}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: {
          chatId
        }
      })
    })
  })
});

export const {
  useGetChatMessagesQuery,
  useGetSellerChatsQuery,
  useSendChatMessageMutation,
  useGetSellerByIdQuery,
  useGetCustomersQuery,
  useMarkChatAsReadMutation
} = chatApiSlice;
