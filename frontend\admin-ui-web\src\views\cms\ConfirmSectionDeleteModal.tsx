import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography } from '@mui/material';

type ConfirmDeleteModalProps = {
  open: boolean;
  title?: string;
  onClose: () => void;
  onConfirm: () => void;
};

export const ConfirmDeleteModal = ({ open, onClose, onConfirm, title }: ConfirmDeleteModalProps) => (
  <Dialog open={open} onClose={onClose}>
    <DialogTitle>Confirm Deletion</DialogTitle>
    <DialogContent>
      <Typography>Are you sure you want to delete this {title ?? 'section'}?</Typography>
    </DialogContent>
    <DialogActions>
      <Button onClick={onClose}>Cancel</Button>
      <Button onClick={onConfirm} color="error" variant="contained">
        Delete
      </Button>
    </DialogActions>
  </Dialog>
);
