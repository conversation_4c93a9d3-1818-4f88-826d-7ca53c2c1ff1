import {ApiSliceIdentifier} from 'enums/api.enum';
import {apiSlice} from 'redux/apiSlice';
import {buildFilterParams, Count, IFilter} from 'types/api';
import {DukeCoin} from 'types/duke';

export const DukeCoinApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getDukeCoins: builder.query<DukeCoin[], {filter?: IFilter}>({
      query: ({filter}) => ({
        url: '/duke-coins',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    getDukeCoinsCount: builder.query<Count, {filter?: IFilter}>({
      query: ({filter}) => ({
        url: '/duke-coins/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
    getDukeCoinBalance: builder.query<
      {balance: number; maxApplicable: number},
      void
    >({
      query: () => ({
        url: '/duke-coins/users/coins/balance',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
      }),
    }),
  }),
});

export const {
  useGetDukeCoinsQuery,
  useLazyGetDukeCoinsQuery,
  useGetDukeCoinsCountQuery,
  useGetDukeCoinBalanceQuery,
} = DukeCoinApiSlice;
