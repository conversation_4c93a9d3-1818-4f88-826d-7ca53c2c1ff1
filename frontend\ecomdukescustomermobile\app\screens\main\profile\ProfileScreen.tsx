import React, {useState} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Switch,
  TouchableOpacity,
} from 'react-native';
import {Text, TextInput, Divider, Icon} from 'react-native-paper';
import CustomButton from '../../../components/CustomButton/CustomButton';
import customColors from '../../../theme/customColors';
import CustomTextInput from '../../../components/InputFields/CustomTextInput';
import {colors} from '../../../theme/colors';
import {
  useGetUserQuery,
  useUpdateUserMutation,
} from '../../../redux/auth/authApiSlice';
import {useFormik} from 'formik';
import Toast from 'react-native-toast-message';

const ProfileScreen = () => {
  const genderOptions = [
    {label: 'Male', value: 'M'},
    {label: 'Female', value: 'F'},
    {label: 'Other', value: 'O'},
  ];
  const {data: user} = useGetUserQuery();
  const [updateUser] = useUpdateUserMutation();

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      gender: user?.gender || '',
      email: user?.email || '',
      phone: user?.phone || '',
      password: '',
      twoStepEnabled: true,
    },
    // validationSchema: profileFormValidation,
    onSubmit: values => {
      handleProfileUpdate(values);
    },
  });
  const handleProfileUpdate = async (values: typeof formik.initialValues) => {
    const updatedUser = {
      firstName: values.firstName,
      lastName: values.lastName,
      gender: values.gender,
      email: values.email,
      phone: values.phone,
      twoStepEnabled: values.twoStepEnabled,
    };

    if (!user?.profileId) {
      console.warn('User ID missing');
      return;
    }

    await updateUser({id: user.id, data: updatedUser}).unwrap();

    Toast.show({
      text1: 'Profile Updated',
      type: 'success',
    });
  };
  const [isEditable, setIsEditable] = useState(false);
  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={styles.containerView}>
        <View style={styles.section}>
          <View style={styles.rowBetween}>
            <Text variant="titleSmall">Personal Information</Text>
            {isEditable ? (
              <Text
                style={styles.edit}
                onPress={() => setIsEditable(!isEditable)}>
                View
              </Text>
            ) : (
              <Text
                style={styles.edit}
                onPress={() => setIsEditable(!isEditable)}>
                Edit
              </Text>
            )}
          </View>
          <View style={styles.row}>
            <TextInput
              mode="outlined"
              style={styles.inputHalf}
              value={formik.values.firstName}
              onChangeText={formik.handleChange('firstName')}
              activeOutlineColor={colors.tertiary}
              textColor={isEditable ? customColors.textBlack : colors.gray.dark}
              editable={isEditable}
            />
            <TextInput
              mode="outlined"
              style={styles.inputHalf}
              value={formik.values.lastName}
              onChangeText={formik.handleChange('lastName')}
              activeOutlineColor={colors.tertiary}
              textColor={isEditable ? customColors.textBlack : colors.gray.dark}
              editable={isEditable}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text variant="titleSmall">Gender</Text>
          <View style={styles.genderOptionRow}>
            {genderOptions.map(option => (
              <View key={option.value} style={styles.genderOption}>
                {isEditable ? (
                  <TouchableOpacity
                    onPress={() =>
                      formik.setFieldValue('gender', option.value)
                    }>
                    <Icon
                      source={
                        formik.values.gender === option.value
                          ? 'checkbox-blank-circle'
                          : 'checkbox-blank-circle-outline'
                      }
                      size={25}
                      color={
                        formik.values.gender === option.value
                          ? colors.tertiary
                          : colors.gray.dark
                      }
                    />
                  </TouchableOpacity>
                ) : (
                  <Icon
                    source={
                      formik.values.gender === option.value
                        ? 'checkbox-blank-circle'
                        : 'checkbox-blank-circle-outline'
                    }
                    size={25}
                    color={
                      formik.values.gender === option.value
                        ? colors.tertiary
                        : colors.gray.dark
                    }
                  />
                )}
                <Text style={styles.genderText}>{option.label}</Text>
              </View>
            ))}
          </View>
        </View>
        <View style={styles.section}>
          <View style={styles.rowBetween}>
            <Text variant="titleSmall">Email Address</Text>
          </View>
          <CustomTextInput
            title=""
            mode="outlined"
            value={formik.values.email}
            onChangeText={formik.handleChange('email')}
            textColor={isEditable ? customColors.textBlack : colors.gray.dark}
            editable={isEditable}
          />
        </View>

        <View style={styles.section}>
          <View style={styles.rowBetween}>
            <Text variant="titleSmall">Mobile Number</Text>
          </View>
          <CustomTextInput
            mode="outlined"
            title=""
            value={formik.values.phone}
            onChangeText={formik.handleChange('phone')}
            keyboardType="phone-pad"
            textColor={isEditable ? customColors.textBlack : colors.gray.dark}
            editable={isEditable}
          />
        </View>

        <View style={styles.section}>
          <View style={styles.verificationView}>
            <Text variant="titleSmall">Two step verification</Text>
            <Switch
              value={formik.values.twoStepEnabled}
              onValueChange={val => {
                formik.setFieldValue('twoStepEnabled', val);
              }}
              trackColor={{
                false: colors.gray.light,
                true: colors.tertiary,
              }}
            />
          </View>
        </View>
        {isEditable && (
          <CustomButton
            title={'Save Profile'}
            mode="contained"
            onPress={formik.handleSubmit}
          />
        )}
        <CustomButton title={' Deactivate Account'} mode="contained" />

        <Divider style={styles.bottomNavDivider} />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: customColors.white,
  },
  containerView: {flex: 1, backgroundColor: customColors.white, padding: 15},

  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  headerText: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  section: {
    marginVertical: 10,
  },
  inputHalf: {
    flex: 1,
    marginRight: 8,
  },
  row: {
    flexDirection: 'row',
    gap: 8,
  },
  rowBetween: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  verificationView: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
    gap: 8,
  },
  radioRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  edit: {
    color: customColors.primaryContainer,
    fontWeight: '500',
  },
  deactivateBtn: {
    marginTop: 20,
    paddingVertical: 6,
  },
  bottomNavDivider: {
    marginTop: 40,
  },
  genderOptionRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    marginTop: 8,
  },
  genderOption: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  genderText: {
    marginLeft: 4,
    fontSize: 14,
  },
});

export default ProfileScreen;
