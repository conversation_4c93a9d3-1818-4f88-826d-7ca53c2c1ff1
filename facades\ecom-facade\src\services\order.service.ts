import {injectable, BindingScope, inject, Getter} from '@loopback/core';
import {
  CashFreeOrderDto,
  CashFreeOrderSplit,
  Customer,
  CustomerDetails,
  IAuthUserWithTenant,
  Order,
  OrderLineItem,
  OrderLineItemWithRelations,
  OrderWithRelations,
  Payment,
  Seller,
} from '../models';
import {OrderEntity} from 'cashfree-pg';
import {ModifiedRestService, restService} from '@sourceloop/core';
import {
  CustomerProxyType,
  OrderProxyType,
  PaymentProxyType,
} from '../datasources/configs';
import {RestBindings, Request} from '@loopback/rest';
import {AuthenticationBindings} from 'loopback4-authentication';
import {DEFAULT_CURRENCY} from '@local/core';
import {FilterExcludingWhere} from '@loopback/repository';
import {CustomerDto} from '../models/ecom-service/dto/customer-dto.model';

@injectable({scope: BindingScope.TRANSIENT})
export class OrderService {
  private token: string;
  constructor(
    @restService(Payment)
    public paymentProxy: PaymentProxyType,
    @restService(Order)
    public orderProxy: OrderProxyType,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    private readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @restService(Customer)
    private readonly customerProxy: CustomerProxyType,
    @restService(OrderLineItem)
    private readonly orderLineItemProxy: ModifiedRestService<OrderLineItem>,
    @restService(Seller)
    private readonly sellerProxy: ModifiedRestService<Seller>,
  ) {
    if (request.headers.authorization) {
      this.token = request.headers.authorization;
    }
  }

  async createOrderInPg(
    order: Order,
    customer: Customer,
  ): Promise<OrderEntity> {
    const currentUser = await this.getCurrentUser();
    const body = new CashFreeOrderDto({
      customerDetails: new CustomerDetails({
        customerEmail: currentUser.email ?? '',
        customerPhone: currentUser.phone?.substring(2) ?? '',
        customerName: `${currentUser?.firstName} ${currentUser.lastName}`,
        customerId: customer.id,
      }),
      orderNote: `Payment for Order #${order.id} - Thank you for shopping with Ecomdukes!`,
      orderAmount: Number(order.payableAmount),
      orderCurrency: DEFAULT_CURRENCY,
      orderId: order.id,
      orderSplits: [],
      // orderSplits: await this.getSplitInfo(order),
    });
    const pgOrder = await this.paymentProxy.createOrder(body, this.token);
    await this.orderProxy.updateById(order.id, {
      orderReferenceId: pgOrder.cf_order_id,
    });
    return pgOrder;
  }

  async decorateOrderWithExtras(
    order: OrderWithRelations,
    hasCustomer: boolean,
  ): Promise<OrderWithRelations> {
    // 1. Add previewUrl for each item
    if (order.orderLineItems?.length) {
      order.orderLineItems = order.orderLineItems.map(
        (item: OrderLineItemWithRelations) => {
          const productVariant = item.productVariant;
          const featuredAsset = productVariant?.featuredAsset;
          if (featuredAsset?.preview && process.env.CDN_ORIGIN) {
            featuredAsset.previewUrl = `${process.env.CDN_ORIGIN}/${featuredAsset.preview}`;
          }
          return item;
        },
      );
    }

    if (hasCustomer && order.customerId) {
      const customerFilter: FilterExcludingWhere<CustomerDto> = {
        include: [
          {
            relation: 'userTenant',
            scope: {
              include: [
                {
                  relation: 'user',
                  scope: {
                    fields: ['firstName', 'lastName', 'email'],
                  },
                },
              ],
              fields: ['id'],
            },
          },
        ],
        fields: ['id'],
      };

      const customer = await this.customerProxy.findById(
        order.customerId,
        customerFilter,
      );

      order.customer = customer as unknown as Customer;
    }

    return order;
  }

  extractAndRemoveCustomerRelation<T extends object>(
    filter?: FilterExcludingWhere<T>,
  ): {
    cleanedFilter: FilterExcludingWhere<T> | undefined;
    hasCustomer: boolean;
  } {
    if (!filter?.include) return {cleanedFilter: filter, hasCustomer: false};

    let hasCustomer = false;

    const cleanedInclude = filter.include.filter(rel => {
      if (typeof rel === 'string') {
        if (rel === 'customer') {
          hasCustomer = true;
          return false;
        }
        return true;
      }
      if (rel.relation === 'customer') {
        hasCustomer = true;
        return false;
      }
      return true;
    });

    const cleanedFilter = {
      ...filter,
      include: cleanedInclude.length > 0 ? cleanedInclude : undefined,
    };

    return {cleanedFilter, hasCustomer};
  }

  async getSplitInfo(order: Order): Promise<CashFreeOrderSplit[]> {
    const orderItems = await this.orderLineItemProxy.find({
      where: {orderId: order.id},
      fields: ['id', 'productVariantId', 'quantity', 'totalPrice', 'sellerId'],
    });
    const splitPromises = orderItems.map(item =>
      this.calculateSplitAmount(order, item),
    );
    const orderSplits: CashFreeOrderSplit[] = await Promise.all(splitPromises);

    return orderSplits;
  }

  private async calculateSplitAmount(
    order: Order,
    item: OrderLineItem,
  ): Promise<CashFreeOrderSplit> {
    const hardcodedEcomdukesPercentage = 0.02;
    let splitAmount = item.totalPrice;
    const seller = await this.sellerProxy.findById(item.sellerId, {
      fields: {id: true, vendorId: true, userTenantId: true},
      include: [],
    });
    const commision = item.totalPrice * hardcodedEcomdukesPercentage;
    splitAmount -= commision;
    return new CashFreeOrderSplit({
      vendorId: seller.vendorId,
      amount: splitAmount,
      tags: {
        productId: item.productVariantId,
        orderId: order.id,
        orderDisplayId: order.orderId,
      },
    });
  }
}
