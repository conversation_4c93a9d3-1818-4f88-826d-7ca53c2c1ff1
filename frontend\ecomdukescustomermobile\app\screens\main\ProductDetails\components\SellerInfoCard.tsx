import React from 'react';
import {View, Text, Image, StyleSheet, useWindowDimensions} from 'react-native';
import {<PERSON><PERSON>, Card} from 'react-native-paper';
import customColors from '../../../../theme/customColors';
import {colors} from '../../../../theme/colors';
import RenderHTML from 'react-native-render-html';

type SellerInfoProps = {
  sellerName: string;
  role: string;
  soldCount: number;
  profileImageUrl: string;
  badgeText?: string;
  disclaimer?: string;
  onMessagePress?: () => void;
  onReturnPolicyPress?: () => void;
  onTermsPress?: () => void;
  firstName?: string;
  lastName?: string;
};

const SellerInfoCard: React.FC<SellerInfoProps> = ({
  sellerName,
  role,
  soldCount,
  profileImageUrl,
  badgeText = '',
  onMessagePress,
  firstName,
  lastName,
}) => {
  const {width} = useWindowDimensions();
  const getInitials = (firstName?: string, lastName?: string) => {
    const first = firstName?.trim()?.charAt(0)?.toUpperCase() ?? '';
    const last = lastName?.trim()?.charAt(0)?.toUpperCase() ?? '';
    return first + last;
  };

  return (
    <Card style={styles.container}>
      <View style={styles.header}>
        {typeof profileImageUrl === 'string' &&
        profileImageUrl.trim() !== '' &&
        profileImageUrl !== 'null' ? (
          <Image source={{uri: profileImageUrl}} style={styles.avatar} />
        ) : (
          <View style={styles.initialsContainer}>
            <Text style={styles.initialsText}>
              {getInitials(firstName, lastName)}
            </Text>
          </View>
        )}
        <View style={styles.info}>
          <Text style={styles.name}>{sellerName}</Text>
          <Text style={styles.role}>{role}</Text>
          <Text style={styles.sold}>Sold: {soldCount} Products</Text>
        </View>
        <View style={styles.actions}>
          {badgeText ? (
            <RenderHTML
              contentWidth={width}
              source={{html: `<div>${badgeText}</div>`}}
            />
          ) : (
            <Text style={styles.badge}>No badge</Text>
          )}
          <Button
            mode="contained"
            style={styles.messageButton}
            onPress={onMessagePress}>
            Message
          </Button>
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    marginTop: 40,
    padding: 28,
    borderRadius: 12,
    backgroundColor: colors.gray.backGround,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
  },
  info: {
    marginLeft: 12,
    flex: 1,
  },
  name: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  role: {
    fontSize: 14,
    color: customColors.textBlack,
  },
  sold: {
    fontSize: 13,
    color: colors.gray.text,
  },
  actions: {
    alignItems: 'flex-end',
  },
  badge: {
    color: colors.tertiary,
    fontWeight: 'bold',
    fontSize: 12,
    marginBottom: 8,
    alignSelf: 'center',
  },
  messageButton: {
    backgroundColor: colors.tertiary,
    borderRadius: 20,
  },
  disclaimerTitle: {
    marginTop: 20,
    fontWeight: 'bold',
    fontSize: 15,
    color: colors.tertiary,
  },
  disclaimerCard: {
    marginTop: 8,
    backgroundColor: customColors.white,
    padding: 12,
    borderRadius: 12,
  },
  disclaimerText: {
    fontSize: 13,
    color: colors.gray.text,
    lineHeight: 20,
  },

  initialsContainer: {
    width: 40,
    height: 40,
    borderRadius: 30,
    backgroundColor: colors.tertiary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  initialsText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: customColors.white,
  },
});

export default SellerInfoCard;
