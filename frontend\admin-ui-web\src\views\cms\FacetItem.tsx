import { Grid, TextField, Button, Box, Autocomplete } from '@mui/material';
import MainCard from 'components/MainCard';
import { useFormik } from 'formik';
import { Dispatch, FC, SetStateAction, useEffect, useState, useCallback } from 'react';
import { SectionItem } from 'types/cms';
import SingleFileUpload from 'components/third-party/dropzone/SingleFileUpload';
import { Save2 as Save } from 'iconsax-react';
import debounce from 'lodash/debounce';
import { useLazyGetFacetsQuery } from 'redux/app/facet/facetApiSlice';
import { facetItemSchema } from '../../../validations/cms';
import { fieldsExcludeMetaFields } from 'constants/shared';

interface Props {
  data: SectionItem;
  onChange: (value: SectionItem) => void;
  setFileMap: Dispatch<SetStateAction<Record<string, File>>>;
  fileMap: Record<string, File>;
}

export const FacetItem: FC<Props> = ({ data, onChange, setFileMap, fileMap }) => {
  const [localFileMap, setLocalFileMap] = useState<Record<string, File>>({ ...fileMap });
  const [isSaved, setIsSaved] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [facetOptions, setFacetOptions] = useState<any[]>([]);
  const [getFacetValues, { isLoading }] = useLazyGetFacetsQuery();

  const formik = useFormik<SectionItem>({
    initialValues: data,
    enableReinitialize: true,
    validationSchema: facetItemSchema,
    onSubmit: (values) => {
      onChange(values);
      setIsSaved(true);
      setFileMap({ ...localFileMap });
    }
  });

  const handleLocalChange = (field: string, value: any) => {
    formik.setFieldValue(field, value);
    setIsSaved(false);
  };

  const handleSave = () => {
    formik.handleSubmit();
  };

  const debouncedSearch = useCallback(
    debounce(async (term: string) => {
      if (term.length < 2) return;

      try {
        const result = await getFacetValues({
          fields: fieldsExcludeMetaFields,
          where: { name: { ilike: `%${term}%` } },
          include: [
            {
              relation: 'facetValues',
              scope: {
                fields: fieldsExcludeMetaFields
              }
            }
          ],
          limit: 20
        }).unwrap();

        const flatFacetValues = result.flatMap((facet: any) =>
          (facet.facetValues || []).map((value: any) => ({
            id: value.id,
            name: `${facet.name} ${value.name}`
          }))
        );

        setFacetOptions(flatFacetValues);
      } catch (error) {
        console.error('Error fetching facet values:', error);
      }
    }, 500),
    [getFacetValues]
  );

  useEffect(() => {
    const fetchInitialFacetValues = async () => {
      const existingIds = formik.values.metadata?.facetValueIds;
      if (!existingIds || existingIds.length === 0) return;

      try {
        const result = await getFacetValues({
          fields: fieldsExcludeMetaFields,
          include: [
            {
              relation: 'facetValues',
              scope: {
                fields: fieldsExcludeMetaFields,
                where: {
                  id: { inq: existingIds }
                }
              },
              required: true
            }
          ],
          limit: 100
        }).unwrap();

        const flatFacetValues = result.flatMap((facet: any) =>
          (facet.facetValues || []).map((value: any) => ({
            id: value.id,
            name: `${facet.name} ${value.name}`
          }))
        );

        // Only set options if some of the initial IDs exist in the result
        const filtered = flatFacetValues.filter((opt) => existingIds.includes(opt.id));
        if (filtered.length > 0) {
          setFacetOptions(flatFacetValues);
        }
      } catch (error) {
        console.error('Error loading initial facet values:', error);
      }
    };

    fetchInitialFacetValues();
  }, []);

  useEffect(() => {
    if (searchTerm) {
      debouncedSearch(searchTerm);
    }

    return () => {
      debouncedSearch.cancel();
    };
  }, [searchTerm, debouncedSearch]);

  useEffect(() => {
    if (Object.keys(formik.errors).length > 0) {
      console.log('Formik validation errors:', formik.errors);
    }
  }, [formik.errors]);

  return (
    <MainCard>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <SingleFileUpload
            setFieldValue={(field: string, value: any): void => {
              handleLocalChange('imageUrl', value?.preview ?? '');
              setLocalFileMap({ ...fileMap, [data.id ?? '']: value });
            }}
            accept={{ 'image/*': ['.png', '.jpg', '.jpeg'] }}
            file={localFileMap[data.id ?? '']}
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            name="title"
            label="Title"
            value={formik.values.title || ''}
            onChange={(e) => handleLocalChange('title', e.target.value)}
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            name="subtitle"
            label="Subtitle (Optional)"
            value={formik.values.subtitle || ''}
            onChange={(e) => handleLocalChange('subtitle', e.target.value)}
          />
        </Grid>

        <Grid item xs={12}>
          <Autocomplete
            multiple
            loading={isLoading}
            options={facetOptions}
            getOptionLabel={(option) => option.name ?? ''}
            filterOptions={(x) => x} // disables local filtering since you're using server-side
            value={(formik.values.metadata?.facetValueIds || []).map(
              (id) => facetOptions.find((opt) => opt.id === id) || { id, name: 'Loading...' }
            )}
            onChange={(_, newValues) => {
              const selectedIds = newValues.map((val) => val.id);
              handleLocalChange('metadata', {
                ...formik.values.metadata,
                facetValueIds: selectedIds
              });
              handleLocalChange('entityType', 'facet-values');
            }}
            onInputChange={(_, newInputValue, reason) => {
              if (reason === 'input') {
                setSearchTerm(newInputValue);
              }
            }}
            inputValue={searchTerm}
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Facet Values"
                placeholder="Search facet values"
                helperText="You can select multiple facet values"
              />
            )}
          />
        </Grid>

        <Grid item xs={12}>
          <Box display="flex" justifyContent="flex-end">
            <Button
              variant="contained"
              color="primary"
              startIcon={<Save />}
              onClick={handleSave}
              disabled={isSaved || !formik.dirty || !formik.isValid}
            >
              {isSaved ? 'Saved' : 'Save'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </MainCard>
  );
};
