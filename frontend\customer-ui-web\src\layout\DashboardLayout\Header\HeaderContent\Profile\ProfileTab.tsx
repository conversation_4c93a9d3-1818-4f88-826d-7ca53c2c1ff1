import {useState, MouseEvent} from 'react';

// material-ui
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import SwitchAccountIcon from '@mui/icons-material/SwitchAccount';
// assets
import {Card, Logout} from 'iconsax-react';
import {useRouter} from 'next/navigation';

// ==============================|| HEADER PROFILE - PROFILE TAB ||============================== //

interface Props {
  handleLogout: () => void;
}

export default function ProfileTab({handleLogout}: Props) {
  const [selectedIndex, setSelectedIndex] = useState<number>();
  const handleListItemClick = (
    event: MouseEvent<HTMLDivElement>,
    index: number,
  ) => {
    setSelectedIndex(index);
  };
  const router = useRouter();

  return (
    <List
      component="nav"
      sx={{p: 0, '& .MuiListItemIcon-root': {minWidth: 32}}}
    >
      <ListItemButton
        selected={selectedIndex === 0}
        onClick={(event: MouseEvent<HTMLDivElement>) => {
          handleListItemClick(event, 1);
          router.push(`/orders`);
        }}
      >
        <ListItemIcon>
          <Card variant="Bulk" size={18} />
        </ListItemIcon>
        <ListItemText primary=" My Order" />
      </ListItemButton>

      <ListItemButton selected={selectedIndex === 1} onClick={handleLogout}>
        <ListItemIcon>
          <Logout variant="Bulk" size={18} />
        </ListItemIcon>
        <ListItemText primary="Logout" />
      </ListItemButton>
      <ListItemButton
        selected={selectedIndex === 2}
        onClick={(event: MouseEvent<HTMLDivElement>) => {
          handleListItemClick(event, 2);
          router.push(`/switch-account`);
        }}
      >
        {' '}
        <ListItemIcon>
          <SwitchAccountIcon fontSize="small" />{' '}
        </ListItemIcon>
        <ListItemText primary="Switch Account" />
      </ListItemButton>
    </List>
  );
}
