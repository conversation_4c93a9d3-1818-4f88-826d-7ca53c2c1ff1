import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>r,
  Grid,
  TextField,
  Button,
  Box,
  IconButton,
  Switch,
  FormControlLabel,
  MenuItem,
  InputAdornment
} from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useRouter } from 'next/navigation';
import { AddCircle, Trash } from 'iconsax-react';
import { CreateDiscountDto } from 'types/discount';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { useCreateDiscountMutation, useUpdateDiscountMutation } from 'redux/app/discount/discountApiSlice';

interface DiscountFormProps {
  initialValues?: {
    id?: string;
    name: string;
    description: string;
    isActive: boolean;
    startDate: string;
    endDate: string;
    usageLimitPerUser: number;
    combinable: boolean;
    conditions: DiscountCondition[];
  };
  onSubmit?: (values: DiscountFormValues) => Promise<void>;
  isEditMode?: boolean;
}

interface DiscountCondition {
  conditionType: 'BASIC' | 'ADDITIONAL' | 'APP_ONLY';
  thresholdAmount: number;
  discountType: 'FLAT' | 'PERCENT';
  discountValue: number;
  isAppOnly?: boolean;
}

interface DiscountFormValues {
  name: string;
  description: string;
  isActive: boolean;
  startDate: string;
  endDate: string;
  usageLimitPerUser: number;
  combinable: boolean;
  conditions: DiscountCondition[];
}

const DiscountForm: React.FC<DiscountFormProps> = ({ initialValues, onSubmit, isEditMode = false }) => {
  const router = useRouter();
  const [updateDiscount] = useUpdateDiscountMutation();
  const [createDiscount] = useCreateDiscountMutation();
  const validationSchema = Yup.object().shape({
    name: Yup.string().required('Discount name is required'),
    description: Yup.string().optional(),
    isActive: Yup.boolean().required(),
    startDate: Yup.date().required('Start date is required'),
    endDate: Yup.date().required('End date is required').min(Yup.ref('startDate'), 'End date must be after start date'),
    usageLimitPerUser: Yup.number().min(1, 'Must be at least 1').required('Usage limit is required'),
    combinable: Yup.boolean().required(),
    conditions: Yup.array()
      .of(
        Yup.object().shape({
          conditionType: Yup.string().required(),
          thresholdAmount: Yup.number().min(0, 'Amount must be positive').required('Threshold is required'),
          discountType: Yup.string().required(),
          discountValue: Yup.number()
            .min(0, 'Value must be positive')
            .when('discountType', {
              is: 'PERCENT',
              then: (schema) => schema.max(100, 'Percentage must be ≤ 100')
            })
            .required('Discount value is required')
        })
      )
      .test('condition-order', 'Thresholds must be in ascending order', function (conditions) {
        if (!conditions) return true;

        const basic = conditions.find((c) => c.conditionType === 'BASIC');
        const additional = conditions.find((c) => c.conditionType === 'ADDITIONAL');

        if (basic && additional && additional.thresholdAmount <= basic.thresholdAmount) {
          return this.createError({
            path: 'conditions',
            message: 'Additional discount threshold must be higher than basic threshold'
          });
        }

        return true;
      })
  });

  const formik = useFormik<DiscountFormValues>({
    initialValues: {
      name: initialValues?.name ?? '',
      description: initialValues?.description ?? '',
      isActive: initialValues?.isActive ?? true,
      combinable: initialValues?.combinable ?? false,
      startDate: initialValues?.startDate ? initialValues.startDate.split('T')[0] : new Date().toISOString().split('T')[0],
      endDate: initialValues?.endDate
        ? initialValues.endDate.split('T')[0]
        : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      usageLimitPerUser: initialValues?.usageLimitPerUser ?? 1,
      conditions: initialValues?.conditions ?? [
        {
          conditionType: 'BASIC',
          thresholdAmount: 0,
          discountType: 'FLAT',
          discountValue: 0,
          isAppOnly: false
        }
      ]
    },
    validationSchema,
    onSubmit: async (values) => {
      if (onSubmit) {
        await onSubmit(values);
      } else {
        const startDate = new Date(values.startDate);
        const endDate = new Date(values.endDate);

        const discountData: CreateDiscountDto = {
          name: values.name,
          description: values.description || undefined,
          isActive: values.isActive,
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          usageLimitPerUser: values.usageLimitPerUser,
          combinable: values.combinable,
          conditions: values.conditions.map((condition) => ({
            thresholdAmount: condition.thresholdAmount,
            discountType: condition.discountType,
            discountValue: condition.discountValue,
            conditionType: condition.conditionType,
            isAppOnly: condition.conditionType === 'APP_ONLY' ? true : false
          }))
        };

        if (isEditMode && initialValues?.id) {
          await updateDiscount({
            id: initialValues.id,
            data: discountData
          }).unwrap();
          openSnackbar({
            open: true,
            message: 'Discount updated successfully',
            variant: 'alert',
            alert: { color: 'success' }
          } as SnackbarProps);
        } else {
          await createDiscount(discountData).unwrap();
          openSnackbar({
            open: true,
            message: 'Discount created successfully',
            variant: 'alert',
            alert: { color: 'success' }
          } as SnackbarProps);
        }
        router.push('/discount');
      }
    }
  });

  const addCondition = (type: 'BASIC' | 'ADDITIONAL' | 'APP_ONLY') => {
    const newCondition: DiscountCondition = {
      conditionType: type,
      thresholdAmount: 0,
      discountType: 'FLAT',
      discountValue: 0,
      isAppOnly: type === 'APP_ONLY'
    };

    formik.setFieldValue('conditions', [...formik.values.conditions, newCondition]);
  };

  const removeCondition = (index: number) => {
    const updated = [...formik.values.conditions];
    updated.splice(index, 1);
    formik.setFieldValue('conditions', updated);
  };

  const hasConditionType = (type: string) => {
    return formik.values.conditions.some((c) => c.conditionType === type);
  };

  return (
    <Card sx={{ width: 'auto', mx: 'auto', mt: 4, p: 3, boxShadow: 3 }}>
      <Typography variant="h5" gutterBottom>
        {isEditMode ? 'Edit Discount' : 'Create Buyer Discount'}
      </Typography>
      <Divider sx={{ mb: 3 }} />

      <CardContent>
        <form onSubmit={formik.handleSubmit}>
          <Grid container spacing={3}>
            {/* Basic Information */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Discount Name"
                name="name"
                value={formik.values.name}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.name && Boolean(formik.errors.name)}
                helperText={formik.touched.name && formik.errors.name}
                InputProps={{ style: { borderRadius: '8px' } }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Description"
                name="description"
                value={formik.values.description}
                onChange={formik.handleChange}
                multiline
                rows={2}
                InputProps={{ style: { borderRadius: '8px' } }}
              />
            </Grid>

            {/* Date Range */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Start Date"
                type="date"
                name="startDate"
                value={formik.values.startDate}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.startDate && Boolean(formik.errors.startDate)}
                helperText={formik.touched.startDate && formik.errors.startDate}
                InputProps={{ style: { borderRadius: '8px' } }}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="End Date"
                type="date"
                name="endDate"
                value={formik.values.endDate}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.endDate && Boolean(formik.errors.endDate)}
                helperText={formik.touched.endDate && formik.errors.endDate}
                InputProps={{ style: { borderRadius: '8px' } }}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            {/* Settings */}
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Usage Limit Per User"
                type="number"
                name="usageLimitPerUser"
                value={formik.values.usageLimitPerUser}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.usageLimitPerUser && Boolean(formik.errors.usageLimitPerUser)}
                helperText={formik.touched.usageLimitPerUser && formik.errors.usageLimitPerUser}
                InputProps={{
                  style: { borderRadius: '8px' },
                  inputProps: { min: 1 }
                }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={<Switch name="isActive" checked={formik.values.isActive} onChange={formik.handleChange} color="primary" />}
                label="Active"
                labelPlacement="start"
                sx={{ justifyContent: 'space-between', width: '100%', ml: 0 }}
              />
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControlLabel
                control={<Switch name="combinable" checked={formik.values.combinable} onChange={formik.handleChange} color="primary" />}
                label="Combinable with other offers"
                labelPlacement="start"
                sx={{ justifyContent: 'space-between', width: '100%', ml: 0 }}
              />
            </Grid>

            {/* Discount Conditions */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Discount Conditions
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Configure tiered discounts for first-time buyers
              </Typography>
            </Grid>

            {formik.values.conditions.map((condition, index) => (
              <React.Fragment key={index}>
                <Grid item xs={12}>
                  <Card variant="outlined" sx={{ p: 2, borderRadius: '8px' }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                      <Typography variant="subtitle1">
                        {condition.conditionType === 'BASIC' && 'Basic Discount'}
                        {condition.conditionType === 'ADDITIONAL' && 'Additional Discount'}
                        {condition.conditionType === 'APP_ONLY' && 'App Exclusive Discount'}
                      </Typography>
                      {index > 0 && (
                        <IconButton onClick={() => removeCondition(index)} color="error">
                          <Trash size={18} />
                        </IconButton>
                      )}
                    </Box>

                    <Grid container spacing={2}>
                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          label="Minimum Cart Value"
                          type="number"
                          name={`conditions[${index}].thresholdAmount`}
                          value={condition.thresholdAmount}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          error={
                            formik.touched.conditions?.[index]?.thresholdAmount &&
                            Boolean((formik.errors.conditions as any)?.[index]?.thresholdAmount)
                          }
                          helperText={
                            formik.touched.conditions?.[index]?.thresholdAmount &&
                            (formik.errors.conditions as any)?.[index]?.thresholdAmount
                          }
                          InputProps={{
                            style: { borderRadius: '8px' },
                            startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                            inputProps: { min: 0 }
                          }}
                        />
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          select
                          label="Discount Type"
                          name={`conditions[${index}].discountType`}
                          value={condition.discountType}
                          onChange={formik.handleChange}
                          InputProps={{ style: { borderRadius: '8px' } }}
                        >
                          <MenuItem value="FLAT">Flat Amount</MenuItem>
                          <MenuItem value="PERCENT">Percentage</MenuItem>
                        </TextField>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          label="Discount Value"
                          type="number"
                          name={`conditions[${index}].discountValue`}
                          value={condition.discountValue}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          error={
                            formik.touched.conditions?.[index]?.discountValue &&
                            Boolean((formik.errors.conditions as any)?.[index]?.discountValue)
                          }
                          helperText={
                            formik.touched.conditions?.[index]?.discountValue && (formik.errors.conditions as any)?.[index]?.discountValue
                          }
                          InputProps={{
                            style: { borderRadius: '8px' },
                            endAdornment: (
                              <InputAdornment position="end">{condition.discountType === 'PERCENT' ? '%' : '₹'}</InputAdornment>
                            ),
                            inputProps: { min: 0 }
                          }}
                        />
                      </Grid>

                      {condition.conditionType === 'APP_ONLY' && (
                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Switch
                                name={`conditions[${index}].isAppOnly`}
                                checked={condition.isAppOnly}
                                onChange={formik.handleChange}
                                color="primary"
                              />
                            }
                            label="App Exclusive (must be ordered via mobile app)"
                            labelPlacement="start"
                            sx={{ justifyContent: 'space-between', width: '100%', ml: 0 }}
                          />
                        </Grid>
                      )}
                    </Grid>
                  </Card>
                </Grid>
              </React.Fragment>
            ))}

            {/* Add Condition Buttons */}
            <Grid item xs={12}>
              <Box display="flex" gap={2} flexWrap="wrap">
                {!hasConditionType('BASIC') && (
                  <Button variant="outlined" startIcon={<AddCircle size={16} />} onClick={() => addCondition('BASIC')}>
                    Add Basic Discount
                  </Button>
                )}
                {!hasConditionType('ADDITIONAL') && (
                  <Button variant="outlined" startIcon={<AddCircle size={16} />} onClick={() => addCondition('ADDITIONAL')}>
                    Add Additional Discount
                  </Button>
                )}
                {!hasConditionType('APP_ONLY') && (
                  <Button variant="outlined" startIcon={<AddCircle size={16} />} onClick={() => addCondition('APP_ONLY')}>
                    Add App Exclusive Discount
                  </Button>
                )}
              </Box>
            </Grid>

            {/* Form Actions */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
                <Button variant="outlined" onClick={() => router.push('/discounts')}>
                  Cancel
                </Button>
                <Button type="submit" variant="contained" color="primary">
                  {isEditMode ? 'Update Discount' : 'Create Discount'}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </CardContent>
    </Card>
  );
};

export default DiscountForm;
