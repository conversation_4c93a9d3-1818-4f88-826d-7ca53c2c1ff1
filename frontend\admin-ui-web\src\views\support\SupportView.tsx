import React, { useEffect } from 'react';
import { Card, CardContent, Typography, Box, Grid } from '@mui/material';
import { FaqStatus } from 'enums/faq-status.enum';
import { FaqVisibility } from 'enums/faq-visibility.enum';
import { useGetSupportByIdQuery } from 'redux/app/contents/support/supportApiSlice';

const formatEnumKey = (key: string) => {
  return key
    .split('_')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

function ViewSupport({ supportId }: { supportId: string }) {
  const { data: supportData, isLoading, error, refetch } = useGetSupportByIdQuery({ id: supportId });

  useEffect(() => {
    refetch();
  }, []);

  if (isLoading) return <Typography>Loading...</Typography>;
  if (error || !supportData) return <Typography>Error fetching Support</Typography>;

  const getFormattedEnumValue = (enumObj: any, value: number) => {
    const enumKey = Object.keys(enumObj).find((key) => enumObj[key] === value);
    return enumKey ? formatEnumKey(enumKey) : 'Unknown';
  };

  return (
    <Grid item xs={12} md={8}>
      <Card sx={{ mb: 2, boxShadow: 3, width: '100%' }}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={2} mb={2} width="100%">
            <Typography variant="h4">Customer Service Details</Typography>
          </Box>

          <Box sx={{ mt: 1, width: '100%' }}>
            <Typography fontWeight={600}>Status:</Typography>
            <Box
              sx={{
                p: 1,
                backgroundColor: '#f5f5f5',
                borderRadius: 1,
                width: '100%'
              }}
            >
              {getFormattedEnumValue(FaqStatus, supportData.status!)}
            </Box>
          </Box>

          <Box sx={{ mt: 1, width: '100%' }}>
            <Typography fontWeight={600}>Visibility:</Typography>
            <Box
              sx={{
                p: 1,
                borderRadius: 1,
                backgroundColor: '#f5f5f5',
                width: '100%'
              }}
            >
              {getFormattedEnumValue(FaqVisibility, supportData.visibility!)}
            </Box>
          </Box>

          <Box sx={{ mt: 2, width: '100%' }}>
            <Typography fontWeight={600}>Support Email:</Typography>
            <Box
              sx={{
                p: 1,
                borderRadius: 1,
                backgroundColor: '#f5f5f5',
                width: '100%'
              }}
            >
              {supportData.supportEmail}
            </Box>
          </Box>

          <Box sx={{ mt: 2, width: '100%' }}>
            <Typography fontWeight={600}>Phone:</Typography>
            <Box
              sx={{
                p: 1,
                borderRadius: 1,
                backgroundColor: '#f5f5f5',
                width: '100%'
              }}
            >
              {supportData?.supportPhone ? `+91${supportData.supportPhone}` : 'N/A'}
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Grid>
  );
}

export default ViewSupport;
