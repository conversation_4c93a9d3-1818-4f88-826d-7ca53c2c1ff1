'use client';

// material-ui
import Grid from '@mui/material/Grid';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { Card, CardContent, ListItemIcon, ListItemText } from '@mui/material';
import { List } from '@mui/material';
import { ListItem } from '@mui/material';

// ==============================|| LANDING - APPS PAGE ||============================== //

export default function PricingInfoPage() {
  return (
    <Container maxWidth="lg">
      <Grid container sx={{ mt: '15vh' }} spacing={4}>
        <Grid item xs={12}>
          <Typography
            variant="h4"
            sx={{
              fontWeight: 'bold',
              color: '#000050',
              textAlign: 'center',
              fontSize: { xs: '1.5rem', sm: '1.8rem', md: '2.2rem' },
              mb: 4
            }}
          >
            EcomDukes marketplace fee structure
          </Typography>
        </Grid>

        <Grid container item xs={12} spacing={3} justifyContent="space-between">
          {[
            {
              charges: 'Commission Fee',
              description: '__% to __% commission fee on your orders depending on category and price '
            },
            {
              charges: 'Fixed Fee',
              description: 'Based on the Price of product Sold,a very small amount from __ to __on all orders'
            },
            {
              charges: 'Delivery Fee',
              description: 'Delivery fee starts at __ and varies according to distance and products weight and '
            },
            {
              charges: 'Payment Collection Fee',
              description: '___ % of Order Value - for online Payments. __% or Rs __, whichever is higher, for COD'
            },
            {
              charges: 'Other Fee',
              description: 'Fee Charged only when optional support service or certain store plans are opted.'
            }
          ].map((item, index) => (
            <Grid item xs={12} sm={4} md={2.4} key={index}>
              <Box
                sx={{
                  border: '1px solid #000050',
                  borderRadius: 2,
                  p: { xs: 3, md: 5 },
                  height: '90%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  width: '100%'
                }}
              >
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 'bold',
                    color: '#000050',
                    fontSize: { xs: '1.1rem', sm: '1.3rem', md: '1.2rem' },
                    mb: 2
                  }}
                >
                  {item.charges}
                </Typography>
                <Typography
                  sx={{
                    fontSize: '0.95rem',
                    color: '#4A4A4A',
                    flexGrow: 1,
                    mb: 2
                  }}
                >
                  {item.description}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
        <Grid item xs={12}>
          <Typography
            variant="h6"
            sx={{
              color: '#4A4A4A',
              textAlign: 'center'
            }}
          >
            Transparent and No Hidden Fees
          </Typography>
        </Grid>
        <Grid item xs={12}>
          {' '}
          <Card
            sx={{
              backgroundColor: 'white',
              borderRadius: 3,
              boxShadow: 'none',
              width: '100%'
            }}
          >
            <CardContent sx={{ px: { xs: 2, md: 10 }, py: { xs: 3, md: 4 } }}>
              <Grid item xs={12}>
                <Typography
                  variant="h4"
                  sx={{
                    color: '#000050',
                    fontWeight: 'bold',
                    mb: 3,
                    textAlign: 'left',
                    fontSize: { xs: '1rem', sm: '1.2rem', md: '1.995rem' }
                  }}
                >
                  Shipping Rates{' '}
                </Typography>
              </Grid>

              <Grid item xs={12} sx={{ textAlign: 'left', mb: 4 }}>
                <List>
                  {[
                    'Shipping Rate Starts from___and varies by Distance,Weight and Dimension of Product',
                    'Chargeable weight would be volumetric or actual weight,whichever is higher (LxBxH(cms)/4000)  ',
                    'You will Get Live Shipping Rates in Your Seller Account'
                  ].map((item, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <Box sx={{ width: 12, height: 12, borderRadius: '50%', background: '#4A4A4A' }} />
                      </ListItemIcon>
                      <ListItemText primary={item} sx={{ fontSize: '1rem', color: '#4A4A4A' }} />
                    </ListItem>
                  ))}
                </List>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}
