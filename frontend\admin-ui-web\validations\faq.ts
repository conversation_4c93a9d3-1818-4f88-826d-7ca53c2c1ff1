import * as Yup from 'yup';
import { EditorState, convertToRaw } from 'draft-js';
import { FaqVisibility } from 'enums/faq-visibility.enum';
import { FaqStatus } from 'enums/faq-status.enum';

export const faqValidationSchema = Yup.object({
  question: Yup.string().required('Question is required'),

  answer: Yup.mixed<EditorState>().test(
    'is-not-empty',
    'Answer is required',
    (value) => value instanceof EditorState && convertToRaw(value.getCurrentContent()).blocks.some((block) => block.text.trim() !== '')
  ),

  priority: Yup.number().required('Priority is required'),

  category: Yup.string().required('Category is required'),

  visibility: Yup.mixed<FaqVisibility>().required('Visibility is required'),

  status: Yup.mixed<FaqStatus>().required('Status is required')
});

export const helpValidationSchema = Yup.object({
  question: Yup.string().required('Question is required'),

  answer: Yup.mixed<EditorState>().test(
    'is-not-empty',
    'Answer is required',
    (value) => value instanceof EditorState && convertToRaw(value.getCurrentContent()).blocks.some((block) => block.text.trim() !== '')
  ),

  category: Yup.string().required('Category is required'),

  visibility: Yup.mixed<FaqVisibility>().required('Visibility is required'),

  status: Yup.mixed<FaqStatus>().required('Status is required')
});
export const supportValidationSchema = Yup.object({
  supportEmail: Yup.string().email('Invalid email address').required('Support email is required'),

  supportPhone: Yup.string()
    .matches(/^\d{10}$/, 'Phone must be 10 digits')
    .required('Phone number is required'),

  visibility: Yup.mixed<FaqVisibility>().required('Visibility is required'),

  status: Yup.mixed<FaqStatus>().required('Status is required')
});
