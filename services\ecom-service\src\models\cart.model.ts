import {CartStatus} from '@local/core';
import {model, property, hasMany, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {CartItem} from './cart-item.model';
import {PromoCode} from './promo-code.model';

@model({name: 'carts'})
export class Cart extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  id: string;

  @property({
    type: 'string',
    required: true,
    name: 'customer_id',
  })
  customerId: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      enum: Object.values(CartStatus),
    },
  })
  status: string;

  @hasMany(() => CartItem, {keyTo: 'cartId'})
  cartItems: CartItem[];

  @belongsTo(
    () => PromoCode,
    {keyTo: 'id'},
    {name: 'promo_code_id', type: 'string', jsonSchema: {nullable: true}},
  )
  promoCodeId?: string;

  @property({
    type: 'number',
    name: 'ecom_duke_coins_applied',
    default: 0,
  })
  ecomDukeCoinsApplied?: number;

  constructor(data?: Partial<Cart>) {
    super(data);
  }
}

export interface CartRelations {
  // describe navigational properties here
}

export type CartWithRelations = Cart & CartRelations;
