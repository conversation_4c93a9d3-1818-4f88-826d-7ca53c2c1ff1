import { apiSlice } from 'redux/apiSlice';
import { ApiSliceIdentifier } from 'enums/api.enum';
import { CreateDiscountDto, Discount } from 'types/discount';
import { buildFilterParams } from 'utils/buildFilterParams';
import { IFilter } from '../types/filter';

export const discountApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    createDiscount: builder.mutation<Discount, CreateDiscountDto>({
      query: (body) => ({
        url: '/discounts',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body
      })
    }),

    getDiscountCount: builder.query<{ count: number }, void>({
      query: () => ({
        url: '/discounts/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),

    getDiscountList: builder.query<
      Discount[],
      {
        limit?: number;
        skip?: number;
        order?: Array<Record<string, unknown> | string>;
        where?: Record<string, unknown>;
        fields?: Record<string, boolean>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ limit, skip, where, fields, include, order } = {}) => ({
        url: '/discounts',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            limit,
            offset: skip,
            where,
            order,
            fields,
            include
          })
        }
      })
    }),

    getDiscountById: builder.query<Discount, { id: string; filter?: IFilter }>({
      query: ({ id, filter }) => ({
        url: `/discounts/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter)
        }
      })
    }),

    updateDiscount: builder.mutation<void, { id: string; data: Partial<Discount> }>({
      query: ({ id, data }) => ({
        url: `/discounts/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),

    deleteDiscount: builder.mutation<void, { id: string }>({
      query: ({ id }) => ({
        url: `/discounts/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    })
  })
});

export const {
  useCreateDiscountMutation,
  useDeleteDiscountMutation,
  useGetDiscountByIdQuery,
  useGetDiscountListQuery,
  useGetDiscountCountQuery,
  useUpdateDiscountMutation
} = discountApiSlice;
