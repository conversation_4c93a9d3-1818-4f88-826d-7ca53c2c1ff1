import {model, property, belongsTo, hasMany} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {ShippingMethod} from './shipping-method.model';
import {SellerShippingCharge} from './seller-shipping-charge.model';
import {ProductShippingCharge} from './product-shipping-charge.model';
import {WeightBasedShippingRule} from './weight-based-shipping-rule.model';

@model({
  name: 'seller_shipping_profiles',
})
export class SellerShippingProfile extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'boolean',
    default: false,
    name: 'is_default',
  })
  isDefault?: boolean;

  @property({
    type: 'boolean',
    default: true,
    name: 'is_active',
  })
  isActive?: boolean;

  @property({
    type: 'string',
    required: true,
    name: 'seller_id',
  })
  sellerId: string;

  @belongsTo(() => ShippingMethod, {keyTo: 'id'}, {name: 'shipping_method_id'})
  shippingMethodId: string;

  @hasMany(() => SellerShippingCharge, {keyTo: 'shippingProfileId'})
  shippingCharges: SellerShippingCharge[];

  @hasMany(() => ProductShippingCharge, {keyTo: 'shippingProfileId'})
  productShippingCharges: ProductShippingCharge[];

  @hasMany(() => WeightBasedShippingRule, {keyTo: 'shippingProfileId'})
  weightBasedRules: WeightBasedShippingRule[];

  constructor(data?: Partial<SellerShippingProfile>) {
    super(data);
  }
}

export interface SellerShippingProfileRelations {
  shippingMethod?: ShippingMethod;
  shippingCharges?: SellerShippingCharge[];
  productShippingCharges?: ProductShippingCharge[];
  weightBasedRules?: WeightBasedShippingRule[];
}

export type SellerShippingProfileWithRelations = SellerShippingProfile &
  SellerShippingProfileRelations;
