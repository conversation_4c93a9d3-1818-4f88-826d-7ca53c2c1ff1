import { Button, CircularProgress, Grid, Typography } from '@mui/material';
import { useGetOrderCountQuery, useGetOrderQuery } from 'redux/app/order/orderApiSlice';
import { OutlinedInput, Box, InputAdornment } from '@mui/material';

// project-imports

import { DRAWER_WIDTH } from 'config';
import { SearchNormal1 } from 'iconsax-react';
import { useEffect, useState } from 'react';
import OrderListPage from './OrderListPage';
import { OrderItemStatus, StatusLabelMap } from 'enums/orderStatus';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from '../../utils/table-filter';
import { SortingState, ColumnFiltersState, PaginationState } from '@tanstack/react-table';
import { PermissionKeys } from 'enums/permission-key.enum';
import withPermission from 'hoc/withPermission';

const Orders = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
  const {
    data: orders = [],
    isLoading,
    isFetching,
    refetch
  } = useGetOrderQuery({
    filter: {
      order: convertSortingToLoopbackSort(sorting),
      where: {
        ...convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['name']),
        ...(selectedStatus ? { status: selectedStatus } : {})
      },
      include: [
        {
          relation: 'order',
          required: true,
          scope: {
            include: [{ relation: 'promoCode' }, { relation: 'shippingAddress' }, { relation: 'billingAddress' }]
          }
        },
        {
          relation: 'productVariant',
          required: true,
          scope: {
            where: { name: { ilike: `%${debouncedSearch}%` } },
            include: [{ relation: 'product' }, { relation: 'featuredAsset' }]
          }
        }
      ],
      ...convertPaginationToLoopback(pagination)
    }
  });
  const { data: orderCountData } = useGetOrderCountQuery();
  const totalRows = orderCountData?.count ?? 0;

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(searchQuery);
    }, 200);

    return () => clearTimeout(handler);
  }, [searchQuery]);
  return (
    <Box sx={{ display: 'flex', width: '100%' }}>
      <Box
        component="main"
        sx={{
          width: `calc(100% - ${DRAWER_WIDTH}px)`,
          flexGrow: 1,
          p: { xs: 1, sm: 3 }
        }}
      >
        <Grid container spacing={3} sx={{ p: 4 }}>
          <Grid item xs={12}>
            <Typography variant="h5">Orders</Typography>
          </Grid>
          <Grid item xs={12}>
            <OutlinedInput
              id="order"
              name="order"
              size="small"
              placeholder="Search for Orders"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              fullWidth
              endAdornment={
                <InputAdornment position="end">
                  <SearchNormal1 size="20" style={{ cursor: 'pointer', color: '#888' }} />
                </InputAdornment>
              }
            />
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, p: 1 }}>
              {Object.entries(OrderItemStatus).map(([label, value]) => (
                <Button
                  key={value}
                  variant={selectedStatus === value ? 'contained' : 'outlined'}
                  onClick={() => setSelectedStatus(value)}
                  sx={{
                    textTransform: 'none',
                    whiteSpace: 'nowrap',
                    minWidth: '150px',
                    bgcolor: selectedStatus === value ? '#EFF1F2' : 'transparent',
                    color: selectedStatus === value ? '#000' : '#888',
                    borderColor: selectedStatus === value ? '#EFF1F2' : '#ccc',
                    '&:hover': {
                      bgcolor: selectedStatus === value ? '#e1e4e6' : '#f5f5f5',
                      borderColor: '#ccc'
                    }
                  }}
                >
                  {StatusLabelMap[value] ?? label}
                </Button>
              ))}
            </Box>
          </Grid>
          {isLoading || isFetching ? (
            <Grid item xs={12} sx={{ textAlign: 'center', mt: 4 }}>
              <CircularProgress />
              <Typography variant="body2" sx={{ mt: 2 }}>
                Loading orders...
              </Typography>
            </Grid>
          ) : orders?.length === 0 ? (
            <Grid item xs={12} sx={{ textAlign: 'center', mt: 4 }}>
              <Typography variant="h4">No orders found.</Typography>
              <Typography variant="h6" color="text.secondary">
                Try a different search term or check back later.
              </Typography>
            </Grid>
          ) : (
            <Grid item xs={12}>
              <Grid container spacing={2}>
                <OrderListPage
                  data={orders}
                  loading={isLoading || isFetching}
                  refetch={refetch}
                  sorting={sorting}
                  setSorting={setSorting}
                  pagination={pagination}
                  setPagination={setPagination}
                  columnFilters={columnFilters}
                  setColumnFilters={setColumnFilters}
                  globalFilter={globalFilter}
                  setGlobalFilter={setGlobalFilter}
                  totalRows={totalRows}
                />
              </Grid>
            </Grid>
          )}
        </Grid>
      </Box>
    </Box>
  );
};
export default withPermission(PermissionKeys.ViewOrder)(Orders);
