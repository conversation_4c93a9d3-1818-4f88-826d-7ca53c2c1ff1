import { Grid, TextField, Button, Box, Autocomplete } from '@mui/material';
import MainCard from 'components/MainCard';
import { useFormik } from 'formik';
import { Dispatch, FC, SetStateAction, useEffect, useState, useCallback } from 'react';
import { SectionItem } from 'types/cms';
import { featuredCollectionItemSchema } from '../../../validations/cms';
import SingleFileUpload from 'components/third-party/dropzone/SingleFileUpload';
import { Save2 as Save } from 'iconsax-react';
import debounce from 'lodash/debounce';
import { useLazyGetCollectionsQuery } from 'redux/app/collections/collectionApiSlice';

interface Props {
  data: SectionItem;
  onChange: (value: SectionItem) => void;
  setFileMap: Dispatch<SetStateAction<Record<string, File>>>;
  fileMap: Record<string, File>;
}

export const FeaturedCollectionItem: FC<Props> = ({ data, onChange, setFileMap, fileMap }) => {
  const [localData, setLocalData] = useState<SectionItem>(data);
  const [searchTerm, setSearchTerm] = useState('');
  const [collectionOptions, setCollectionOptions] = useState<any[]>([]);
  const [getCollections, { isLoading }] = useLazyGetCollectionsQuery();
  const [localFileMap, setLocalFileMap] = useState<Record<string, File>>({ ...fileMap });
  const [isSaved, setIsSaved] = useState(true);

  // Update local data when props change
  useEffect(() => {
    setLocalData(data);
  }, [data]);

  useEffect(() => {
    setLocalFileMap({ ...fileMap });
  }, [fileMap]);

  // Create a debounced search function for collections
  const debouncedSearch = useCallback(
    debounce(async (term: string) => {
      if (term.length < 2) return;

      try {
        const result = await getCollections({
          where: { name: { ilike: `%${term}%` } },
          limit: 10
        }).unwrap();

        setCollectionOptions(result || []);
      } catch (error) {
        console.error('Error fetching collections:', error);
      }
    }, 500),
    [getCollections]
  );

  // Effect to trigger search when searchTerm changes
  useEffect(() => {
    if (searchTerm) {
      debouncedSearch(searchTerm);
    }

    // Cleanup function
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchTerm, debouncedSearch]);

  // Load selected collection on initial render
  useEffect(() => {
    const loadSelectedCollection = async () => {
      if (data.entityId) {
        const result = await getCollections({
          where: { id: data.entityId }
        }).unwrap();

        if (result && result.length > 0) {
          setCollectionOptions((prevOptions) => {
            // Avoid duplicates
            if (!prevOptions.some((p) => p.id === result[0].id)) {
              return [...prevOptions, result[0]];
            }
            return prevOptions;
          });
        }
      }
    };

    loadSelectedCollection();
  }, [data.entityId, getCollections]);

  const formik = useFormik<SectionItem>({
    initialValues: localData,
    enableReinitialize: true,
    validationSchema: featuredCollectionItemSchema,
    onSubmit: (values) => {
      onChange(values);
      setIsSaved(true);
      setFileMap({ ...localFileMap });
    }
  });

  const handleLocalChange = (field: string, value: any) => {
    formik.setFieldValue(field, value);
    setIsSaved(false);
  };

  const handleSave = () => {
    formik.handleSubmit();
  };

  return (
    <MainCard>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <SingleFileUpload
            setFieldValue={(field: string, value: any): void => {
              handleLocalChange('imageUrl', value?.preview ?? '');
              setLocalFileMap({ ...fileMap, [data.id ?? '']: value });
            }}
            accept={{ 'image/*': ['.png', '.jpg', '.jpeg'] }}
            file={localFileMap[data.id ?? '']}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="title"
            label="Collection Title"
            value={formik.values.title || ''}
            onChange={(e) => {
              handleLocalChange('title', e.target.value);
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="subtitle"
            label="Subtitle (Optional)"
            value={formik.values.subtitle || ''}
            onChange={(e) => {
              handleLocalChange('subtitle', e.target.value);
            }}
          />
        </Grid>
        <Grid item xs={12}>
          <Autocomplete
            loading={isLoading}
            options={collectionOptions}
            getOptionLabel={(option) => option.name}
            value={collectionOptions.find((c) => c.id === formik.values.entityId) || null}
            onChange={(_, newValue) => {
              handleLocalChange('entityId', newValue?.id || '');
              handleLocalChange('entityType', 'collection');
            }}
            onInputChange={(_, value) => setSearchTerm(value)}
            filterOptions={(x) => x} // Disable client-side filtering
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Collection"
                placeholder="Search collections"
                helperText="Type at least 2 characters to search"
              />
            )}
          />
        </Grid>
        <Grid item xs={12}>
          <Box display="flex" justifyContent="flex-end">
            <Button
              variant="contained"
              color="primary"
              startIcon={<Save />}
              onClick={handleSave}
              disabled={isSaved || !formik.dirty || !formik.isValid}
            >
              {isSaved ? 'Saved' : 'Save'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </MainCard>
  );
};
