/* eslint-disable @typescript-eslint/no-explicit-any */
import {injectable, BindingScope, inject} from '@loopback/core';
import {S3WithSigner} from 'loopback4-s3';
import {Request} from '@loopback/rest';
import multer from 'multer';
import {FileMetadata} from '../models/file-metadata.model';
import {IFileRequestMetadata, S3StorageOptions} from '@sourceloop/file-utils';
import {MulterS3Storage} from '@sourceloop/file-utils/s3';

@injectable({scope: BindingScope.TRANSIENT})
export class FileUploadService {
  constructor(
    @inject('services.S3WithSigner')
    private readonly s3Client: S3WithSigner,

    @inject('file.request.metadata')
    private readonly fileMetadata: IFileRequestMetadata,
  ) {}

  async handleFileUpload(request: Request): Promise<FileMetadata[]> {
    const multerStorage = new MulterS3Storage(
      this.s3Client,
      this.fileMetadata as IFileRequestMetadata<S3StorageOptions>,
    );

    const upload = multer({storage: multerStorage.value()}).any();

    await new Promise<void>((resolve, reject) => {
      const res = {} as any;
      upload(request, res, (err: any) => {
        if (err) reject(err);
        else resolve();
      });
    });

    const files = (request.files ?? []) as Express.Multer.File[];
    return files.map(file => this.toFileMetadata(file));
  }

  private toFileMetadata(file: Express.Multer.File): FileMetadata {
    return new FileMetadata({
      buffer: file.buffer?.toString('base64') ?? '',
      fieldname: file.fieldname,
      originalname: file.originalname,
      encoding: file.encoding,
      mimetype: file.mimetype,
      size: file.size,
      bucket: (file as any).bucket,
      key: (file as any).key,
      acl: (file as any).acl,
      contentType: (file as any).contentType,
      contentDisposition: (file as any).contentDisposition ?? null,
      contentEncoding: (file as any).contentEncoding ?? null,
      storageClass: (file as any).storageClass,
      serverSideEncryption: (file as any).serverSideEncryption ?? null,
      metadata: (file as any).metadata,
      location: (file as any).location,
      etag: (file as any).etag,
      versionId: (file as any).versionId ?? null,
    });
  }
}
