import {apiSlice} from '../apiSlice';
import {ApiSliceIdentifier} from '../../constants/enums';
import {Seller} from '../../types/seller';

export const chatApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getSellerById: builder.query<
      Seller,
      {id: string; include?: Array<Record<string, unknown> | string>}
    >({
      query: ({id, include}) => ({
        url: `/sellers/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            include: include || [],
          }),
        },
      }),
    }),
  }),
});

export const {useGetSellerByIdQuery} = chatApiSlice;
