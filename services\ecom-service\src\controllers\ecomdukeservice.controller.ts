import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
  repository,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';

import {Ecomdukeservice} from '../models';
import {EcomdukeserviceRepository} from '../repositories';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';

const basePath = '/ecomdukeservices';

export class EcomDukeServiceController {
  constructor(
    @repository(EcomdukeserviceRepository)
    public serviceRepo: EcomdukeserviceRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateEcomdukeService]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Ecomdukeservice model instance',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Ecomdukeservice)},
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Ecomdukeservice, {
            exclude: ['id'],
            title: 'NewEcomDukeService',
          }),
        },
      },
    })
    service: Omit<Ecomdukeservice, 'id'>,
  ): Promise<Ecomdukeservice> {
    return this.serviceRepo.create(service);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewEcomdukeService]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'Ecomdukeservice count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(Ecomdukeservice) where?: Where<Ecomdukeservice>,
  ): Promise<Count> {
    return this.serviceRepo.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewEcomdukeService]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Ecomdukeservice instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Ecomdukeservice, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Ecomdukeservice) filter?: Filter<Ecomdukeservice>,
  ): Promise<Ecomdukeservice[]> {
    return this.serviceRepo.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewEcomdukeService]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'Ecomdukeservice model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Ecomdukeservice, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Ecomdukeservice, {exclude: 'where'})
    filter?: FilterExcludingWhere<Ecomdukeservice>,
  ): Promise<Ecomdukeservice> {
    return this.serviceRepo.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateEcomdukeService]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Ecomdukeservice PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Ecomdukeservice, {partial: true}),
        },
      },
    })
    service: Partial<Ecomdukeservice>,
  ): Promise<void> {
    await this.serviceRepo.updateById(id, service);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteEcomdukeService]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Ecomdukeservice DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.serviceRepo.deleteById(id);
  }
}
