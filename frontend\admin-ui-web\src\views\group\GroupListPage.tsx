'use client';

import { MouseEvent, useCallback, useEffect, useMemo, useState } from 'react';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';
import IconButton from 'components/@extended/IconButton';
import { Eye, ArrowLeft, Add, Trash } from 'iconsax-react';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from 'utils/table-filter';
import Loader from 'components/Loader';
import withPermission from 'hoc/withPermission';
import { PermissionKeys } from 'enums/permission-keys.enum';
import { ThemeMode } from 'config';
import { Group } from 'types/group';
import { useGetGroupsCountQuery, useGetGroupsQuery } from 'redux/app/campaigns/groupApiSlice';
import GroupTable from './GroupTable';
import AlertGroupDelete from './AlertGroupDelete';
import moment from 'moment';

interface ApiGroup {
  id?: string;
  name: string;
  createdOn?: string;
}

const GroupListPage = () => {
  const theme = useTheme();

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
  const [selectedGroup, setSelectedGroup] = useState<Group | null>(null);
  const [open, setOpen] = useState<boolean>(false);
  const [groupDeleteid, setGroupDeleteId] = useState<string>('');
  const [groupDeleteTitle, setGroupDeleteTitle] = useState<string>('');

  const {
    data: groupList,
    isLoading: groupListLoading,
    refetch
  } = useGetGroupsQuery({
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['name']),
    ...convertPaginationToLoopback(pagination)
  });

  useEffect(() => {
    refetch();
  }, [refetch]);

  const trasnformGroupList = (list: ApiGroup[] = []): Group[] => {
    return list.map((item) => ({
      id: item.id ?? '',
      name: item.name,
      createdOn: item.createdOn
    }));
  };

  const { data: groupCount, isLoading: groupCountLoading } = useGetGroupsCountQuery({
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['subject', 'message'])
  });

  const handleClose = useCallback(() => setOpen((prev) => !prev), []);

  const columns = useMemo<ColumnDef<Group>[]>(
    () => [
      {
        header: 'Created On',
        accessorKey: 'createdOn',
        enableSorting: true,
        cell: ({ row }) => {
          return <Typography> {row.original?.createdOn ? moment(row.original.createdOn).format('DD MMM YYYY, hh:mm A') : '-'}</Typography>;
        }
      },
      {
        header: 'Name',
        accessorKey: 'name',
        enableSorting: true,
        cell: ({ row }) => <Typography>{row.original?.name ?? '-'}</Typography>
      },
      {
        header: 'Actions',
        meta: {
          className: 'cell-center'
        },
        enableSorting: false,
        cell: ({ row }) => {
          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add
                style={{
                  color: theme.palette.error.main,
                  transform: 'rotate(45deg)'
                }}
              />
            ) : (
              <Eye />
            );

          return (
            <Stack direction="row" alignItems="center" justifyContent="center" spacing={0}>
              <Tooltip title="View">
                <IconButton
                  color="secondary"
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  {collapseIcon}
                </IconButton>
              </Tooltip>
              <Tooltip title="Delete">
                <IconButton
                  color="error"
                  sx={{
                    ':hover': {
                      color: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.error[100]
                    }
                  }}
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    handleClose();
                    setGroupDeleteId(row.original.id as string);
                    setGroupDeleteTitle(row.original.name);
                  }}
                >
                  <Trash />
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    [theme]
  );

  return (
    <>
      {selectedGroup ? (
        <>
          <IconButton onClick={() => setSelectedGroup(null)} sx={{ mb: 2 }}>
            <ArrowLeft />
          </IconButton>
        </>
      ) : groupCountLoading || groupListLoading ? (
        <Loader />
      ) : (
        <GroupTable
          {...{
            data: trasnformGroupList(groupList || []),
            columns,
            setSorting,
            sorting,
            columnFilters,
            setColumnFilters,
            loading: groupListLoading,
            globalFilter,
            setGlobalFilter,
            pagination,
            setPagination,
            totalRows: groupCount?.count ?? 0,
            refetch
          }}
        />
      )}
      <AlertGroupDelete refetch={refetch} id={groupDeleteid} title={groupDeleteTitle} open={open} handleClose={handleClose} />
    </>
  );
};

export default withPermission(PermissionKeys.ViewNotification)(GroupListPage);
