import {RestOperationTemplate} from '@sourceloop/core';
import {Groups} from '../../models';

export type ListProxyType = {
  subscribeContactToList(
    listKey: string,
    contactInfo: {
      firstName: string;
      lastName: string;
      email: string;
    },
    topicId: string,
    token: string,
  ): Promise<Groups>;
};

export const ListProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'POST',
      url: `/lists/subscribe`,
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: {
        listKey: '{listKey}',
        contactInfo: '{contactInfo}',
        topicId: '{topicId}',
      },
      query: {},
    },
    functions: {
      subscribeContactToList: ['listKey', 'contactInfo', 'topicId', 'token'],
    },
  },
];
