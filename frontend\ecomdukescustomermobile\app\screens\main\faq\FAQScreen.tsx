import {<PERSON><PERSON><PERSON>, SafeAreaView, StyleSheet, View} from 'react-native';
import React, {useCallback, useMemo} from 'react';
import {ActivityIndicator, Text} from 'react-native-paper';
import styleConstants from '../../../theme/styleConstants';
import {colors} from '../../../theme/colors';
import FAQAccordion from '../../../components/accordion/FAQAccordion';
import customColors from '../../../theme/customColors';
import draftToHtml from 'draftjs-to-html';
import {useFocusEffect} from '@react-navigation/native';
import {useGetFaqsQuery} from '../../../redux/faq/faqApiSlice';
import {FAQScreenNavigationProp} from '../../../navigations/types';
interface ScreenProps {
  navigation: FAQScreenNavigationProp;
}

// Footer
const ListFooter = () => (
  <View style={styles.footerContainer}>
    <View style={styles.footerItem}>
      <Text variant="titleMedium" style={styles.footerLabel}>
        Mail Us:
      </Text>
      <Text variant="titleMedium" style={styles.footerValue}>
        <EMAIL>
      </Text>
    </View>

    <View style={styles.footerItem}>
      <Text variant="titleMedium" style={styles.footerLabel}>
        Contact Through:
      </Text>
      <Text variant="titleMedium" style={styles.footerValue}>
        988766557
      </Text>
    </View>
  </View>
);

const FAQScreen: React.FC<ScreenProps> = () => {
  const {data: faqs = [], isLoading, refetch} = useGetFaqsQuery();

  const sortedFaqs = useMemo(() => {
    return [...faqs].sort((a, b) => b.priority - a.priority);
  }, [faqs]);
  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch]),
  );
  return (
    <SafeAreaView style={styles.mainContainer}>
      {isLoading ? (
        <ActivityIndicator size="small" color={colors.primary} />
      ) : (
        <FlatList
          refreshing={isLoading}
          onRefresh={() => {
            refetch();
          }}
          data={sortedFaqs}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({item}) => {
            let formattedAnswer = item.answer;

            const parsedAnswer = JSON.parse(item.answer);
            formattedAnswer = draftToHtml(parsedAnswer);

            return (
              <FAQAccordion question={item.question} answer={formattedAnswer} />
            );
          }}
          ListHeaderComponent={
            <Text style={styles.headerText}>
              What are the issues you are facing?
            </Text>
          }
          ListFooterComponent={<ListFooter />}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </SafeAreaView>
  );
};

export default FAQScreen;

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.surface,
  },
  emailText: {
    color: colors.primary,
    fontWeight: 'bold',
  },
  footerContainer: {
    backgroundColor: colors.background,
    borderColor: colors.gray.light,
    borderTopWidth: 1,
    marginTop: styleConstants.spacing.x20,
    paddingHorizontal: styleConstants.spacing.x20,
    paddingVertical: styleConstants.spacing.x20,
  },

  footerItem: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: styleConstants.spacing.s10,
  },
  footerLabel: {
    color: customColors.textBlack,
    fontSize: 16,
    fontWeight: 'bold',
  },
  footerText: {
    color: 'black',
    fontSize: 16,
    marginBottom: 5,
  },
  footerValue: {
    color: colors.primary,
    fontSize: 16,
    marginTop: 2,
  },
  headerText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: styleConstants.spacing.s10,
  },
  listContainer: {
    backgroundColor: colors.background,
    flexGrow: 1,
    padding: styleConstants.spacing.x20,
  },
  phoneText: {
    color: colors.primary,
    fontWeight: 'bold',
  },
});
