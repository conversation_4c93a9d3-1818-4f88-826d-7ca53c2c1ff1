{"openapi": "3.0.0", "info": {"title": "notification-service", "version": "1.0.0", "description": "notification-service", "contact": {}}, "paths": {"/campaigns/count": {"get": {"x-controller-name": "CampaignController", "x-operation-name": "count", "tags": ["CampaignController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewCampaign   |\n", "responses": {"200": {"description": "Campaign model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "campaigns.Where<PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Campaign>"}}}}], "operationId": "CampaignController.count"}}, "/campaigns/send": {"post": {"x-controller-name": "CampaignController", "x-operation-name": "sendCampaign", "tags": ["CampaignController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateCampaign   |\n", "responses": {"200": {"description": "Send campaign response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Campaign"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["campaign<PERSON>ey"], "properties": {"campaignKey": {"type": "string", "description": "The key of the campaign to send"}}}}}}, "operationId": "CampaignController.sendCampaign"}}, "/campaigns/{id}": {"put": {"x-controller-name": "CampaignController", "x-operation-name": "replaceById", "tags": ["CampaignController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateCampaign   |\n", "responses": {"200": {"description": "Return value of CampaignController.replaceById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Campaign PUT success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Campaign"}}}, "x-parameter-index": 1}, "operationId": "CampaignController.replaceById"}, "patch": {"x-controller-name": "CampaignController", "x-operation-name": "updateById", "tags": ["CampaignController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateCampaign   |\n", "responses": {"200": {"description": "Return value of CampaignController.updateById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Campaign PATCH success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CampaignPartial"}}}, "x-parameter-index": 1}, "operationId": "CampaignController.updateById"}, "get": {"x-controller-name": "CampaignController", "x-operation-name": "findById", "tags": ["CampaignController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewCampaign   |\n", "responses": {"200": {"description": "Campaign model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CampaignWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/campaigns.Filter"}}}}], "operationId": "CampaignController.findById"}, "delete": {"x-controller-name": "CampaignController", "x-operation-name": "deleteById", "tags": ["CampaignController"], "description": "\n\n| Permissions |\n| ------- |\n| DeleteCampaign   |\n", "responses": {"200": {"description": "Return value of CampaignController.deleteById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Campaign DELETE success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "CampaignController.deleteById"}}, "/campaigns": {"post": {"x-controller-name": "CampaignController", "x-operation-name": "create", "tags": ["CampaignController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateCampaign   |\n", "responses": {"200": {"description": "Campaign model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Campaign"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewCampaign"}}}}, "operationId": "CampaignController.create"}, "patch": {"x-controller-name": "CampaignController", "x-operation-name": "updateAll", "tags": ["CampaignController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateCampaign   |\n", "responses": {"200": {"description": "Campaign PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "campaigns.Where<PERSON><PERSON><PERSON>", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Campaign>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CampaignPartial"}}}}, "operationId": "CampaignController.updateAll"}, "get": {"x-controller-name": "CampaignController", "x-operation-name": "find", "tags": ["CampaignController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewCampaign   |\n", "responses": {"200": {"description": "Array of Campaign model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CampaignWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/campaigns.Filter1"}}}}], "operationId": "CampaignController.find"}}, "/groups/count": {"get": {"x-controller-name": "GroupController", "x-operation-name": "count", "tags": ["GroupController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateCampaign   |\n", "responses": {"200": {"description": "Groups model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "groups.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Groups>"}}}}], "operationId": "GroupController.count"}}, "/groups/{id}": {"put": {"x-controller-name": "GroupController", "x-operation-name": "replaceById", "tags": ["GroupController"], "description": "\n\n| Permissions |\n| ------- |\n| DeleteCampaign   |\n", "responses": {"200": {"description": "Return value of GroupController.replaceById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Groups PUT success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Groups"}}}, "x-parameter-index": 1}, "operationId": "GroupController.replaceById"}, "patch": {"x-controller-name": "GroupController", "x-operation-name": "updateById", "tags": ["GroupController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateCampaign   |\n", "responses": {"200": {"description": "Return value of GroupController.updateById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Groups PATCH success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GroupsPartial"}}}, "x-parameter-index": 1}, "operationId": "GroupController.updateById"}, "get": {"x-controller-name": "GroupController", "x-operation-name": "findById", "tags": ["GroupController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewCampaign   |\n", "responses": {"200": {"description": "Groups model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GroupsWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/groups.Filter"}}}}], "operationId": "GroupController.findById"}, "delete": {"x-controller-name": "GroupController", "x-operation-name": "deleteById", "tags": ["GroupController"], "description": "\n\n| Permissions |\n| ------- |\n| DeleteCampaign   |\n", "responses": {"200": {"description": "Return value of GroupController.deleteById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "Groups DELETE success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "GroupController.deleteById"}}, "/groups": {"post": {"x-controller-name": "GroupController", "x-operation-name": "create", "tags": ["GroupController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateCampaign   |\n", "responses": {"200": {"description": "Groups model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Groups"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}}}}}}, "operationId": "GroupController.create"}, "patch": {"x-controller-name": "GroupController", "x-operation-name": "updateAll", "tags": ["GroupController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateCampaign   |\n", "responses": {"200": {"description": "Groups PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "groups.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Groups>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GroupsPartial"}}}}, "operationId": "GroupController.updateAll"}, "get": {"x-controller-name": "GroupController", "x-operation-name": "find", "tags": ["GroupController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewCampaign   |\n", "responses": {"200": {"description": "Array of Groups model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GroupsWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/groups.Filter1"}}}}], "operationId": "GroupController.find"}}, "/lists/subscribe": {"post": {"x-controller-name": "ListController", "x-operation-name": "subscribeContact", "tags": ["ListController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewCampaign   |\n", "responses": {"200": {"description": "Subscribe contact to list", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Groups"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["<PERSON><PERSON><PERSON>", "contactInfo", "topicId"], "properties": {"listKey": {"type": "string"}, "topicId": {"type": "string"}, "contactInfo": {"type": "object", "required": ["firstName", "lastName", "email"], "properties": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "email": {"type": "string", "format": "email"}}}}}}}}, "operationId": "ListController.subscribeContact"}}, "/notification-users/bulk": {"post": {"x-controller-name": "NotificationUserController", "x-operation-name": "createAll", "tags": ["NotificationUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Notification User model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationUser"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewNotification   |\n| 1   |\n", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NewNotificationUser"}}}}}, "operationId": "NotificationUserController.createAll"}}, "/notification-users/count": {"get": {"x-controller-name": "NotificationUserController", "x-operation-name": "count", "tags": ["NotificationUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "NotificationUser model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewNotification   |\n| 1   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "notification_users.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<NotificationUser>"}}}}], "operationId": "NotificationUserController.count"}}, "/notification-users/hard": {"delete": {"x-controller-name": "NotificationUserController", "x-operation-name": "deleteAllHard", "tags": ["NotificationUserController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Notification DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| DeleteNotification   |\n| 4   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "notification_users.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<NotificationUser>"}}}}], "operationId": "NotificationUserController.deleteAllHard"}}, "/notification-users/{id}/notification": {"get": {"x-controller-name": "NotificationUserNotificationController", "x-operation-name": "getNotification", "tags": ["NotificationUserNotificationController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Notification belonging to NotificationUser", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewNotification   |\n| 1   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "NotificationUserNotificationController.getNotification"}}, "/notification-users/{id}": {"put": {"x-controller-name": "NotificationUserController", "x-operation-name": "replaceById", "tags": ["NotificationUserController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "NotificationUser PUT success"}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewNotification   |\n| 1   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationUser"}}}, "x-parameter-index": 1}, "operationId": "NotificationUserController.replaceById"}, "patch": {"x-controller-name": "NotificationUserController", "x-operation-name": "updateById", "tags": ["NotificationUserController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "NotificationUser PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewNotification   |\n| 1   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationUserPartial"}}}, "x-parameter-index": 1}, "operationId": "NotificationUserController.updateById"}, "get": {"x-controller-name": "NotificationUserController", "x-operation-name": "findById", "tags": ["NotificationUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "NotificationUser instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationUser"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewNotification   |\n| 1   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "NotificationUserController.findById"}, "delete": {"x-controller-name": "NotificationUserController", "x-operation-name": "deleteById", "tags": ["NotificationUserController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "NotificationUser DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| DeleteNotification   |\n| 4   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "NotificationUserController.deleteById"}}, "/notification-users": {"post": {"x-controller-name": "NotificationUserController", "x-operation-name": "create", "tags": ["NotificationUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "NotificationUser model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationUser"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewNotification   |\n| 1   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewNotificationUser"}}}}, "operationId": "NotificationUserController.create"}, "patch": {"x-controller-name": "NotificationUserController", "x-operation-name": "updateAll", "tags": ["NotificationUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "NotificationUser PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewNotification   |\n| 1   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "notification_users.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<NotificationUser>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationUserPartial"}}}}, "operationId": "NotificationUserController.updateAll"}, "get": {"x-controller-name": "NotificationUserController", "x-operation-name": "find", "tags": ["NotificationUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of NotificationUser model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationUser"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewNotification   |\n| 1   |\n", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/notification_users.Filter"}}}}], "operationId": "NotificationUserController.find"}, "delete": {"x-controller-name": "NotificationUserController", "x-operation-name": "deleteAll", "tags": ["NotificationUserController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Notification DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| DeleteNotification   |\n| 4   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "notification_users.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<NotificationUser>"}}}}], "operationId": "NotificationUserController.deleteAll"}}, "/notifications/access/{id}": {"patch": {"x-controller-name": "PubnubNotificationController", "x-operation-name": "grantAccess", "tags": ["PubnubNotificationController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Access response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AccessResponseDto"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CanGetNotificationAccess   |\n| 5   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "Authorization", "in": "header", "schema": {"type": "string"}}, {"name": "pubnubToken", "in": "header", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationAccess"}}}}, "operationId": "PubnubNotificationController.grantAccess"}, "delete": {"x-controller-name": "PubnubNotificationController", "x-operation-name": "revokeAccess", "tags": ["PubnubNotificationController"], "responses": {"200": {"description": "Object with success"}}, "description": "", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "PubnubNotificationController.revokeAccess"}}, "/notifications/bulk": {"post": {"x-controller-name": "NotificationController", "x-operation-name": "createBulkNotificaitions", "tags": ["NotificationController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Notifications, to send notifications as bulk.", "content": {"application/json": {"type": "array", "items": {"$ref": "#/components/schemas/Notification", "definitions": {"Notification": {"$ref": "#/components/schemas/Notification"}}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateNotification   |\n| 2   |\n", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationExcluding_id_"}}}}}, "operationId": "NotificationController.createBulkNotificaitions"}}, "/notifications/count": {"get": {"x-controller-name": "NotificationController", "x-operation-name": "count", "tags": ["NotificationController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Notification model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewNotification   |\n| 1   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "notifications.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Notification>"}}}}], "operationId": "NotificationController.count"}}, "/notifications/drafts": {"post": {"x-controller-name": "NotificationController", "x-operation-name": "draftNotification", "tags": ["NotificationController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "This API is used to draft notifications, here in case isDraft .", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationExcluding_id-isDraft_"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateNotification   |\n| 2   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationExcluding_id-isDraft_"}}}}, "operationId": "NotificationController.draftNotification"}}, "/notifications/groups/{groupKey}": {"post": {"x-controller-name": "NotificationController", "x-operation-name": "sendGroupedNotificationByGroupKey", "tags": ["NotificationController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "This API is used to send notification by grouping by given key in the end point.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoExcluding_id_"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateNotification   |\n| 2   |\n", "parameters": [{"name": "groupKey", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoExcluding_id-groupKey_"}}}}, "operationId": "NotificationController.sendGroupedNotificationByGroupKey"}}, "/notifications/send": {"post": {"x-controller-name": "NotificationController", "x-operation-name": "sendNotificationForSleepTimeUsers", "tags": ["NotificationController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "This API is used to send notifications for given search criteria.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationSettingsDto"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateNotification   |\n| 2   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationSettingsDto"}}}}, "operationId": "NotificationController.sendNotificationForSleepTimeUsers"}}, "/notifications/{id}/notification-users": {"post": {"x-controller-name": "NotificationNotificationUserController", "x-operation-name": "create", "tags": ["NotificationNotificationUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Notification model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationUser"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateNotification   |\n| 2   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewNotificationUserInNotification"}}}, "x-parameter-index": 1}, "operationId": "NotificationNotificationUserController.create"}, "patch": {"x-controller-name": "NotificationNotificationUserController", "x-operation-name": "patch", "tags": ["NotificationNotificationUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Notification.NotificationUser PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateNotification   |\n| 3   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "notification_users.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<NotificationUser>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationUserPartial"}}}, "x-parameter-index": 1}, "operationId": "NotificationNotificationUserController.patch"}, "get": {"x-controller-name": "NotificationNotificationUserController", "x-operation-name": "find", "tags": ["NotificationNotificationUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Notification has many NotificationUser", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NotificationUser"}}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewNotification   |\n| 1   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}], "operationId": "NotificationNotificationUserController.find"}, "delete": {"x-controller-name": "NotificationNotificationUserController", "x-operation-name": "delete", "tags": ["NotificationNotificationUserController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Notification.NotificationUser DELETE success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| DeleteNotification   |\n| 4   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "notification_users.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<NotificationUser>"}}}}], "operationId": "NotificationNotificationUserController.delete"}}, "/notifications/{id}/send": {"post": {"x-controller-name": "NotificationController", "x-operation-name": "sendNotificationById", "tags": ["NotificationController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "This API is used to send notifications for given Notification Id.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoExcluding_id-groupKey-receiver-subject-body-type_"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateNotification   |\n| 2   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationDtoExcluding_id-groupKey-receiver-subject-body-type_"}}}}, "operationId": "NotificationController.sendNotificationById"}}, "/notifications/{id}": {"patch": {"x-controller-name": "NotificationController", "x-operation-name": "updateById", "tags": ["NotificationController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Notification PATCH success"}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateNotification   |\n| 3   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationPartial"}}}, "x-parameter-index": 1}, "operationId": "NotificationController.updateById"}, "get": {"x-controller-name": "NotificationController", "x-operation-name": "findById", "tags": ["NotificationController"], "responses": {"200": {"description": ", to get the notification by ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Notification"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| ViewNotification   |\n| 1   |\n", "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "NotificationController.findById"}}, "/notifications": {"post": {"x-controller-name": "NotificationController", "x-operation-name": "create", "tags": ["NotificationController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Notification model instance, This API end point will be used to send the notification to the user.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Notification"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| CreateNotification   |\n| 2   |\n", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewNotificationDtoExcluding_id-isDraft_"}}}, "description": "This API is used to send notifications, the request body contains the object of notification model."}, "operationId": "NotificationController.create"}, "patch": {"x-controller-name": "NotificationController", "x-operation-name": "updateAll", "tags": ["NotificationController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Notification PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "description": "\n\n| Permissions |\n| ------- |\n| UpdateNotification   |\n| 3   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "notifications.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Notification>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationPartial"}}}}, "operationId": "NotificationController.updateAll"}, "get": {"x-controller-name": "NotificationController", "x-operation-name": "find", "tags": ["NotificationController"], "security": [{"HTTPBearer": []}], "responses": {"200": {"description": "Array of Notification model instances, To get the notifications", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Notification"}}}}}}, "description": "", "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/notifications.Filter"}}}}], "operationId": "NotificationController.find"}, "delete": {"x-controller-name": "NotificationController", "x-operation-name": "deleteAll", "tags": ["NotificationController"], "security": [{"HTTPBearer": []}], "responses": {"204": {"description": "Notification DELETE success"}}, "description": "\n\n| Permissions |\n| ------- |\n| DeleteNotification   |\n| 4   |\n", "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "notifications.WhereF<PERSON>er", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<Notification>"}}}}], "operationId": "NotificationController.deleteAll"}}, "/ping": {"get": {"x-controller-name": "PingController", "x-operation-name": "ping", "tags": ["PingController"], "responses": {"200": {"description": "Ping Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PingResponse"}}}}}, "description": "", "operationId": "PingController.ping"}}, "/subscribe": {"post": {"x-controller-name": "SubscriptionController", "x-operation-name": "subscribeToTopics", "tags": ["SubscriptionController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateCampaign   |\n", "responses": {"200": {"description": "Subscribe to FCM and Zoho Campaign", "content": {"application/json": {"schema": {"type": "object", "properties": {"fcm": {"type": "string"}, "zoho": {"type": "string"}}}}}}}, "requestBody": {"content": {"application/json": {"schema": {"type": "object", "required": ["topic", "zohoListKey", "zohoTopicId"], "properties": {"topic": {"type": "string"}, "fcmToken": {"type": "string"}, "email": {"type": "string"}, "zohoListKey": {"type": "string"}, "zohoTopicId": {"type": "string"}}}}}}, "operationId": "SubscriptionController.subscribeToTopics"}}, "/user-fcms/count": {"get": {"x-controller-name": "UserFcmControllerController", "x-operation-name": "count", "tags": ["UserFcmControllerController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateCampaign   |\n", "responses": {"200": {"description": "UserFcm model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "user_fcms.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<UserFcm>"}}}}], "operationId": "UserFcmControllerController.count"}}, "/user-fcms/{id}": {"put": {"x-controller-name": "UserFcmControllerController", "x-operation-name": "replaceById", "tags": ["UserFcmControllerController"], "responses": {"204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "UserFcm PUT success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserFcm"}}}, "x-parameter-index": 1}, "operationId": "UserFcmControllerController.replaceById"}, "patch": {"x-controller-name": "UserFcmControllerController", "x-operation-name": "updateById", "tags": ["UserFcmControllerController"], "responses": {"204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "UserFcm PATCH success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserFcmPartial"}}}, "x-parameter-index": 1}, "operationId": "UserFcmControllerController.updateById"}, "get": {"x-controller-name": "UserFcmControllerController", "x-operation-name": "findById", "tags": ["UserFcmControllerController"], "responses": {"200": {"description": "UserFcm model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserFcmWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}, {"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/user_fcms.Filter"}}}}], "operationId": "UserFcmControllerController.findById"}, "delete": {"x-controller-name": "UserFcmControllerController", "x-operation-name": "deleteById", "tags": ["UserFcmControllerController"], "responses": {"204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "UserFcm DELETE success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "UserFcmControllerController.deleteById"}}, "/user-fcms": {"post": {"x-controller-name": "UserFcmControllerController", "x-operation-name": "create", "tags": ["UserFcmControllerController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateCampaign   |\n", "responses": {"200": {"description": "UserFcm model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserFcm"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserFcm"}}}}, "operationId": "UserFcmControllerController.create"}, "patch": {"x-controller-name": "UserFcmControllerController", "x-operation-name": "updateAll", "tags": ["UserFcmControllerController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateCampaign   |\n", "responses": {"200": {"description": "UserFcm PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "user_fcms.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<UserFcm>"}}}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserFcmPartial"}}}}, "operationId": "UserFcmControllerController.updateAll"}, "get": {"x-controller-name": "UserFcmControllerController", "x-operation-name": "find", "tags": ["UserFcmControllerController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewCampaign   |\n", "responses": {"200": {"description": "Array of UserFcm model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserFcmWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/user_fcms.Filter1"}}}}], "operationId": "UserFcmControllerController.find"}}, "/user-notification-settings/count": {"get": {"x-controller-name": "UserNotificationSettingsController", "x-operation-name": "count", "tags": ["UserNotificationSettingsController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewNotificationUserSettings   |\n", "responses": {"200": {"description": "UserNotificationSettings model count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "parameters": [{"name": "where", "in": "query", "content": {"application/json": {"schema": {"type": "object", "title": "user_notification_settings.WhereFilter", "additionalProperties": true, "x-typescript-type": "@loopback/repository#Where<UserNotificationSettings>"}}}}], "operationId": "UserNotificationSettingsController.count"}}, "/user-notification-settings/{id}": {"put": {"x-controller-name": "UserNotificationSettingsController", "x-operation-name": "replaceById", "tags": ["UserNotificationSettingsController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateNotificationUserSettings   |\n", "responses": {"200": {"description": "Return value of UserNotificationSettingsController.replaceById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "UserNotificationSettings PUT success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserNotificationSettings"}}}, "x-parameter-index": 1}, "operationId": "UserNotificationSettingsController.replaceById"}, "patch": {"x-controller-name": "UserNotificationSettingsController", "x-operation-name": "updateById", "tags": ["UserNotificationSettingsController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateNotificationUserSettings   |\n", "responses": {"200": {"description": "Return value of UserNotificationSettingsController.updateById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "UserNotificationSettings PATCH success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserNotificationSettingsPartial"}}}, "x-parameter-index": 1}, "operationId": "UserNotificationSettingsController.updateById"}, "get": {"x-controller-name": "UserNotificationSettingsController", "x-operation-name": "findById", "tags": ["UserNotificationSettingsController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewNotificationUserSettings   |\n", "responses": {"200": {"description": "UserNotificationSettings model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserNotificationSettingsWithRelations"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "UserNotificationSettingsController.findById"}, "delete": {"x-controller-name": "UserNotificationSettingsController", "x-operation-name": "deleteById", "tags": ["UserNotificationSettingsController"], "description": "\n\n| Permissions |\n| ------- |\n| DeleteNotificationUserSettings   |\n", "responses": {"200": {"description": "Return value of UserNotificationSettingsController.deleteById", "content": {}}, "204": {"description": "No Content", "content": {"application/json": {"schema": {"description": "UserNotificationSettings DELETE success"}}}}}, "parameters": [{"name": "id", "in": "path", "schema": {"type": "string"}, "required": true}], "operationId": "UserNotificationSettingsController.deleteById"}}, "/user-notification-settings": {"post": {"x-controller-name": "UserNotificationSettingsController", "x-operation-name": "create", "tags": ["UserNotificationSettingsController"], "description": "\n\n| Permissions |\n| ------- |\n| CreateNotificationUserSettings   |\n", "responses": {"200": {"description": "UserNotificationSettings model instance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserNotificationSettings"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewUserNotificationSettings"}}}}, "operationId": "UserNotificationSettingsController.create"}, "patch": {"x-controller-name": "UserNotificationSettingsController", "x-operation-name": "updateAll", "tags": ["UserNotificationSettingsController"], "description": "\n\n| Permissions |\n| ------- |\n| UpdateNotificationUserSettings   |\n", "responses": {"200": {"description": "UserNotificationSettings PATCH success count", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/loopback.Count"}}}}}, "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserNotificationSettingsPartial"}}}}, "operationId": "UserNotificationSettingsController.updateAll"}, "get": {"x-controller-name": "UserNotificationSettingsController", "x-operation-name": "find", "tags": ["UserNotificationSettingsController"], "description": "\n\n| Permissions |\n| ------- |\n| ViewNotificationUserSettings   |\n", "responses": {"200": {"description": "Array of UserNotificationSettings model instances", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserNotificationSettingsWithRelations"}}}}}}, "parameters": [{"name": "filter", "in": "query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/user_notification_settings.Filter"}}}}], "operationId": "UserNotificationSettingsController.find"}}, "/": {"get": {"x-controller-name": "HomePageController", "x-operation-name": "homePage", "tags": ["HomePageController"], "responses": {"200": {"description": "Home Page", "content": {"text/html": {"schema": {"type": "string"}}}}}, "description": "", "operationId": "HomePageController.homePage"}}}, "components": {"securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"Notification": {"title": "Notification", "type": "object", "properties": {"id": {"type": "string"}, "subject": {"type": "string", "nullable": true}, "body": {"type": "string"}, "receiver": {"type": "object"}, "type": {"type": "number"}, "sentDate": {"type": "string", "format": "date-time"}, "options": {"type": "object"}, "isDraft": {"type": "boolean"}, "groupKey": {"type": "string"}, "isCritical": {"type": "boolean"}}, "required": ["body", "type"], "additionalProperties": false}, "NewNotificationDtoExcluding_id-isDraft_": {"title": "NewNotificationDtoExcluding_id-isDraft_", "type": "object", "description": "(tsType: Omit<NewNotificationDto, 'id' | 'isDraft'>, schemaOptions: { exclude: [ 'id', 'isDraft' ] })", "properties": {"subject": {"type": "string", "nullable": true}, "body": {"type": "string"}, "receiver": {"type": "object"}, "type": {"type": "number"}, "sentDate": {"type": "string", "format": "date-time"}, "options": {"type": "object"}, "groupKey": {"type": "string"}, "isCritical": {"type": "boolean"}, "smsType": {"type": "string"}}, "required": ["body", "type"], "additionalProperties": false, "x-typescript-type": "Omit<NewNotificationDto, 'id' | 'isDraft'>"}, "NotificationDtoExcluding_id_": {"title": "NotificationDtoExcluding_id_", "type": "object", "description": "(tsType: Omit<NotificationDto, 'id'>, schemaOptions: { exclude: [ 'id' ] })", "properties": {"subject": {"type": "string", "nullable": true}, "body": {"type": "string"}, "receiver": {"type": "object"}, "isCritical": {"type": "boolean"}, "type": {"type": "number"}, "groupKey": {"type": "string"}, "options": {"type": "object"}, "sentDate": {"type": "string", "format": "date-time"}}, "required": ["type"], "additionalProperties": false, "x-typescript-type": "Omit<NotificationDto, 'id'>"}, "NotificationDtoExcluding_id-groupKey_": {"title": "NotificationDtoExcluding_id-groupKey_", "type": "object", "description": "(tsType: Omit<NotificationDto, 'id' | 'groupKey'>, schemaOptions: { exclude: [ 'id', 'groupKey' ] })", "properties": {"subject": {"type": "string", "nullable": true}, "body": {"type": "string"}, "receiver": {"type": "object"}, "isCritical": {"type": "boolean"}, "type": {"type": "number"}, "options": {"type": "object"}, "sentDate": {"type": "string", "format": "date-time"}}, "required": ["type"], "additionalProperties": false, "x-typescript-type": "Omit<NotificationDto, 'id' | 'groupKey'>"}, "NotificationExcluding_id-isDraft_": {"title": "NotificationExcluding_id-isDraft_", "type": "object", "description": "(tsType: Omit<Notification, 'id' | 'isDraft'>, schemaOptions: { exclude: [ 'id', 'isDraft' ] })", "properties": {"subject": {"type": "string", "nullable": true}, "body": {"type": "string"}, "receiver": {"type": "object"}, "type": {"type": "number"}, "sentDate": {"type": "string", "format": "date-time"}, "options": {"type": "object"}, "groupKey": {"type": "string"}, "isCritical": {"type": "boolean"}}, "required": ["body", "type"], "additionalProperties": false, "x-typescript-type": "Omit<Notification, 'id' | 'isDraft'>"}, "NotificationDtoExcluding_id-groupKey-receiver-subject-body-type_": {"title": "NotificationDtoExcluding_id-groupKey-receiver-subject-body-type_", "type": "object", "description": "(tsType: Omit<NotificationDto, 'id' | 'groupKey' | 'receiver' | 'subject' | 'body' | 'type'>, schemaOptions: { exclude: [ 'id', 'groupKey', 'receiver', 'subject', 'body', 'type' ] })", "properties": {"isCritical": {"type": "boolean"}, "options": {"type": "object"}, "sentDate": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "x-typescript-type": "Omit<NotificationDto, 'id' | 'groupKey' | 'receiver' | 'subject' | 'body' | 'type'>"}, "NotificationDto": {"title": "NotificationDto", "type": "object", "properties": {"id": {"type": "string"}, "subject": {"type": "string", "nullable": true}, "body": {"type": "string"}, "receiver": {"type": "object"}, "isCritical": {"type": "boolean"}, "type": {"type": "number"}, "groupKey": {"type": "string"}, "options": {"type": "object"}, "sentDate": {"type": "string", "format": "date-time"}}, "required": ["type"], "additionalProperties": false}, "NotificationSettingsDto": {"title": "NotificationSettingsDto", "type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string"}}, "userId": {"type": "array", "items": {"type": "string"}}, "startTime": {"type": "string", "format": "date-time"}, "endTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "NotificationExcluding_id_": {"title": "NotificationExcluding_id_", "type": "object", "description": "(tsType: Omit<Notification, 'id'>, schemaOptions: { exclude: [ 'id' ] })", "properties": {"subject": {"type": "string", "nullable": true}, "body": {"type": "string"}, "receiver": {"type": "object"}, "type": {"type": "number"}, "sentDate": {"type": "string", "format": "date-time"}, "options": {"type": "object"}, "isDraft": {"type": "boolean"}, "groupKey": {"type": "string"}, "isCritical": {"type": "boolean"}}, "required": ["body", "type"], "additionalProperties": false, "x-typescript-type": "Omit<Notification, 'id'>"}, "NotificationPartial": {"title": "NotificationPartial", "type": "object", "description": "(tsType: Partial<Notification>, schemaOptions: { partial: true })", "properties": {"id": {"type": "string"}, "subject": {"type": "string", "nullable": true}, "body": {"type": "string"}, "receiver": {"type": "object"}, "type": {"type": "number"}, "sentDate": {"type": "string", "format": "date-time"}, "options": {"type": "object"}, "isDraft": {"type": "boolean"}, "groupKey": {"type": "string"}, "isCritical": {"type": "boolean"}}, "additionalProperties": false, "x-typescript-type": "Partial<Notification>"}, "NotificationUser": {"title": "NotificationUser", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "id": {"type": "string"}, "notificationId": {"type": "string"}, "userId": {"type": "string"}, "isRead": {"type": "boolean"}, "actionMeta": {"type": "object"}, "isDraft": {"type": "boolean"}}, "required": ["notificationId", "userId"], "additionalProperties": true}, "NewNotificationUser": {"title": "NewNotificationUser", "type": "object", "description": "(tsType: Omit<NotificationUser, 'id'>, schemaOptions: { title: 'NewNotificationUser', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "notificationId": {"type": "string"}, "userId": {"type": "string"}, "isRead": {"type": "boolean"}, "actionMeta": {"type": "object"}, "isDraft": {"type": "boolean"}}, "required": ["notificationId", "userId"], "additionalProperties": true, "x-typescript-type": "Omit<NotificationUser, 'id'>"}, "NotificationUserPartial": {"title": "NotificationUserPartial", "type": "object", "description": "(tsType: Partial<NotificationUser>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "id": {"type": "string"}, "notificationId": {"type": "string"}, "userId": {"type": "string"}, "isRead": {"type": "boolean"}, "actionMeta": {"type": "object"}, "isDraft": {"type": "boolean"}}, "additionalProperties": true, "x-typescript-type": "Partial<NotificationUser>"}, "AccessResponseDto": {"title": "AccessResponseDto", "type": "object", "properties": {"ttl": {"type": "number"}, "cipherKey": {"type": "string"}}, "additionalProperties": false}, "NotificationAccess": {"title": "NotificationAccess", "type": "object", "properties": {"receiver": {"type": "object", "description": "this will contain the list of reciever to give access"}, "type": {"type": "number"}, "options": {"type": "object", "description": "this will contain the ttl property for now"}}, "required": ["receiver", "type"], "additionalProperties": false}, "NewNotificationUserInNotification": {"title": "NewNotificationUserInNotification", "type": "object", "description": "(tsType: @loopback/repository-json-schema#Optional<Omit<NotificationUser, 'id'>, 'notificationId'>, schemaOptions: { title: 'NewNotificationUserInNotification', exclude: [ 'id' ], optional: [ 'notificationId' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "notificationId": {"type": "string"}, "userId": {"type": "string"}, "isRead": {"type": "boolean"}, "actionMeta": {"type": "object"}, "isDraft": {"type": "boolean"}}, "required": ["userId"], "additionalProperties": true, "x-typescript-type": "@loopback/repository-json-schema#Optional<Omit<NotificationUser, 'id'>, 'notificationId'>"}, "UserNotificationSettings": {"title": "UserNotificationSettings", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "id": {"type": "string"}, "userId": {"type": "string"}, "sleepStartTime": {"type": "string", "format": "date-time"}, "sleepEndTime": {"type": "string", "format": "date-time"}, "type": {"type": "number"}}, "required": ["userId", "sleepStartTime", "sleepEndTime", "type"], "additionalProperties": true}, "NewUserNotificationSettings": {"title": "NewUserNotificationSettings", "type": "object", "description": "(tsType: Omit<UserNotificationSettings, 'id'>, schemaOptions: { title: 'NewUserNotificationSettings', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "userId": {"type": "string"}, "sleepStartTime": {"type": "string", "format": "date-time"}, "sleepEndTime": {"type": "string", "format": "date-time"}, "type": {"type": "number"}}, "required": ["userId", "sleepStartTime", "sleepEndTime", "type"], "additionalProperties": true, "x-typescript-type": "Omit<UserNotificationSettings, 'id'>"}, "UserNotificationSettingsWithRelations": {"title": "UserNotificationSettingsWithRelations", "type": "object", "description": "(tsType: UserNotificationSettingsWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "id": {"type": "string"}, "userId": {"type": "string"}, "sleepStartTime": {"type": "string", "format": "date-time"}, "sleepEndTime": {"type": "string", "format": "date-time"}, "type": {"type": "number"}}, "required": ["userId", "sleepStartTime", "sleepEndTime", "type"], "additionalProperties": true, "x-typescript-type": "UserNotificationSettingsWithRelations"}, "UserNotificationSettingsPartial": {"title": "UserNotificationSettingsPartial", "type": "object", "description": "(tsType: Partial<UserNotificationSettings>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "id": {"type": "string"}, "userId": {"type": "string"}, "sleepStartTime": {"type": "string", "format": "date-time"}, "sleepEndTime": {"type": "string", "format": "date-time"}, "type": {"type": "number"}}, "additionalProperties": true, "x-typescript-type": "Partial<UserNotificationSettings>"}, "UserFcm": {"title": "UserFcm", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "fcmToken": {"type": "string"}, "deviceId": {"type": "string"}, "userTenantId": {"type": "string"}}, "required": ["fcmToken", "userTenantId"], "additionalProperties": false}, "NewUserFcm": {"title": "NewUserFcm", "type": "object", "description": "(tsType: Omit<UserFcm, 'id'>, schemaOptions: { title: 'NewUserFcm', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "fcmToken": {"type": "string"}, "deviceId": {"type": "string"}, "userTenantId": {"type": "string"}}, "required": ["fcmToken", "userTenantId"], "additionalProperties": false, "x-typescript-type": "Omit<UserFcm, 'id'>"}, "UserFcmWithRelations": {"title": "UserFcmWithRelations", "type": "object", "description": "(tsType: UserFcmWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "fcmToken": {"type": "string"}, "deviceId": {"type": "string"}, "userTenantId": {"type": "string"}}, "required": ["fcmToken", "userTenantId"], "additionalProperties": false, "x-typescript-type": "UserFcmWithRelations"}, "UserFcmPartial": {"title": "UserFcmPartial", "type": "object", "description": "(tsType: Partial<UserFcm>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "fcmToken": {"type": "string"}, "deviceId": {"type": "string"}, "userTenantId": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<UserFcm>"}, "Groups": {"title": "Groups", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "topicId": {"type": "string"}, "listKey": {"type": "string"}, "listName": {"type": "string"}, "groupId": {"type": "string"}}, "required": ["name", "topicId", "<PERSON><PERSON><PERSON>", "listName", "groupId"], "additionalProperties": false}, "GroupsWithRelations": {"title": "GroupsWithRelations", "type": "object", "description": "(tsType: GroupsWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "topicId": {"type": "string"}, "listKey": {"type": "string"}, "listName": {"type": "string"}, "groupId": {"type": "string"}}, "required": ["name", "topicId", "<PERSON><PERSON><PERSON>", "listName", "groupId"], "additionalProperties": false, "x-typescript-type": "GroupsWithRelations"}, "GroupsPartial": {"title": "GroupsPartial", "type": "object", "description": "(tsType: Partial<Groups>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "topicId": {"type": "string"}, "listKey": {"type": "string"}, "listName": {"type": "string"}, "groupId": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<Groups>"}, "Campaign": {"title": "Campaign", "type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "number"}, "subject": {"type": "string"}, "clickAction": {"type": "string"}, "body": {"type": "string"}, "isDraft": {"type": "boolean"}, "sent": {"type": "string"}, "groupId": {"type": "string"}, "campaignStatus": {"type": "string"}, "campaignKey": {"type": "string"}}, "required": ["name", "subject", "groupId"], "additionalProperties": false}, "NewCampaign": {"title": "NewCampaign", "type": "object", "description": "(tsType: Omit<Campaign, 'id'>, schemaOptions: { title: 'NewCampaign', exclude: [ 'id' ] })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "number"}, "subject": {"type": "string"}, "clickAction": {"type": "string"}, "body": {"type": "string"}, "isDraft": {"type": "boolean"}, "sent": {"type": "string"}, "groupId": {"type": "string"}, "campaignStatus": {"type": "string"}, "campaignKey": {"type": "string"}}, "required": ["name", "subject", "groupId"], "additionalProperties": false, "x-typescript-type": "Omit<Campaign, 'id'>"}, "CampaignWithRelations": {"title": "CampaignWithRelations", "type": "object", "description": "(tsType: CampaignWithRelations, schemaOptions: { includeRelations: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "number"}, "subject": {"type": "string"}, "clickAction": {"type": "string"}, "body": {"type": "string"}, "isDraft": {"type": "boolean"}, "sent": {"type": "string"}, "groupId": {"type": "string"}, "campaignStatus": {"type": "string"}, "campaignKey": {"type": "string"}}, "required": ["name", "subject", "groupId"], "additionalProperties": false, "x-typescript-type": "CampaignWithRelations"}, "CampaignPartial": {"title": "CampaignPartial", "type": "object", "description": "(tsType: Partial<Campaign>, schemaOptions: { partial: true })", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "string", "format": "date-time", "nullable": true}, "deletedBy": {"type": "string", "nullable": true}, "createdOn": {"type": "string", "format": "date-time"}, "modifiedOn": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "type": {"type": "number"}, "subject": {"type": "string"}, "clickAction": {"type": "string"}, "body": {"type": "string"}, "isDraft": {"type": "boolean"}, "sent": {"type": "string"}, "groupId": {"type": "string"}, "campaignStatus": {"type": "string"}, "campaignKey": {"type": "string"}}, "additionalProperties": false, "x-typescript-type": "Partial<Campaign>"}, "loopback.Count": {"type": "object", "title": "loopback.Count", "x-typescript-type": "@loopback/repository#Count", "properties": {"count": {"type": "number"}}}, "campaigns.Filter": {"type": "object", "title": "campaigns.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "type": {"type": "boolean"}, "subject": {"type": "boolean"}, "clickAction": {"type": "boolean"}, "body": {"type": "boolean"}, "isDraft": {"type": "boolean"}, "sent": {"type": "boolean"}, "groupId": {"type": "boolean"}, "campaignStatus": {"type": "boolean"}, "campaignKey": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "type", "subject", "clickAction", "body", "isDraft", "sent", "groupId", "campaignStatus", "campaign<PERSON>ey"], "example": "deleted"}, "uniqueItems": true}], "title": "campaigns.<PERSON>"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Campaign>"}, "campaigns.Filter1": {"type": "object", "title": "campaigns.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "campaigns.Where<PERSON><PERSON><PERSON>", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "type": {"type": "boolean"}, "subject": {"type": "boolean"}, "clickAction": {"type": "boolean"}, "body": {"type": "boolean"}, "isDraft": {"type": "boolean"}, "sent": {"type": "boolean"}, "groupId": {"type": "boolean"}, "campaignStatus": {"type": "boolean"}, "campaignKey": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "type", "subject", "clickAction", "body", "isDraft", "sent", "groupId", "campaignStatus", "campaign<PERSON>ey"], "example": "deleted"}, "uniqueItems": true}], "title": "campaigns.<PERSON>"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Campaign>"}, "groups.Filter": {"type": "object", "title": "groups.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "topicId": {"type": "boolean"}, "listKey": {"type": "boolean"}, "listName": {"type": "boolean"}, "groupId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "topicId", "<PERSON><PERSON><PERSON>", "listName", "groupId"], "example": "deleted"}, "uniqueItems": true}], "title": "groups.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Groups>"}, "groups.Filter1": {"type": "object", "title": "groups.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "groups.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "name": {"type": "boolean"}, "topicId": {"type": "boolean"}, "listKey": {"type": "boolean"}, "listName": {"type": "boolean"}, "groupId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "name", "topicId", "<PERSON><PERSON><PERSON>", "listName", "groupId"], "example": "deleted"}, "uniqueItems": true}], "title": "groups.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Groups>"}, "notification_users.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "notification_users.ScopeFilter"}, "notification_users.IncludeFilter.Items": {"title": "notification_users.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["notification"]}, "scope": {"$ref": "#/components/schemas/notification_users.ScopeFilter"}}}, "notification_users.Filter": {"type": "object", "title": "notification_users.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "notification_users.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "id": {"type": "boolean"}, "notificationId": {"type": "boolean"}, "userId": {"type": "boolean"}, "isRead": {"type": "boolean"}, "actionMeta": {"type": "boolean"}, "isDraft": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "deleted"}, "uniqueItems": true}], "title": "notification_users.Fields"}, "include": {"title": "notification_users.IncludeFilter", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/notification_users.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<NotificationUser>"}, "notifications.ScopeFilter": {"type": "object", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {}, "additionalProperties": true}, {"type": "array", "items": {"type": "string"}, "uniqueItems": true}]}, "include": {"type": "array", "items": {"type": "object", "properties": {}, "additionalProperties": true}}}, "additionalProperties": false, "title": "notifications.<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "notifications.IncludeFilter.Items": {"title": "notifications.IncludeFilter.Items", "type": "object", "properties": {"relation": {"type": "string", "enum": ["notificationUsers"]}, "scope": {"$ref": "#/components/schemas/notifications.ScopeFilter"}}}, "notifications.Filter": {"type": "object", "title": "notifications.<PERSON>lter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "notifications.WhereF<PERSON>er", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"id": {"type": "boolean"}, "subject": {"type": "boolean"}, "body": {"type": "boolean"}, "receiver": {"type": "boolean"}, "type": {"type": "boolean"}, "sentDate": {"type": "boolean"}, "options": {"type": "boolean"}, "isDraft": {"type": "boolean"}, "groupKey": {"type": "boolean"}, "isCritical": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["id", "subject", "body", "receiver", "type", "sentDate", "options", "isDraft", "groupKey", "isCritical"], "example": "id"}, "uniqueItems": true}], "title": "notifications.<PERSON>"}, "include": {"title": "notifications.Include<PERSON><PERSON>er", "type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/notifications.IncludeFilter.Items"}, {"type": "string"}]}}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<Notification>"}, "PingResponse": {"type": "object", "title": "PingResponse", "properties": {"greeting": {"type": "string"}, "date": {"type": "string"}, "url": {"type": "string"}, "headers": {"type": "object", "properties": {"Content-Type": {"type": "string"}}, "additionalProperties": true}}}, "user_fcms.Filter": {"type": "object", "title": "user_fcms.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "fcmToken": {"type": "boolean"}, "deviceId": {"type": "boolean"}, "userTenantId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "fcmToken", "deviceId", "userTenantId"], "example": "deleted"}, "uniqueItems": true}], "title": "user_fcms.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<UserFcm>"}, "user_fcms.Filter1": {"type": "object", "title": "user_fcms.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "user_fcms.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "createdBy": {"type": "boolean"}, "modifiedBy": {"type": "boolean"}, "id": {"type": "boolean"}, "fcmToken": {"type": "boolean"}, "deviceId": {"type": "boolean"}, "userTenantId": {"type": "boolean"}}, "additionalProperties": false}, {"type": "array", "items": {"type": "string", "enum": ["deleted", "deletedOn", "deletedBy", "createdOn", "modifiedOn", "created<PERSON>y", "modifiedBy", "id", "fcmToken", "deviceId", "userTenantId"], "example": "deleted"}, "uniqueItems": true}], "title": "user_fcms.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<UserFcm>"}, "user_notification_settings.Filter": {"type": "object", "title": "user_notification_settings.Filter", "properties": {"offset": {"type": "integer", "minimum": 0}, "limit": {"type": "integer", "minimum": 1, "example": 100}, "skip": {"type": "integer", "minimum": 0}, "order": {"oneOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "where": {"title": "user_notification_settings.WhereFilter", "type": "object", "additionalProperties": true}, "fields": {"oneOf": [{"type": "object", "properties": {"deleted": {"type": "boolean"}, "deletedOn": {"type": "boolean"}, "deletedBy": {"type": "boolean"}, "createdOn": {"type": "boolean"}, "modifiedOn": {"type": "boolean"}, "id": {"type": "boolean"}, "userId": {"type": "boolean"}, "sleepStartTime": {"type": "boolean"}, "sleepEndTime": {"type": "boolean"}, "type": {"type": "boolean"}}, "additionalProperties": true}, {"type": "array", "items": {"type": "string", "example": "deleted"}, "uniqueItems": true}], "title": "user_notification_settings.Fields"}}, "additionalProperties": false, "x-typescript-type": "@loopback/repository#Filter<UserNotificationSettings>"}}}, "servers": [{"url": "/"}]}