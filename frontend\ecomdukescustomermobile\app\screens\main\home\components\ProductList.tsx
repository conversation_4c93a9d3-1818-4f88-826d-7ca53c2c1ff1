import {FlatList, StyleSheet, View} from 'react-native';
import {ProductVariant} from '../../../../redux/product/product';
import ProductCard from '../../Products/Components/ProductCard';
import {SCREEN_NAME} from '../../../../constants/screenNames';
import {useEffect, useState} from 'react';
import Toast from 'react-native-toast-message';
import {useSelector} from 'react-redux';
import {useAddItemToCartMutation} from '../../../../redux/cart/cartApiSlice';
import {
  useAddItemToWishlistMutation,
  useRemoveItemFromWishlistMutation,
} from '../../../../redux/wishlist/wishlistApiSlice';
import {useNavigation} from '@react-navigation/native';
import {HomeScreenNavigationProp} from '../../../../navigations/types';
import {RootState} from '../../../../redux/store';

export const ProductList: React.FC<{variants: ProductVariant[]}> = ({
  variants,
}) => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const [wishlistMap, setWishlistMap] = useState<Map<string, string>>(
    new Map(),
  );
  const [cartMap, setCartMap] = useState<Record<string, boolean>>({});
  const [loaderMap, setLoaderMap] = useState<Record<string, boolean>>({});
  const [addItemToCart] = useAddItemToCartMutation();
  const [addItemToWishlist] = useAddItemToWishlistMutation();
  const [removeItemFromWishlist] = useRemoveItemFromWishlistMutation();
  const user = useSelector((state: RootState) => state.auth.userDetails);

  const handleProductClick = (productId: string) => {
    navigation.navigate(SCREEN_NAME.PRODUCTS, {productId});
  };

  const calculateDiscount = (
    mrp?: number,
    price?: number,
  ): string | undefined => {
    if (
      typeof mrp !== 'number' ||
      typeof price !== 'number' ||
      mrp <= price ||
      mrp === 0
    ) {
      return undefined;
    }

    const discountPercent = ((mrp - price) / mrp) * 100;
    return `${Math.round(discountPercent)}%`;
  };
  const handleAddToCart = async (product: ProductVariant) => {
    const productVariantId = product?.id;
    if (!productVariantId) {
      return;
    }
    const customerId = user?.profileId;
    if (!customerId) {
      Toast.show({text1: 'Please log in first', type: 'info'});
      return;
    }
    setLoaderMap({...loaderMap, [productVariantId]: true});

    try {
      await addItemToCart({productVariantId, quantity: 1}).unwrap();
      Toast.show({type: 'success', text1: 'Added to cart!'});
      setLoaderMap({...loaderMap, [productVariantId]: false});
      setCartMap(prev => ({...prev, [productVariantId]: true}));
    } catch (error) {
      setLoaderMap({...loaderMap, [productVariantId]: false});
    }
  };

  const handleCartPress = () => {
    navigation.navigate('mainHome', {screen: SCREEN_NAME.CART});
  };

  const handleToggleWishlist = async (productId: string) => {
    const customerId = user?.profileId;
    if (!customerId) {
      Toast.show({text1: 'Please log in first', type: 'info'});
      return;
    }

    if (wishlistMap.has(productId)) {
      const wishlistId = wishlistMap.get(productId);
      if (wishlistId) {
        await removeItemFromWishlist(wishlistId).unwrap();
        const updated = new Map(wishlistMap);
        updated.delete(productId);
        setWishlistMap(updated);
        Toast.show({text1: 'Removed from wishlist', type: 'info'});
      }
    } else {
      const result = await addItemToWishlist(productId).unwrap();
      const updated = new Map(wishlistMap);
      updated.set(productId, result.id ?? '');
      setWishlistMap(updated);
      Toast.show({text1: 'Added to wishlist', type: 'success'});
    }
  };

  useEffect(() => {
    if (variants?.length) {
      const map = new Map();
      variants.forEach(item => {
        if (item.wishlist?.id && item.id) {
          map.set(item.id.toString(), item.wishlist.id);
        }
      });
      setWishlistMap(map);
    }
  }, [variants]);

  return (
    <FlatList
      horizontal
      showsHorizontalScrollIndicator={false}
      keyExtractor={item => item.id.toString()}
      data={variants}
      contentContainerStyle={styles.flatListContainer}
      renderItem={({item}) => (
        <View style={styles.productCardWidth}>
          <ProductCard
            onPress={() => handleProductClick(item.id)}
            id={item.id}
            image={item.featuredAsset?.previewUrl || ''}
            name={item.name}
            price={item.productVariantPrice?.price?.toString()}
            originalPrice={item.productVariantPrice?.mrp?.toString()}
            discount={calculateDiscount(
              Number(item.productVariantPrice?.mrp),
              Number(item.productVariantPrice?.price),
            )}
            rating={item.reviews?.[0]?.rating || 4}
            shortDescription={item.product?.description}
            isWishlisted={wishlistMap.has(item.id.toString())}
            onWishlistToggle={() => handleToggleWishlist(item.id)}
            onAddToCart={() => handleAddToCart(item)}
            onGoToCart={() => handleCartPress()}
            isloading={loaderMap[item.id] || false}
            isAddedToCart={cartMap[item.id] || false}
          />
        </View>
      )}
    />
  );
};

const styles = StyleSheet.create({
  flatListContainer: {gap: 10, paddingVertical: 10},

  productCardWidth: {width: 185},
});
