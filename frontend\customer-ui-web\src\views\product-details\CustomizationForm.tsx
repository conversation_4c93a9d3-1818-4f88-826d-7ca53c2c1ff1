import {useFormik} from 'formik';
import * as Yup from 'yup';
import {
  Box,
  Button,
  Checkbox,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormLabel,
  MenuItem,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from '@mui/material';
import {ProductCustomField} from 'types/product';
import {
  useAddItemToCartMutation,
  useUpdateProductCustomizationsMutation,
} from 'redux/ecom/cartApiSlice';
import {SnackbarProps} from 'types/snackbar';
import {openSnackbar} from 'api/snackbar';
import {useEffect, useState} from 'react';
import {useRouter} from 'next/navigation';
import {productCustomizationsAtom} from 'utils/atoms/productCustomizationAtom';
import {useSetAtom} from 'jotai';
import {useAtomValue} from 'jotai';

type Props = {
  fields: ProductCustomField[];
  productVariantId: string;
  isInCart?: boolean;
  cartId?: string;
};

const ProductCustomizationForm: React.FC<Props> = ({
  fields,
  productVariantId,
  isInCart,
  cartId,
}) => {
  const setProductCustomizations = useSetAtom(productCustomizationsAtom);
  const existingCustomizations = useAtomValue(productCustomizationsAtom);
  const router = useRouter();
  const initialValues = fields.reduce(
    (acc, field) => {
      const storedValue =
        existingCustomizations?.[productVariantId]?.[field.id];
      acc[field.name] =
        storedValue ?? (field.fieldType === 'checkbox' ? false : '');
      return acc;
    },
    {} as Record<string, any>,
  );

  const [addItemToCart, {isLoading}] = useAddItemToCartMutation();
  const [updateCustomizations, {isLoading: isSaving}] =
    useUpdateProductCustomizationsMutation();

  const [isAddedToCart, setIsAddedToCart] = useState(false);

  const handleGoToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    router.push('/cart');
  };

  const validationSchema = Yup.object(
    fields.reduce(
      (schema, field) => {
        let validator: Yup.AnySchema;

        if (field.fieldType === 'number') {
          validator = Yup.number()
            .typeError(`${field.label} must be a number`)
            .transform((value, originalValue) =>
              String(originalValue).trim() === '' ? undefined : value,
            );
        } else if (field.fieldType === 'checkbox') {
          validator = field.isRequired
            ? Yup.boolean().oneOf([true], `${field.label} is required`)
            : Yup.boolean();
        } else {
          let stringValidator = Yup.string();
          if (field.validationRegex) {
            stringValidator = stringValidator.matches(
              new RegExp(field.validationRegex),
              'Invalid format',
            );
          }
          if (field.isRequired) {
            stringValidator = stringValidator.required(
              `${field.label} is required`,
            );
          }
          validator = stringValidator;
        }

        if (field.isRequired && field.fieldType !== 'checkbox') {
          validator = validator.required(`${field.label} is required`);
        }

        schema[field.name] = validator;
        return schema;
      },
      {} as Record<string, Yup.AnySchema>,
    ),
  );

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async values => {
      const customizationValues = fields.map(field => ({
        customizationFieldId: field.id,
        value: String(values[field.name]),
      }));

      // Store per-variant customization values
      const customizationValuesById = fields.reduce(
        (acc, field) => {
          acc[field.id] = values[field.name];
          return acc;
        },
        {} as Record<string, any>,
      );

      setProductCustomizations(prev => ({
        ...prev,
        [productVariantId]: customizationValuesById,
      }));

      if (isInCart && cartId && productVariantId) {
        await updateCustomizations({
          cartId,
          data: customizationValues,
        }).unwrap();

        openSnackbar({
          open: true,
          message: 'Customizations saved successfully!',
          variant: 'alert',
          alert: {color: 'success'},
        } as SnackbarProps);
      } else {
        const cartItem = {
          productVariantId,
          quantity: 1,
          customizationValues,
        };
        await addItemToCart(cartItem).unwrap();
        setIsAddedToCart(true);

        openSnackbar({
          open: true,
          message: 'Product added to Cart',
          variant: 'alert',
          alert: {color: 'success'},
        } as SnackbarProps);
      }
    },
  });

  useEffect(() => {
    const customizationValuesById = fields.reduce(
      (acc, field) => {
        acc[field.id] = formik.values[field.name];
        return acc;
      },
      {} as Record<string, any>,
    );

    setProductCustomizations(prev => ({
      ...prev,
      [productVariantId]: customizationValuesById,
    }));
  }, [formik.values, productVariantId, setProductCustomizations]);

  return (
    <form onSubmit={formik.handleSubmit}>
      <Box display="flex" flexDirection="column" gap={3}>
        {fields.map(field => {
          const error =
            formik.touched[field.name] &&
            typeof formik.errors[field.name] === 'string'
              ? (formik.errors[field.name] as string)
              : undefined;

          switch (field.fieldType) {
            case 'text':
            case 'number':
              return (
                <TextField
                  key={field.id}
                  type={field.fieldType}
                  name={field.name}
                  label={field.label}
                  placeholder={field.placeholder}
                  fullWidth
                  value={formik.values[field.name]}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={Boolean(error)}
                  helperText={error}
                />
              );

            case 'textarea':
              return (
                <TextField
                  key={field.id}
                  name={field.name}
                  label={field.label}
                  placeholder={field.placeholder}
                  fullWidth
                  multiline
                  rows={4}
                  value={formik.values[field.name]}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={Boolean(error)}
                  helperText={error}
                />
              );

            case 'select':
            case 'dropdown':
              return (
                <>
                  <Box key={field.id} sx={{mb: 0}}>
                    <Typography
                      sx={{mb: 0.5, fontWeight: 500}}
                      component="label"
                      htmlFor={field.name}
                    >
                      {field.placeholder}
                    </Typography>
                    <TextField
                      select
                      key={field.id}
                      name={field.name}
                      fullWidth
                      value={formik.values[field.name]}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={Boolean(error)}
                      helperText={error}
                      sx={{
                        borderRadius: '25px',
                        background: 'white',
                      }}
                    >
                      {(field.productCustomizationOptions || []).map(option => (
                        <MenuItem key={option.id} value={option.value}>
                          {option.label}
                        </MenuItem>
                      ))}
                    </TextField>
                  </Box>
                </>
              );

            case 'checkbox':
              return (
                <FormControl key={field.id} error={Boolean(error)}>
                  <FormGroup>
                    <FormControlLabel
                      control={
                        <Checkbox
                          name={field.name}
                          checked={formik.values[field.name]}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                        />
                      }
                      label={field.label}
                    />
                  </FormGroup>
                  {error && <Typography color="error">{error}</Typography>}
                </FormControl>
              );

            case 'radio':
              return (
                <FormControl
                  key={field.id}
                  component="fieldset"
                  error={Boolean(error)}
                >
                  <FormLabel component="legend">{field.label}</FormLabel>
                  <RadioGroup
                    name={field.name}
                    value={formik.values[field.name]}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  >
                    {(field.productCustomizationOptions || []).map(option => (
                      <FormControlLabel
                        key={option.id}
                        value={option.value}
                        control={<Radio />}
                        label={option.label}
                      />
                    ))}
                  </RadioGroup>
                  {error && <Typography color="error">{error}</Typography>}
                </FormControl>
              );

            default:
              return (
                <TextField
                  key={field.id}
                  name={field.name}
                  label={field.label}
                  placeholder={field.placeholder}
                  fullWidth
                  value={formik.values[field.name]}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={Boolean(error)}
                  helperText={error}
                />
              );
          }
        })}

        {isAddedToCart ? (
          <Button
            onClick={handleGoToCart}
            variant="contained"
            sx={{
              backgroundColor: '#07074f',
              '&:hover': {backgroundColor: '#07074f'},
              borderRadius: 6,
              px: 4,
              fontWeight: 'bold',
              width: {xs: '100%', sm: 'auto'},
            }}
          >
            GO TO CART
          </Button>
        ) : (
          <Button
            type="submit"
            variant="contained"
            disabled={isLoading || isSaving}
            sx={{
              backgroundColor: '#00004F',
              '&:hover': {backgroundColor: '#07074f'},
              borderRadius: 6,
              px: 4,
              fontWeight: 'bold',
              width: {xs: '100%', sm: 'auto'},
            }}
          >
            {isInCart
              ? isSaving
                ? 'Saving...'
                : 'Save'
              : isLoading
                ? 'Adding...'
                : 'Add to Cart'}
          </Button>
        )}
      </Box>
    </form>
  );
};

export default ProductCustomizationForm;
