import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {SellerShippingProfile} from './seller-shipping-profile.model';

@model({
  name: 'seller_shipping_charges',
})
export class SellerShippingCharge extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'country_code',
  })
  countryCode: string;

  @property({
    type: 'string',
    name: 'state_code',
  })
  stateCode?: string;

  @property({
    type: 'boolean',
    default: false,
    name: 'is_default',
  })
  isDefault?: boolean;

  @property({
    type: 'number',
    required: true,
    name: 'base_charge',
  })
  baseCharge: number;

  @property({
    type: 'number',
    default: 0,
    name: 'additional_charge',
  })
  additionalCharge?: number;

  @property({
    type: 'number',
    name: 'free_shipping_threshold',
  })
  freeShippingThreshold?: number;

  @property({
    type: 'number',
    name: 'estimated_days_min',
  })
  estimatedDaysMin?: number;

  @property({
    type: 'number',
    name: 'estimated_days_max',
  })
  estimatedDaysMax?: number;

  @belongsTo(
    () => SellerShippingProfile,
    {keyTo: 'id'},
    {name: 'shipping_profile_id'},
  )
  shippingProfileId: string;

  constructor(data?: Partial<SellerShippingCharge>) {
    super(data);
  }
}

export interface SellerShippingChargeRelations {
  shippingProfile?: SellerShippingProfile;
}

export type SellerShippingChargeWithRelations = SellerShippingCharge &
  SellerShippingChargeRelations;
