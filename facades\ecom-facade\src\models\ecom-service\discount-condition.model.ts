import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {Discount} from './discount.model';

@model({name: 'discount_conditions'})
export class DiscountCondition extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
    required: true,
  })
  id: string;

  @property({
    type: 'number',
    name: 'threshold_amount',
    required: true,
  })
  thresholdAmount: number;

  @property({
    type: 'string',
    name: 'discount_type',
  })
  discountType: 'FLAT' | 'PERCENT';

  @property({
    type: 'number',
    name: 'discount_value',
  })
  discountValue: number;

  @property({
    type: 'string',
    name: 'condition_type',
  })
  conditionType: 'BASIC' | 'ADDITIONAL' | 'APP_ONLY';

  @property({
    type: 'boolean',
    name: 'is_app_only',
    default: false,
  })
  isAppOnly: boolean;

  @belongsTo(() => Discount, {keyTo: 'id'}, {name: 'discount_id'})
  discountId: string;

  constructor(data?: Partial<DiscountCondition>) {
    super(data);
  }
}

export interface DiscountConditionRelations {
  // describe navigational properties here
}

export type DiscountConditionWithRelations = DiscountCondition &
  DiscountConditionRelations;
