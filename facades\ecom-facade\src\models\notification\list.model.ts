import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

@model({name: 'lists'})
export class List extends UserModifiableEntity {
  @property({
    type: 'string',
    required: true,
    name: 'list_key',
  })
  listKey: string;

  @property({
    type: 'string',
    required: true,
    name: 'list_name',
  })
  listName: string;

  @property({
    type: 'string',
    required: true,
  })
  topicId: string;

  constructor(data?: Partial<List>) {
    super(data);
  }
}

export interface ListRelations {
  // add navigational properties here
}

export type ListWithRelations = List & ListRelations;
