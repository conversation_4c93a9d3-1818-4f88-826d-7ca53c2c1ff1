import { Grid, TextField, Autocomplete, Button, Box } from '@mui/material';
import MainCard from 'components/MainCard';
import { useFormik } from 'formik';
import { FC, useState, useEffect, useCallback } from 'react';
import { FeaturedProductsMetadata, SectionItem } from 'types/cms';
import { featuredProductsItemSchema } from '../../../validations/cms';
import debounce from 'lodash/debounce';
import { Save2, TickCircle } from 'iconsax-react';
import { ProductVariant } from 'types/product';
import { useLazyGetProductVariantsQuery } from 'redux/app/products/productApiSlice';

interface Props {
  data: SectionItem;
  onChange: (value: SectionItem) => void;
}

export const FeaturedProductsItem: FC<Props> = ({ data, onChange }) => {
  const [productOptions, setProductOptions] = useState<ProductVariant[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [getProductVariants, { isLoading }] = useLazyGetProductVariantsQuery();
  const [localData, setLocalData] = useState<SectionItem>(data);

  const [isSaved, setIsSaved] = useState(false);

  // Create a debounced search function
  const debouncedSearch = useCallback(
    debounce(async (term: string) => {
      if (term.length < 2) return;

      const result = await getProductVariants({
        where: {
          or: [{ name: { ilike: `%${term}%` } }, { sku: { ilike: `%${term}%` } }]
        },
        limit: 10
      }).unwrap();

      setProductOptions(result || []);
    }, 500),
    [getProductVariants]
  );

  // Effect to trigger search when searchTerm changes
  useEffect(() => {
    if (searchTerm) {
      debouncedSearch(searchTerm);
    }

    // Cleanup function
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchTerm, debouncedSearch]);

  // Update local data when props change
  useEffect(() => {
    setLocalData(data);
  }, [data]);

  // Load selected products on initial render
  useEffect(() => {
    const loadSelectedProducts = async () => {
      const productIds = (data.metadata as FeaturedProductsMetadata)?.productIds ?? [];
      if (productIds.length) {
        const result = await getProductVariants({
          where: {
            id: { inq: productIds }
          }
        }).unwrap();

        if (result) {
          setProductOptions((prevOptions) => {
            // Merge with existing options, avoiding duplicates
            const existingIds = prevOptions.map((p) => p.id);
            const newOptions = result.filter((p) => !existingIds.includes(p.id));
            return [...prevOptions, ...newOptions];
          });
        }
      }
    };

    loadSelectedProducts();
  }, [data.metadata, getProductVariants]);

  const formik = useFormik<SectionItem>({
    initialValues: localData,
    enableReinitialize: true,
    validationSchema: featuredProductsItemSchema,
    onSubmit: (values) => {
      onChange(values);
    }
  });

  const handleLocalChange = (field: string, value: any) => {
    formik.setFieldValue(field, value);
  };

  const handleSave = () => {
    formik.handleSubmit();
  };

  // Listen for form submission
  useEffect(() => {
    if (formik.isSubmitting) {
      setIsSaved(false); // During submission
    } else if (formik.submitCount > 0 && !formik.isSubmitting) {
      setIsSaved(true); // Mark as saved after successful submit
    }
  }, [formik.isSubmitting, formik.submitCount]);

  // Reset `isSaved` on form change
  useEffect(() => {
    if (isSaved && formik.dirty) {
      setIsSaved(false);
    }
  }, [formik.values]);

  return (
    <MainCard>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Autocomplete
            multiple
            loading={isLoading}
            options={productOptions}
            getOptionLabel={(option) =>
              `${typeof option === 'string' ? option : option?.name} (${typeof option === 'string' ? '' : option?.sku})`
            }
            value={productOptions.filter((p) => formik.values.metadata?.productIds?.includes(p.id)) ?? []}
            onChange={(_, newValue) => {
              handleLocalChange('metadata', {
                ...formik.values.metadata,
                productIds:
                  typeof newValue === 'string' ? newValue : newValue.map((item) => (typeof item === 'string' ? item : (item.id ?? '')))
              });
            }}
            onInputChange={(_, value) => setSearchTerm(value)}
            filterOptions={(x) => x} // Disable client-side filtering
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select Products"
                placeholder="Search products by name or SKU"
                helperText="Type at least 2 characters to search"
              />
            )}
            freeSolo
          />
        </Grid>

        <Grid item xs={12}>
          <Box display="flex" justifyContent="flex-end">
            <Button
              variant="contained"
              color={isSaved ? 'success' : 'primary'}
              startIcon={isSaved ? <TickCircle /> : <Save2 />}
              onClick={handleSave}
            >
              {isSaved ? 'Saved' : 'Save'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </MainCard>
  );
};
