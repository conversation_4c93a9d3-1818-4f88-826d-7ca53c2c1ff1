import React, {useCallback, useState} from 'react';
import {KeyboardAvoidingView, Platform, StyleSheet, View} from 'react-native';
import {
  Bubble,
  BubbleProps,
  GiftedChat,
  IMessage,
  InputToolbar,
  InputToolbarProps,
  Send,
  SendProps,
} from 'react-native-gifted-chat';
import {Icon} from 'react-native-paper';
import {SafeAreaView} from 'react-native-safe-area-context';
import customColors from '../../../theme/customColors';
import {colors} from '../../../theme/colors';
import {
  MessageNavigationProp,
  OnboardStackParamList,
} from '../../../navigations/types';
import {ChatHeader} from './components/ChatHeader';
import {RouteProp, useRoute} from '@react-navigation/native';
interface ScreenProps {
  navigation: MessageNavigationProp;
}

const MessageScreen: React.FC<ScreenProps> = navigation => {
  const route = useRoute<RouteProp<OnboardStackParamList, 'message'>>();
  const {sellerId} = route.params;
  console.log('Seller ID:', sellerId);

  const [messages, setMessages] = useState<IMessage[]>([]);

  const onSend = useCallback((newMessages: IMessage[] = []) => {
    setMessages(previousMessages => [...previousMessages, ...newMessages]);

    const userMessage = newMessages[0].text;

    const botResponse: IMessage = {
      _id: Math.random().toString(),
      text: `Echo: ${userMessage}`,
      createdAt: new Date(),
      user: {
        _id: 2,
        name: 'Bot',
      },
    };

    setTimeout(() => {
      setMessages(previousMessages => [...previousMessages, botResponse]);
    }, 1000);
  }, []);

  return (
    <SafeAreaView style={styles.safeArea}>
      <ChatHeader
        onBackPress={() => navigation.navigation.goBack()}
        user={{
          name: 'Chat With Us',
          avatar: '',
          online: false,
        }}
      />
      <GiftedChat
        messages={messages}
        onSend={onSend}
        user={{_id: 1}}
        renderBubble={renderBubble}
        renderSend={renderSend}
        renderInputToolbar={renderInputToolbar}
        renderAvatar={null}
        placeholder="Type your message..."
        alwaysShowSend
        inverted={false}
        bottomOffset={Platform.OS === 'ios' ? 20 : 0}
      />
      {Platform.OS === 'android' ? null : (
        <KeyboardAvoidingView behavior="padding" keyboardVerticalOffset={80} />
      )}
    </SafeAreaView>
  );
};
export default MessageScreen;
const renderBubble = (
  props: React.JSX.IntrinsicAttributes & BubbleProps<IMessage>,
) => (
  <Bubble
    {...props}
    wrapperStyle={{
      left: {backgroundColor: colors.gray.backGround, marginBottom: 8},
      right: {backgroundColor: colors.tertiary, marginBottom: 8},
    }}
    textStyle={{
      right: {color: customColors.white},
      left: {color: customColors.textBlack},
    }}
  />
);

const renderSend = (
  props: React.JSX.IntrinsicAttributes & SendProps<IMessage>,
) => (
  <Send {...props}>
    <View style={styles.sendButton}>
      <Icon source="send" size={25} color={colors.tertiary} />
    </View>
  </Send>
);

const renderInputToolbar = (
  props: React.JSX.IntrinsicAttributes & InputToolbarProps<IMessage>,
) => <InputToolbar {...props} containerStyle={styles.inputToolBar} />;

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: customColors.white,
  },
  sendButton: {marginRight: 20, marginBottom: 5},
  inputToolBar: {
    borderTopColor: colors.gray.dark,
    paddingVertical: 6,
    marginLeft: 20,
  },
});
