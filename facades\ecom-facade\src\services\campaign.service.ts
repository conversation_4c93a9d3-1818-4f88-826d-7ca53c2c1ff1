import {injectable, BindingScope, service} from '@loopback/core';
import {Campaign} from '../models';
import {S3HelperService} from './s3-helper.service';
import fs from 'fs';
import path from 'path';
import {promisify} from 'util';
import {restService} from '@sourceloop/core';
import {CampaignProxyType} from '../datasources/configs';

const writeFileAsync = promisify(fs.writeFile);

@injectable({scope: BindingScope.TRANSIENT})
export class CampaignService {
  constructor(
    @service(S3HelperService)
    private readonly s3HelperService: S3HelperService,
    @restService(Campaign)
    private readonly campaignProxyService: CampaignProxyType,
  ) {}

  async createCampaign(campaign: Campaign): Promise<Campaign> {
    if (campaign.type === 1) {
      const filePath = await this.createHtmlFile(
        campaign.body ?? '',
        'campaign',
      );

      const s3Key = await this.s3HelperService.uploadFileToS3FromDisk(
        filePath,
        process.env.AWS_S3_BUCKET!,
      );

      const previewUrl = `${process.env.CDN_ORIGIN}/${s3Key}`;
      campaign.body = previewUrl;
    }

    return this.campaignProxyService.create(campaign);
  }

  async sendCampaign(campaignKey: string, token: string): Promise<Campaign> {
    return this.campaignProxyService.sendCampaign(campaignKey, token);
  }

  async createHtmlFile(htmlString: string, filename: string): Promise<string> {
    try {
      const outputPath = path.resolve(__dirname, '../../src/templates');

      // Ensure the directory exists
      if (!fs.existsSync(outputPath)) {
        fs.mkdirSync(outputPath, {recursive: true});
      }

      // Create the full file path
      const fullPath = path.join(outputPath, `${filename}.html`);

      // Write the HTML file with proper UTF-8 encoding
      await writeFileAsync(fullPath, htmlString);

      return fullPath;
    } catch (error) {
      throw new Error(
        `Failed to create HTML file: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }
}
