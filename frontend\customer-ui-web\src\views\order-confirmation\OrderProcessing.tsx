import Grid from '@mui/material/Grid';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import {Location, Truck} from 'iconsax-react';
import {Box} from '@mui/material';
import {Order} from 'types/order';

const OrderDetails = ({order}: {order: Order}) => {
  const shipping = order.shippingAddress;
  const billing = order.billingAddress;
  const shippingDetails = [
    shipping.name,
    shipping.addressLine1,
    `${shipping.city}, ${shipping.state} ${shipping.zipCode}`,
    shipping.country,
    shipping.phoneNumber,
  ];

  const billingDetails = [
    billing.name,
    billing.addressLine1,
    `${billing.city}, ${billing.state} ${billing.zipCode}`,
    billing.country,
    billing.phoneNumber,
  ];

  const shippingMethodDetails = [
    'Preferred Method: U.S. Standard (normally 4-5 business days, unless otherwise noted)',
  ];

  const cardStyle = {
    height: '100%',
    minHeight: 250,
    width: '100%',
    borderRadius: 2,
    boxShadow: '0px 4px 10px rgba(0,0,0,0.1)',
    textAlign: 'left',
    transition: 'all 0.3s ease',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    '&:hover': {
      boxShadow: '0px 6px 15px rgba(0,0,0,0.15)',
    },
  };

  const contentBoxStyle = {
    display: 'flex',
    flexDirection: 'column',
    gap: 1,
  };

  return (
    <Container maxWidth="xl" sx={{px: {xs: 2, sm: 4, md: 10, lg: 15}, py: 2}}>
      <Grid container spacing={3} justifyContent="center">
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={cardStyle}>
            <CardContent>
              <Box sx={contentBoxStyle}>
                <Location size="32" color="#9E3393" variant="Bold" />
                <Typography
                  variant="h6"
                  fontSize={{xs: '1rem', sm: '1.1rem', md: '1.2rem'}}
                  fontWeight="bold"
                  color="#00004F"
                >
                  Shipping
                </Typography>
                {shippingDetails.map((detail, idx) => (
                  <Typography
                    key={idx}
                    color="text.secondary"
                    fontSize={{xs: '0.9rem', sm: '1rem'}}
                  >
                    {detail}
                  </Typography>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Card sx={cardStyle}>
            <CardContent>
              <Box sx={contentBoxStyle}>
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  color="#00004F"
                  fontSize={{xs: '1rem', sm: '1.1rem', md: '1.2rem'}}
                  mt={{xs: 2, sm: 3, md: 4}}
                >
                  Billing Details
                </Typography>
                {billingDetails.map((detail, idx) => (
                  <Typography
                    key={idx}
                    color="text.secondary"
                    fontSize={{xs: '0.9rem', sm: '1rem'}}
                  >
                    {detail}
                  </Typography>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Card sx={cardStyle}>
            <CardContent>
              <Box sx={contentBoxStyle}>
                <Truck size="32" color="#9E3393" variant="Bold" />
                <Typography
                  variant="subtitle1"
                  fontWeight="bold"
                  color="#00004F"
                  fontSize={{xs: '1rem', sm: '1.1rem', md: '1.2rem'}}
                >
                  Shipping Method
                </Typography>
                {shippingMethodDetails.map((detail, idx) => (
                  <Typography
                    key={idx}
                    color="text.secondary"
                    fontSize={{xs: '0.9rem', sm: '1rem'}}
                  >
                    {detail}
                  </Typography>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default OrderDetails;
