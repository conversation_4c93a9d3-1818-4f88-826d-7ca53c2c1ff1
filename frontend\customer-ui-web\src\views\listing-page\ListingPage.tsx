'use client';

import {Grid} from '@mui/material';
import {getSearchParam} from 'constants/product';
import {useSearchParams} from 'next/navigation';
import {useEffect, useMemo, useState} from 'react';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import {useAppSelector} from 'redux/hooks';
import {useFiltersQuery, useLazySearchQuery} from 'redux/search/searchApiSlice';
import {FilterValue, IFilterWithKeyword} from 'types/filter';
import ProductFilters from 'views/products/ProductFilters';
import ProductList from 'views/products/ProductListing';
import ScrollableCategoryList from 'views/products/ProductSubCategory';
import FilterSortProducts from 'views/products/QuikFilter';

export default function ListingPage() {
  const searchParams = useSearchParams();
  const searchQuery = searchParams.get('search')?.toLowerCase() || '';
  const facetValueIdsParam = searchParams.get('facetValueIds');
  const collectionIdsParam = searchParams.get('collectionIds');

  const facetValueIds = facetValueIdsParam?.split(',').filter(Boolean);
  const collectionIds = collectionIdsParam?.split(',').filter(Boolean);

  const [appliedFilters, setAppliedFilters] = useState<FilterValue[]>([]);

  const isLoggedIn = useAppSelector(state => state.auth.isLoggedIn);
  const {data: user} = useGetUserQuery(undefined, {skip: !isLoggedIn});

  const searchParam = getSearchParam(isLoggedIn, user?.profileId);

  const {data: filters} = useFiltersQuery(
    {
      keyword: searchQuery,
      facetValueIds,
      collectionIds,
    },
    {
      skip: !searchQuery && !facetValueIds?.length && !collectionIds?.length,
    },
  );

  const facetFilters = useMemo(() => {
    if (!filters) {
      return [];
    }
    return filters?.filter(item => item.isFacet)?.flatMap(item => item.values);
  }, [filters]);

  const [fetchProductsApi, {data: products, isLoading, isFetching}] =
    useLazySearchQuery();

  useEffect(() => {
    const params: IFilterWithKeyword = {...searchParam};
    if (searchQuery) {
      params.keyword = searchQuery;
    }

    if (facetValueIds?.length) {
      params.facetValueIds = facetValueIds;
    }

    if (collectionIds?.length) {
      params.collectionIds = collectionIds;
    }

    params.where = !appliedFilters.length ? undefined : prePareFilter();

    if (!isFetching || !isLoading) {
      fetchProductsApi(params).unwrap();
    }
  }, [searchQuery, facetValueIdsParam, collectionIdsParam, appliedFilters]);

  const toggleFilter = (selectedFilter: FilterValue) => {
    setAppliedFilters(prev => {
      const exists = prev.some(
        f =>
          f.label === selectedFilter.label && f.value === selectedFilter.value,
      );
      if (exists) {
        return prev.filter(
          f =>
            !(
              f.label === selectedFilter.label &&
              f.value === selectedFilter.value
            ),
        );
      } else {
        return [...prev, selectedFilter];
      }
    });
  };

  const prePareFilter = () => {
    const ids = appliedFilters.flatMap(item => item.productVariantIds);
    if (!ids.length) {
      return undefined;
    }
    return {id: {inq: ids}};
  };

  return (
    <div>
      <Grid container>
        <ScrollableCategoryList />
        <Grid item>
          <FilterSortProducts facets={facetFilters} />
        </Grid>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4} md={3} sx={{marginTop: '1rem'}}>
            <ProductFilters
              filters={filters ?? []}
              onToggleFilter={toggleFilter}
              appliedFilters={appliedFilters}
            />
          </Grid>
          <Grid item xs={12} sm={8} md={9}>
            <ProductList
              products={products}
              isLoading={isLoading}
              isFetching={isFetching}
            />
          </Grid>
        </Grid>
      </Grid>
    </div>
  );
}
