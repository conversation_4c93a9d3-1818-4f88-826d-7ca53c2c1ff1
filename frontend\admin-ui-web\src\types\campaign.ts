import { EditorState } from 'draft-js';
import { NotificationType } from 'enums/notification-type.enum';

export interface Campaign {
  id?: string;
  campaignKey?: string;
  name: string;
  type: NotificationType;
  subject?: string;
  clickAction?: string;
  body: EditorState | string;
  isDraft: boolean;
  scheduledAt?: string;
  createdOn?: string;
  modifiedOn?: string;
  deleted?: boolean;
  deletedOn?: string;
  deletedBy?: string;
}

export interface CampaignViewData {
  id?: string;
  subject?: string;
  body: EditorState | string;
  isDraft?: boolean;
  isCritical?: boolean;
  type: number;
  sent?: string | null;
  groupKey?: string | null;
  options?: any;
  name?: string;
}
