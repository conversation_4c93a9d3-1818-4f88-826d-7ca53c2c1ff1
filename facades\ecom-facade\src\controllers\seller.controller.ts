import {
  post,
  getModelSchemaRef,
  response,
  requestBody,
  get,
  param,
  del,
  patch,
  Request,
  put,
  RestBindings,
} from '@loopback/rest';
import {
  CashFreeVendor,
  SampleProductImage,
  Seller,
  SellerBankDetailsDto,
  SellerDetailsDto,
  SellerStatusDto,
} from '../models';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {inject, service} from '@loopback/core';
import {SellerService} from '../services';
import {PermissionKeys} from '@local/core';
import {CONTENT_TYPE, restService, STATUS_CODE} from '@sourceloop/core';
import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {SellerProxyType} from '../datasources/configs';

const basePath = '/sellers';

export class SellerController {
  private token: string;
  constructor(
    @service(SellerService)
    public sellerService: SellerService,
    @restService(Seller)
    private readonly sellerProxyService: SellerProxyType,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
  ) {
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    }
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateSeller]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Seller model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Seller)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Seller, {
            title: 'NewSeller',
            exclude: ['id', 'sellerId', 'verificationCode'],
            partial: true,
          }),
        },
      },
    })
    seller: Partial<Omit<Seller, 'id, sellerId,verificationCode'>>,
  ): Promise<Seller> {
    return this.sellerProxyService.create(seller);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateSeller]})
  @post(`${basePath}/on-board`)
  @response(STATUS_CODE.OK, {
    description: 'Seller onboarding success',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Seller)}},
  })
  async sellerOnBoard(
    @requestBody.file()
    _req: Request,
  ): Promise<Seller> {
    return this.sellerService.onboardSeller();
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'Seller model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(@param.where(Seller) where?: Where<Seller>): Promise<Count> {
    return this.sellerProxyService.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Seller model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Seller, {includeRelations: true}),
        },
      },
    },
  })
  async find(@param.filter(Seller) filter?: Filter<Seller>): Promise<Seller[]> {
    return this.sellerService.findWithPreSignedUrls(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'Seller model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Seller, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Seller, {exclude: 'where'})
    filter?: FilterExcludingWhere<Seller>,
  ): Promise<Seller> {
    const seller = await this.sellerProxyService.findById(id, filter);

    if (seller.sampleProductImages) {
      seller.sampleProductImages = (await Promise.all(
        seller.sampleProductImages.map(async sampleProductImage => ({
          ...sampleProductImage,
          thumbnail: sampleProductImage.thumbnail.map(
            image => `${process.env.CDN_ORIGIN}/${image}`,
          ),
        })),
      )) as SampleProductImage[];
    }

    return seller;
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Seller PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerDetailsDto, {partial: true}),
        },
      },
    })
    seller: Partial<SellerDetailsDto>,
  ): Promise<void> {
    return this.sellerProxyService.updateSellerById(id, seller, this.token);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(`${basePath}/{id}/status`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Seller PATCH success',
  })
  async updateStatusById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerStatusDto),
        },
      },
    })
    seller: SellerStatusDto,
  ): Promise<void> {
    await this.sellerService.updateStatusById(id, seller);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Seller PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() seller: Seller,
  ): Promise<void> {
    await this.sellerProxyService.replaceById(id, seller);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteSeller]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Seller DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.sellerProxyService.deleteById(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @post(`${basePath}/{id}/bank-details`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Add bank details for seller',
  })
  async addBankDetails(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerBankDetailsDto),
        },
      },
    })
    bankDetails: SellerBankDetailsDto,
  ): Promise<void> {
    return this.sellerService.addBankDetails(id, bankDetails);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/bank-details`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Payment Vendor response',
        schema: getModelSchemaRef(CashFreeVendor),
      },
    },
  })
  async getVendor(): Promise<CashFreeVendor> {
    return this.sellerService.getVendor();
  }
}
