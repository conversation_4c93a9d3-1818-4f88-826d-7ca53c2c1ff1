import {ApiSliceIdentifier} from '../../constants/enums';
import {buildFilterParams, IFilter} from '../../types/api';
import {AddressDto, Customer} from '../../types/customerApi';
import {apiSlice} from '../apiSlice';

export const customerApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getCustomerById: builder.query<Customer, {id: string; filter?: IFilter}>({
      query: ({id, filter}) => ({
        url: `/addresses/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
        params: {
          filter: buildFilterParams(filter ?? undefined),
        },
      }),
    }),
    getAddresses: builder.query<AddressDto[], string>({
      query: (customerId: string) => ({
        url: '/addresses',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
        params: {
          filter: JSON.stringify({
            where: {customerId},
          }),
        },
      }),
    }),
    createCustomerAddress: builder.mutation<void, AddressDto>({
      query: formData => ({
        url: '/addresses',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
        body: formData,
      }),
    }),
    updateCustomerById: builder.mutation<void, {id: string; data: AddressDto}>({
      query: ({id, data}) => ({
        url: `/addresses/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
        body: data,
      }),
    }),
    deleteCustomerById: builder.mutation<void, string>({
      query: id => ({
        url: `/addresses/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_SERVICE,
      }),
    }),
  }),
});

export const {
  useCreateCustomerAddressMutation,
  useGetCustomerByIdQuery,
  useUpdateCustomerByIdMutation,
  useGetAddressesQuery,
  useDeleteCustomerByIdMutation,
} = customerApiSlice;
