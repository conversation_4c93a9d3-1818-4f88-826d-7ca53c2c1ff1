import React from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import {Icon} from 'react-native-paper';
import {AddressDto} from '../../../../types/customerApi';
import customColors from '../../../../theme/customColors';
import {colors} from '../../../../theme/colors';

interface Props {
  address: AddressDto;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onEdit: (address: AddressDto) => void;
}

const AddressCard: React.FC<Props> = ({
  address,
  isSelected,
  onSelect,
  onEdit,
}) => {
  const handleSelect = () => {
    if (address.id) onSelect(address.id);
  };

  return (
    <TouchableOpacity
      onPress={handleSelect}
      activeOpacity={0.9}
      style={[styles.block, isSelected && styles.selectedBlock]}>
      <View style={styles.rowBetween}>
        <View style={styles.addressDetails}>
          <Text style={[styles.text, isSelected && styles.selectedText]}>
            {address.name}
          </Text>
          <Text style={[styles.text, isSelected && styles.selectedText]}>
            {address.addressLine1}
            {address.addressLine2 ? `, ${address.addressLine2}` : ''}
          </Text>
          <Text style={[styles.text, isSelected && styles.selectedText]}>
            {address.city}, {address.state} - {address.zipCode}
          </Text>
          <Text style={[styles.text, isSelected && styles.selectedText]}>
            Phone: {address.phoneNumber}
          </Text>
        </View>

        <TouchableOpacity
          onPress={() => onEdit(address)}
          hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}>
          <Icon
            source="pencil-box-multiple"
            size={20}
            color={isSelected ? customColors.white : colors.tertiary}
          />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

export default AddressCard;

const styles = StyleSheet.create({
  block: {
    backgroundColor: colors.gray.light,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  selectedBlock: {
    backgroundColor: colors.tertiary,
  },
  rowBetween: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  addressDetails: {
    flexShrink: 1,
    gap: 4,
  },
  text: {
    fontSize: 14,
    color: customColors.textBlack,
  },
  selectedText: {
    color: customColors.white,
  },
});
