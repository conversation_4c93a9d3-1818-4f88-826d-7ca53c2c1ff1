import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from 'redux/apiSlice';

import { SupportContactInfo } from 'types/admin';

export const supportApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getSupport: builder.query<
      SupportContactInfo[],
      {
        limit: number;
        skip?: number;
        order?: Array<Record<string, unknown> | string>;
        where?: Record<string, unknown>;
        fields?: Record<string, boolean>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({ limit, skip, order = ['createdOn DESC'], where, fields, include }) => ({
        url: '/support',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            limit,
            offset: skip,
            order,
            where,
            fields,
            include
          })
        }
      })
    }),
    createSupport: builder.mutation<void, Partial<SupportContactInfo>>({
      query: (payload) => ({
        url: '/support',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: payload
      })
    }),
    getSupportById: builder.query<SupportContactInfo, { id: string; include?: Array<Record<string, unknown> | string> }>({
      query: ({ id, include }) => ({
        url: `/support/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: include ? { filter: JSON.stringify({ include }) } : undefined
      })
    }),
    removeSupport: builder.mutation<void, string>({
      query: (id) => ({
        url: `/support/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    updateSupport: builder.mutation<void, { id: string; data: Partial<SupportContactInfo> }>({
      query: ({ id, data }) => ({
        url: `/support/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),
    getSupportCount: builder.query<{ count: number }, void>({
      query: () => ({
        url: '/support/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    })
  }),
  overrideExisting: false
});

export const {
  useCreateSupportMutation,
  useGetSupportByIdQuery,
  useRemoveSupportMutation,
  useUpdateSupportMutation,
  useGetSupportCountQuery,
  useGetSupportQuery
} = supportApiSlice;
