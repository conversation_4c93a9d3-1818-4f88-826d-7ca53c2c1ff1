import {ApiSliceIdentifier} from 'enums/api.enum';
import {buildFilterParams, Count, IFilter} from 'types/api';
import {apiSlice} from 'redux/apiSlice';
import { Collection } from 'types/product';

export const collectionApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getCollections: builder.query<Collection[], IFilter>({
      query: filter => ({
        url: '/collections',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined),
        },
      }),
    }),
    getCollectionCount: builder.query<
      Count,
      {
        where?: Record<string, unknown>;
        include?: Array<Record<string, unknown> | string>;
      }
    >({
      query: ({where, include}) => ({
        url: '/collections/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: JSON.stringify({
            where,
            include,
          }),
        },
      }),
    }),
  }),
});

export const {
  useGetCollectionsQuery,
  useGetCollectionCountQuery,
  useLazyGetCollectionsQuery,
} = collectionApiSlice;
