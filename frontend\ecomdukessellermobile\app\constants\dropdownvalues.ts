export const radioButtonOptions = [
  {value: '1', title: 'Yes'},
  {value: '0', title: 'No'},
];
export const firmTypes = [
  {
    value: 'Individual/Sole Proprietorship',
    title: 'Individual/Sole Proprietorship',
    description: 'PAN Card or Bank Details need to be uploaded.',
  },
  {
    value: 'Partnership firm',
    title: 'Partnership firm',
    description:
      'PAN of the Partnership firm, Partnership Deed, Bank details need to be uploaded.',
  },
  {
    value: 'Limited Liability Partnership (LLP)',
    title: 'Limited Liability Partnership (LLP)',
    description:
      'LLP Pan card, LLP Agreement, Certificate of Incorporation need to be uploaded.',
  },
  {
    value: 'Private Limited Company (Pvt Ltd)',
    title: 'Private Limited Company (Pvt Ltd)',
    description:
      'Copy of certificate of Incorporation of Pvt Ltd, Copy of Memorandum of association, Company PAN card need to be uploaded.',
  },
];

export const accountTypes = [
  {label: 'Saving', value: 'Saving'},
  {
    label: 'Current',
    value: 'Current',
  },
];

export const businessTypes = [
  {label: 'Grocery', value: 'Grocery'},
  {label: 'Jewellery', value: 'Jewellery'},
  {label: 'Miscellaneous', value: 'Miscellaneous'},
  {label: 'Web host/Domain seller', value: 'Web host/Domain seller'},
  {label: 'E-commerce', value: 'E-commerce'},
  {label: 'Online Gaming', value: 'Online Gaming'},
  {
    label: 'Society/Trust/Club/Association',
    value: 'Society/Trust/Club/Association',
  },
  {label: 'Mutual funds/Broking', value: 'Mutual funds/Broking'},
  {label: 'B2B', value: 'B2B'},
  {label: 'Real Estate', value: 'Real Estate'},
  {label: 'Housing', value: 'Housing'},
  {label: 'Rentals', value: 'Rentals'},
  {label: 'Utilities', value: 'Utilities'},
  {label: 'Travel and Hospitality', value: 'Travel and Hospitality'},
  {label: 'Education', value: 'Education'},
  {label: 'Food and Beverages', value: 'Food and Beverages'},
  {
    label: 'NBFCs/Organizations into Lending',
    value: 'NBFCs/Organizations into Lending',
  },
  {label: 'Chit Funds', value: 'Chit Funds'},
  {label: 'Non Profit/NGO', value: 'Non Profit/NGO'},
  {label: 'Financial Services', value: 'Financial Services'},
  {label: 'Government', value: 'Government'},
  {label: 'Readymade', value: 'Readymade'},
  {label: 'SaaS', value: 'SaaS'},
  {
    label:
      'Professional Services (Doctors, Lawyers, Architects, CAs, and other Professionals)',
    value:
      'Professional Services (Doctors, Lawyers, Architects, CAs, and other Professionals)',
  },
  {label: 'Open and Semi Open Wallet', value: 'Open and Semi Open Wallet'},
  {
    label: 'Social Media and Entertainment',
    value: 'Social Media and Entertainment',
  },
  {label: 'Pan shop', value: 'Pan shop'},
  {label: 'Telecom', value: 'Telecom'},
  {label: 'Digital Goods', value: 'Digital Goods'},
  {label: 'Insurance', value: 'Insurance'},
  {label: 'Pharmacy', value: 'Pharmacy'},
  {label: 'Healthcare', value: 'Healthcare'},
  {label: 'Retail and Shopping', value: 'Retail and Shopping'},
  {label: 'Gaming', value: 'Gaming'},
  {label: 'Logistics', value: 'Logistics'},
];
