import {model, property, belongsTo} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {Chat} from './chat.model';
import {SenderType} from '@local/core';

@model()
export class ChatMessage extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
    name: 'sender_id',
  })
  senderId: string;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      enum: Object.values(SenderType),
    },
    name: 'sender_type',
  })
  senderType: string;

  @property({
    type: 'string',
    required: true,
  })
  message: string;

  @property({
    type: 'boolean',
    default: false,
  })
  read?: boolean;

  @belongsTo(() => Chat)
  chatId: string;

  constructor(data?: Partial<ChatMessage>) {
    super(data);
  }
}
