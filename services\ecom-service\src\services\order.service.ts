import {injectable, BindingScope, inject} from '@loopback/core';
import {
  InventoryMovement,
  Order,
  OrderDto,
  OrderItemDto,
  OrderLineItem,
} from '../models';
import {repository} from '@loopback/repository';
import {
  CartRepository,
  ConfigurationRepository,
  CustomizationValueRepository,
  DukeCoinRepository,
  InventoryItemRepository,
  InventoryMovementRepository,
  OrderLineItemRepository,
  OrderRepository,
  ProductVariantRepository,
  PromoCodeRepository,
} from '../repositories';
import {Transaction} from '@loopback/sequelize';
import {HttpErrors} from '@loopback/rest';
import {
  DEFAULT_CURRENCY,
  InventoryMovementSourceType,
  InventoryMovementType,
  PREFIXES,
  TransactionType,
} from '@local/core';
import {IAuthUserWithPermissions, ILogger, LOGGER} from '@sourceloop/core';
import {PromoType} from '../enums/promo-type.enum';
import {AuthenticationBindings} from 'loopback4-authentication';

@injectable({scope: BindingScope.TRANSIENT})
export class OrderService {
  constructor(
    @repository(OrderRepository)
    private readonly orderRepository: OrderRepository,
    @repository(CartRepository)
    private readonly cartRepository: CartRepository,
    @repository(PromoCodeRepository)
    private readonly promoCodeRepository: PromoCodeRepository,
    @repository(OrderLineItemRepository)
    private readonly orderLineItemRepository: OrderLineItemRepository,
    @repository(InventoryItemRepository)
    private readonly inventoryItemRepository: InventoryItemRepository,
    @repository(InventoryMovementRepository)
    private readonly inventoryMovementRepository: InventoryMovementRepository,
    @repository(ProductVariantRepository)
    private readonly productVariantRepository: ProductVariantRepository,
    @inject(LOGGER.LOGGER_INJECT) public logger: ILogger,
    @repository(CustomizationValueRepository)
    public customizationValueRepository: CustomizationValueRepository,
    @repository(ConfigurationRepository)
    public configurationRepository: ConfigurationRepository,
    @repository(DukeCoinRepository)
    public dukeCoinRepository: DukeCoinRepository,
    @inject(AuthenticationBindings.CURRENT_USER)
    private readonly currentUser: IAuthUserWithPermissions,
  ) {}

  async generateOrderId(): Promise<string> {
    const lastCreatedEntry =
      await this.orderRepository.findOneIncludeSoftDelete({
        order: ['created_on DESC'],
        fields: {orderId: true},
      });

    if (!lastCreatedEntry) {
      return `${PREFIXES.ORDER}-000001`;
    }
    const sequenceNumber = Number(lastCreatedEntry.orderId.split('-')[1]);
    if (!sequenceNumber || isNaN(sequenceNumber)) {
      throw HttpErrors.BadRequest('Failed to generate order Id');
    }
    return `${PREFIXES.ORDER}-${(sequenceNumber + 1)
      .toString()
      .padStart(6, '0')}`;
  }

  async createOrder(
    order: Omit<
      OrderDto,
      'id' | 'totalAmount' | 'currency' | 'discountAmount' | 'payableAmount'
    >,
  ): Promise<Order> {
    const {items, ...rest} = order;
    const transaction = await this.orderRepository.dataSource.beginTransaction({
      isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,
    });

    try {
      const cart = await this.cartRepository.findById(order.cartId, undefined, {
        transaction,
      });

      const promoCodeId = cart.promoCodeId;
      const dukeCoinsApplied = cart.ecomDukeCoinsApplied ?? 0;
      const orderId = await this.generateOrderId();

      const newOrder = await this.orderRepository.create(
        {
          ...rest,
          totalAmount: 0,
          orderId,
          promoCodeId,
          ecomDukeCoinsUsed: dukeCoinsApplied,
        },
        {transaction},
      );

      const orderLineItems = await this.buildOrderItem(
        items,
        newOrder.id,
        transaction,
      );
      const createdOrderItems = await this.orderLineItemRepository.createAll(
        orderLineItems,
        {transaction},
      );

      const totalAmount = orderLineItems.reduce(
        (sum, item) => sum + (item.totalPrice ?? 0),
        0,
      );

      let discountAmount = 0;
      if (promoCodeId) {
        const promoCode = await this.promoCodeRepository.findById(
          promoCodeId,
          undefined,
          {transaction},
        );

        if (promoCode?.isActive) {
          if (promoCode.type === PromoType.Flat) {
            discountAmount = promoCode.value ?? 0;
          } else if (promoCode.type === PromoType.Percentage) {
            const percentageDiscount =
              (totalAmount * (promoCode.value ?? 0)) / 100;
            discountAmount = promoCode.maxDiscountCap
              ? Math.min(percentageDiscount, promoCode.maxDiscountCap)
              : percentageDiscount;
          }

          if (discountAmount > totalAmount) {
            discountAmount = totalAmount;
          }
        }
      }

      const userTenantId = this.currentUser?.tenantId;

      if (!userTenantId) {
        throw new HttpErrors.NotFound('UserTenant not found for this customer');
      }

      const afterPromo = totalAmount - discountAmount;
      const payableAmount = Math.max(afterPromo - dukeCoinsApplied, 0);

      await this.orderRepository.updateById(
        newOrder.id,
        {
          totalAmount,
          discountAmount,
          ecomDukeCoinsUsed: dukeCoinsApplied,
          payableAmount,
          currency: DEFAULT_CURRENCY,
        },
        {transaction},
      );

      const rawValue = cart.ecomDukeCoinsApplied ?? 0;
      const coinsToRedeem = Number(rawValue);

      if (coinsToRedeem > 0) {
        await this.dukeCoinRepository.create(
          {
            userTenantId,
            coins: 0,
            coinsChanged: coinsToRedeem,
            transactionType: TransactionType.Redeem,
            description: 'Redeemed coins',
          },
          {transaction},
        );
      }

      const customizations = await this.customizationValueRepository.find(
        {
          where: {cartId: newOrder.cartId},
          include: [{relation: 'customizationField'}],
        },
        {transaction},
      );

      const updates = customizations.map(cust => {
        const matchingItem = createdOrderItems.find(
          item =>
            item.productVariantId === cust.customizationField?.productVariantId,
        );
        if (!matchingItem) return null;

        return this.customizationValueRepository.updateById(
          cust.id!,
          {orderLineItemId: matchingItem.id},
          {transaction},
        );
      });

      await Promise.all(updates.filter(Boolean));

      const newOrderRes = await this.orderRepository.findById(
        newOrder.id,
        undefined,
        {transaction},
      );

      await transaction.commit();
      return newOrderRes;
    } catch (error) {
      this.logger.error('Error while creating order:', error);
      await transaction.rollback();
      throw error;
    }
  }

  private async buildOrderItem(
    items: OrderItemDto[],
    orderId: string,
    transaction: Transaction,
  ): Promise<OrderLineItem[]> {
    const variantIds = items.map(item => item.productVariantId);

    const productVariants = await this.productVariantRepository.find({
      where: {id: {inq: variantIds}},
      fields: {id: true, name: true},
      include: [
        {
          relation: 'productVariantPrice',
          scope: {
            fields: {id: true, price: true},
          },
        },
        {
          relation: 'product',
          scope: {
            fields: {id: true, sellerId: true},
          },
        },
      ],
    });
    const variantMap = new Map(productVariants.map(v => [v.id, v]));

    // Fetch all relevant inventories
    const inventories = await this.inventoryItemRepository.find({
      where: {
        productVariantId: {inq: variantIds},
        stockOnHand: {gt: 0},
      },
      order: ['stockOnHand DESC'],
      fields: {
        id: true,
        productVariantId: true,
        stockOnHand: true,
        stockAllocated: true,
        warehouseId: true,
      },
    });

    // STEP 1: Stock check before allocation
    for (const item of items) {
      const variantInventories = inventories.filter(
        inv => inv.productVariantId === item.productVariantId,
      );

      const totalStock = variantInventories.reduce(
        (sum, inv) => sum + inv.stockOnHand,
        0,
      );

      const variant = variantMap.get(item.productVariantId);
      if (!variant) {
        throw new HttpErrors.BadRequest(
          `Variant not found for ID ${item.productVariantId}`,
        );
      }

      const variantName =
        variant.name.length > 30
          ? `${variant.name.slice(0, 27)}...`
          : variant.name;

      if (totalStock < item.quantity) {
        throw new HttpErrors.BadRequest(
          `Insufficient stock for "${variantName}". Requested- ${item.quantity}, Available- ${totalStock}`,
        );
      }
    }

    // STEP 2: Proceed with allocation
    const orderLineItems: OrderLineItem[] = [];
    const inventoryUpdates: Promise<unknown>[] = [];
    const inventoryMovements: InventoryMovement[] = [];

    for (const item of items) {
      let remainingQty = item.quantity;
      const variant = variantMap.get(item.productVariantId);

      if (!variant?.productVariantPrice) {
        throw new HttpErrors.BadRequest(
          `Product variant ${item.productVariantId} not found or missing price.`,
        );
      }

      const relatedInventories = inventories.filter(
        inv => inv.productVariantId === item.productVariantId,
      );

      for (const inventory of relatedInventories) {
        if (remainingQty <= 0) break;

        const allocatableQty = Math.min(inventory.stockOnHand, remainingQty);

        inventory.stockOnHand -= allocatableQty;
        inventory.stockAllocated += allocatableQty;

        inventoryUpdates.push(
          this.inventoryItemRepository.updateById(inventory.id, inventory, {
            transaction,
          }),
        );

        inventoryMovements.push(
          new InventoryMovement({
            type: InventoryMovementType.ALLOCATE,
            quantity: allocatableQty,
            sourceType: InventoryMovementSourceType.ORDER,
            sourceId: orderId,
            inventoryItemId: inventory.id,
            notes: `Allocated for order ${orderId}`,
          }),
        );

        orderLineItems.push(
          new OrderLineItem({
            quantity: allocatableQty,
            unitPrice: variant.productVariantPrice.price,
            totalPrice: allocatableQty * variant.productVariantPrice.price,
            orderId,
            productVariantId: item.productVariantId,
            warehouseId: inventory.warehouseId,
            sellerId: variant.product?.sellerId,
          }),
        );

        remainingQty -= allocatableQty;
      }
    }

    await Promise.all([
      ...inventoryUpdates,
      this.inventoryMovementRepository.createAll(inventoryMovements, {
        transaction,
      }),
    ]);

    return orderLineItems;
  }

  async deleteOrder(orderId: string): Promise<void> {
    const transaction = await this.orderRepository.dataSource.beginTransaction({
      isolationLevel: Transaction.ISOLATION_LEVELS.READ_COMMITTED,
    });

    try {
      // Fetch order and line items
      const order = await this.orderRepository.findById(orderId, {
        include: ['orderLineItems'],
      });

      if (!order) {
        throw new HttpErrors.NotFound(`Order ${orderId} not found`);
      }

      const results = await Promise.all(
        (order.orderLineItems ?? []).map(item =>
          this.handleInventoryDeallocation(item, orderId, transaction),
        ),
      );

      const inventoryUpdates = results.map(r => r.updatePromise);
      const inventoryMovements = results.map(r => r.movement);

      await Promise.all([
        ...inventoryUpdates,
        this.inventoryMovementRepository.createAll(inventoryMovements, {
          transaction,
        }),
      ]);

      await Promise.all([
        ...inventoryUpdates,
        this.inventoryMovementRepository.createAll(inventoryMovements, {
          transaction,
        }),
      ]);

      // Delete all line items
      await this.orderLineItemRepository.deleteAll({orderId}, {transaction});

      // Delete order
      await this.orderRepository.deleteById(orderId, {transaction});

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  }

  private async handleInventoryDeallocation(
    item: OrderLineItem,
    orderId: string,
    transaction: Transaction,
  ): Promise<{updatePromise: Promise<unknown>; movement: InventoryMovement}> {
    const inventory = await this.inventoryItemRepository.findOne({
      where: {
        productVariantId: item.productVariantId,
        warehouseId: item.warehouseId,
      },
    });

    if (!inventory) {
      throw new HttpErrors.NotFound(
        `Inventory not found for variant ${item.productVariantId} in warehouse ${item.warehouseId}`,
      );
    }

    // Revoke allocation
    inventory.stockOnHand += item.quantity;
    inventory.stockAllocated -= item.quantity;

    const updatePromise = this.inventoryItemRepository.updateById(
      inventory.id,
      inventory,
      {transaction},
    );

    const movement = new InventoryMovement({
      type: InventoryMovementType.DEALLOCATE,
      quantity: item.quantity,
      sourceType: InventoryMovementSourceType.ORDER,
      sourceId: orderId,
      inventoryItemId: inventory.id,
      notes: `Deallocated due to order deletion ${orderId}`,
    });

    return {updatePromise, movement};
  }
}
