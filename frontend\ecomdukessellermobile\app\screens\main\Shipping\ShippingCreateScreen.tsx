import {SafeAreaView, StyleSheet, TouchableOpacity, View} from 'react-native';
import React from 'react';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import {colors} from '../../../theme/colors';
import styleConstants from '../../../theme/styleConstants';
import customColors from '../../../theme/customColors';
import {
  ProductShippingCharge,
  ShippingCharge,
  ShippingProfile,
  WeightBasedRule,
} from '../../../types/shipping';
import {useTypedSelector} from '../../../redux/appstore';
import {FieldArray, useFormik} from 'formik';
import Toast from 'react-native-toast-message';
import {
  useCreateShippingProfileMutation,
  useGetShippingMethodsQuery,
  useUpdateShippingProfileMutation,
} from '../../../redux/shipping/shippingApiSlice';
import {shippingProfileValidation} from '../../../validations/shipping';
import {Icon, MD3Colors, Switch, Text} from 'react-native-paper';
import CustomTextInput from '../../../components/InputFields/Textinput';
import CustomDropDown from '../../../components/InputFields/Dropdown';
import CustomButton from '../../../components/CustomButton/ContainedButton';

type Props = {};

const emptyShippingCharge: ShippingCharge = {
  countryCode: 'IN',
  stateCode: '',
  baseCharge: 0,
  estimatedDaysMin: 1,
  estimatedDaysMax: 3,
};

const emptyWeightRule: WeightBasedRule = {
  minWeight: 0,
  maxWeight: 1,
  charge: 0,
};

const emptyProductShippingCharge: ProductShippingCharge = {
  productId: '',
  baseCharge: 0,
};
const ShippingCreateScreen = ({route, navigation}: Props) => {
  const {userDetails} = useTypedSelector(state => state.auth);
  const isEditing = false;
  const selectedProfile = route?.params?.editData;
  const initialValues: ShippingProfile = {
    name: '',
    description: '',
    isDefault: false,
    isActive: true,
    sellerId: userDetails?.profileId ?? '',
    shippingMethodId: '',
    shippingCharges: [],
    weightBasedRules: [],
    productShippingCharges: [],
  };

  const [createShippingProfile, {isLoading: isCreating}] =
    useCreateShippingProfileMutation();
  const [updateShippingProfile, {isLoading: isUpdating}] =
    useUpdateShippingProfileMutation();
  const {data: shippingMethods, isLoading: isMethodsLoading} =
    useGetShippingMethodsQuery();

  const handleSave = async (values: ShippingProfile) => {
    if (isEditing) {
      delete values.shippingMethod;
      delete values.id;
      await updateShippingProfile({
        id: selectedProfile?.id,
        body: values,
      }).unwrap();
      Toast.show({
        type: 'success',
        text1: 'Shipping profile updated successfully',
      });
    } else {
      await createShippingProfile(values).unwrap();
      Toast.show({
        type: 'success',
        text1: 'Shipping profile created successfully',
      });
    }
    resetForm();
  };
  const formik = useFormik<ShippingProfile>({
    initialValues,
    validationSchema: shippingProfileValidation,
    onSubmit: async values => {
      handleSave(values);
    },
  });
  const resetForm = () => {
    formik.resetForm();
  };
  const _renderSeperator = () => {
    return (
      <View
        style={{
          borderBottomWidth: 1,
          borderBottomColor: customColors.borderGrey,
          marginVertical: styleConstants.spacing.s10,
        }}
      />
    );
  };
  return (
    <SafeAreaView style={styles.mainContainer}>
      <KeyboardAwareScrollView
        nestedScrollEnabled
        style={styles.scrolllViewStyle}
        contentContainerStyle={styles.scrollviewContainer}>
        <Text variant="titleMedium">{`Profile Information`}</Text>
        {_renderSeperator()}
        <CustomTextInput
          title="Profile Name*"
          value={formik.values.name}
          onChangeText={formik.handleChange('name')}
          onBlur={formik.handleBlur('name')}
          touched={formik.touched.name}
          errors={formik.errors.name}
        />
        <CustomDropDown
          title="Shipping Method*"
          value={formik.values.shippingMethod}
          touched={formik.touched.shippingMethod}
          errors={formik.errors.shippingMethod}
          placeholder="Select Shipping Method"
          data={Array.isArray(shippingMethods) ? shippingMethods : []}
          labelField="name"
          valueField="type"
          onChange={(item: any) =>
            formik.setFieldValue('shippingMethod', item.value)
          }
          onDropdownFocus={() => formik.setFieldTouched('shippingMethod', true)}
          onDropdownBlur={() => formik.handleBlur('shippingMethod')}
          onRemove={() => {
            formik.setFieldValue('shippingMethod', '');
          }}
        />
        <CustomTextInput
          title="Description"
          value={formik.values.description}
          onChangeText={formik.handleChange('description')}
          onBlur={formik.handleBlur('description')}
          touched={formik.touched.description}
          errors={formik.errors.description}
          multiline={true}
          textInputStyle={{
            height: 150,
          }}
        />
        <View style={styles.SwitchButton}>
          <Text variant="bodyMedium">Active</Text>
          <Switch
            trackColor={{false: '#767577', true: colors.primary}}
            thumbColor={
              formik.values.isActive ? customColors.appBlue : '#f4f3f4'
            }
            ios_backgroundColor="#3e3e3e"
            onValueChange={() => {
              formik.setFieldValue('isActive', !formik.values.isActive);
            }}
            value={formik.values.isActive}
          />
        </View>
        <View style={styles.SwitchButton}>
          <Text variant="bodyMedium">Set as Default</Text>
          <Switch
            trackColor={{false: '#767577', true: colors.primary}}
            thumbColor={
              formik.values.isDefault ? customColors.appBlue : '#f4f3f4'
            }
            ios_backgroundColor="#3e3e3e"
            onValueChange={() => {
              formik.setFieldValue('isDefault', !formik.values.isDefault);
            }}
            value={formik.values.isDefault}
          />
        </View>
        <View
          style={{
            marginTop: 20,
          }}>
          <Text variant="titleMedium">{`Shipping Charges by Location`}</Text>
          <Text variant="bodyMedium">{`Set shipping prices for different countries`}</Text>

          {_renderSeperator()}

          <View>
            {formik.values.shippingCharges?.map((charge, index) => {
              return (
                <View>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginVertical: 10,
                    }}>
                    <Text variant="bodyMedium">{` Shipping Charge #${index + 1}`}</Text>
                    <TouchableOpacity
                      onPress={() => {
                        formik.setFieldValue(
                          'shippingCharges',
                          formik.values.shippingCharges.filter(
                            (_, i) => i !== index,
                          ),
                        );
                        Toast.show({
                          type: 'info',
                          text1: 'Shipping charge removed',
                        });
                      }}>
                      <Icon
                        source={'delete'}
                        color={MD3Colors.error50}
                        size={25}
                      />
                    </TouchableOpacity>
                  </View>
                  <CustomTextInput
                    title="Shipping Price (₹)*"
                    value={charge.baseCharge.toString()}
                    onChangeText={text => {
                      formik.setFieldValue(
                        `shippingCharges.${index}.baseCharge`,
                        Number(text),
                      );
                    }}
                    onBlur={() =>
                      formik.setFieldTouched(
                        `shippingCharges.${index}.baseCharge`,
                      )
                    }
                    touched={
                      formik.touched.shippingCharges &&
                      Array.isArray(formik.touched.shippingCharges) &&
                      formik.touched.shippingCharges[index]?.baseCharge
                    }
                    errors={formik.errors.shippingCharges?.[index]?.baseCharge}
                  />
                  <CustomTextInput
                    title="Min Delivery Days*"
                    value={charge.estimatedDaysMin.toString()}
                    onChangeText={text => {
                      formik.setFieldValue(
                        `shippingCharges.${index}.estimatedDaysMin`,
                        Number(text),
                      );
                    }}
                    onBlur={() =>
                      formik.setFieldTouched(
                        `shippingCharges.${index}.estimatedDaysMin`,
                      )
                    }
                    touched={
                      formik.touched.shippingCharges &&
                      Array.isArray(formik.touched.shippingCharges) &&
                      formik.touched.shippingCharges[index]?.estimatedDaysMin
                    }
                    errors={
                      formik.errors.shippingCharges?.[index]?.estimatedDaysMin
                    }
                  />
                  <CustomTextInput
                    title="Max Delivery Days*"
                    value={charge.estimatedDaysMax.toString()}
                    onChangeText={text => {
                      formik.setFieldValue(
                        `shippingCharges.${index}.estimatedDaysMax`,
                        Number(text),
                      );
                    }}
                    onBlur={() =>
                      formik.setFieldTouched(
                        `shippingCharges.${index}.estimatedDaysMax`,
                      )
                    }
                    touched={
                      formik.touched.shippingCharges &&
                      Array.isArray(formik.touched.shippingCharges) &&
                      formik.touched.shippingCharges[index]?.estimatedDaysMax
                    }
                    errors={
                      formik.errors.shippingCharges?.[index]?.estimatedDaysMax
                    }
                  />
                  {formik?.values?.shippingCharges?.length !== index + 1 &&
                    _renderSeperator()}
                </View>
              );
            })}
            <CustomButton
              buttonStyle={{
                borderColor: colors.primary,
              }}
              mode="outlined"
              icon={'plus'}
              title={
                formik?.values?.shippingCharges?.length
                  ? 'Add Another Shipping Charge'
                  : 'Add Shipping Charge'
              }
              onPress={() => {
                formik.setFieldValue('shippingCharges', [
                  ...(formik.values.shippingCharges ?? []),
                  {...emptyShippingCharge},
                ]);
              }}
            />
          </View>
          <View
            style={{
              marginTop: 20,
            }}>
            <Text variant="titleMedium">{`Weight-Based Shipping Rules`}</Text>
            <Text variant="bodyMedium">{`Set shipping prices based on product weight`}</Text>

            {_renderSeperator()}

            {formik.values.weightBasedRules?.map((rule, index) => {
              return (
                <View>
                  <View
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginVertical: 10,
                    }}>
                    <Text variant="bodyMedium">{`Weight Rule #${index + 1}`}</Text>
                    <TouchableOpacity
                      onPress={() => {
                        formik.setFieldValue(
                          'weightBasedRules',
                          formik.values.weightBasedRules.filter(
                            (_, i) => i !== index,
                          ),
                        );
                        Toast.show({
                          type: 'info',
                          text1: 'Weight rule removed',
                        });
                      }}>
                      <Icon
                        source={'delete'}
                        color={MD3Colors.error50}
                        size={25}
                      />
                    </TouchableOpacity>
                  </View>
                  <CustomTextInput
                    title="Min Weight (kg)*"
                    value={rule.minWeight?.toString()}
                    onChangeText={text => {
                      formik.setFieldValue(
                        `weightBasedRules.${index}.minWeight`,
                        Number(text),
                      );
                    }}
                    onBlur={() =>
                      formik.setFieldTouched(
                        `weightBasedRules.${index}.minWeight`,
                      )
                    }
                    touched={
                      formik.touched.weightBasedRules &&
                      Array.isArray(formik.touched.weightBasedRules) &&
                      formik.touched.weightBasedRules[index]?.minWeight
                    }
                    errors={formik.errors.weightBasedRules?.[index]?.minWeight}
                  />
                  <CustomTextInput
                    title="Max Weight (kg)*"
                    value={rule?.maxWeight?.toString()}
                    onChangeText={text => {
                      formik.setFieldValue(
                        `weightBasedRules.${index}.maxWeight`,
                        Number(text),
                      );
                    }}
                    onBlur={() =>
                      formik.setFieldTouched(
                        `weightBasedRules.${index}.maxWeight`,
                      )
                    }
                    touched={
                      formik.touched.weightBasedRules &&
                      Array.isArray(formik.touched.weightBasedRules) &&
                      formik.touched.weightBasedRules[index]?.maxWeight
                    }
                    errors={formik.errors.weightBasedRules?.[index]?.maxWeight}
                  />
                  <CustomTextInput
                    title="Shipping Price*"
                    value={rule.charge?.toString()}
                    onChangeText={text => {
                      formik.setFieldValue(
                        `weightBasedRules.${index}.charge`,
                        Number(text),
                      );
                    }}
                    onBlur={() =>
                      formik.setFieldTouched(`weightBasedRules.${index}.charge`)
                    }
                    touched={
                      formik.touched.weightBasedRules &&
                      Array.isArray(formik.touched.weightBasedRules) &&
                      formik.touched.weightBasedRules[index]?.charge
                    }
                    errors={formik.errors.weightBasedRules?.[index]?.charge}
                  />
                  {formik?.values?.weightBasedRules?.length !== index + 1 &&
                    _renderSeperator()}
                </View>
              );
            })}
            <CustomButton
              buttonStyle={{
                borderColor: colors.primary,
              }}
              mode="outlined"
              icon={'plus'}
              title={
                formik.values.weightBasedRules?.length
                  ? 'Add Another Weight Rule'
                  : 'Add Weight Rule'
              }
              onPress={() => {
                formik.setFieldValue('weightBasedRules', [
                  ...(formik.values.weightBasedRules ?? []),
                  {...emptyWeightRule},
                ]);
              }}
            />
          </View>
          <CustomButton
            title={'Save Shipping Profile'}
            onPress={() => {
              formik.handleSubmit();
            }}
          />
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default ShippingCreateScreen;

const styles = StyleSheet.create({
  mainContainer: {
    backgroundColor: colors.surface,
    flex: 1,
    justifyContent: 'center',
  },
  scrolllViewStyle: {
    margin: styleConstants.spacing.x20,
    borderRadius: styleConstants.borderRadii.b10,
  },
  scrollviewContainer: {
    backgroundColor: customColors.white,
    borderRadius: styleConstants.borderRadii.b10,
    padding: styleConstants.spacing.x20,
    flexGrow: 1,
  },
  SwitchButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 10,
  },
});
