import {Getter, inject} from '@loopback/core';
import {Groups, GroupsRelations} from '../models';
import {SequelizeUserModifyCrudRepository} from '@sourceloop/core/sequelize';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {NotifDbSourceName} from '@sourceloop/notification-service';
import {PgDataSource} from '../datasources';

export class GroupsRepository extends SequelizeUserModifyCrudRepository<
  Groups,
  typeof Groups.prototype.id,
  GroupsRelations
> {
  constructor(
    @inject(`datasources.${NotifDbSourceName}`) dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {
    super(Groups, dataSource, getCurrentUser);
  }
}
