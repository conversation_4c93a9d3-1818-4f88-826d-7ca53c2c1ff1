import {ApiSliceIdentifier} from '../constants/enums';
import {UploadResponse} from '../types/file';
import {apiSlice} from './apiSlice';

export const fileApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    uploadFile: builder.mutation<UploadResponse, FormData>({
      query: formData => ({
        url: '/files/upload',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: formData,
        formData: true,
      }),
    }),
  }),
});

export const {useUploadFileMutation} = fileApiSlice;
