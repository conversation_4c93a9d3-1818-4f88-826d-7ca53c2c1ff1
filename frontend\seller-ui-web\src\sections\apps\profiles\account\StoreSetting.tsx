import { ChangeEvent, useEffect, useMemo, useState } from 'react';

// material-ui
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import InputLabel from '@mui/material/InputLabel';
import Typography from '@mui/material/Typography';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
// project-imports
import MainCard from 'components/MainCard';
import { useFormik } from 'formik';

import { Autocomplete, Avatar, Box, Button, Checkbox, FormControlLabel, FormLabel, OutlinedInput, useTheme } from '@mui/material';
import { storesettingValidation } from 'validation/store-setting.validation';
import { useGetUserQuery } from '../../../../redux/auth/authApiSlice';
import { ALLOWED_STORE_FILE_EXTENTIONS, ThemeMode } from 'config';
import { Camera } from 'iconsax-react';
import Loading from 'app/loading';
import { SellerStoreAction } from './SellerStoreAction';
import { ISellerStore, ISellerStorePayload } from 'types/auth';
import { Country, State } from 'country-state-city';
import { useApiErrorHandler } from 'hooks/useApiErrorHandler';
import { useRouter } from 'next/navigation';
import { useGetSellerStoreBySellerIdQuery, useUpdateSellerStoreByIdMutation } from 'redux/auth/sellerApiSlice';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';

// ==============================|| ACCOUNT PROFILE - MY ACCOUNT ||============================== //

interface FileKeyType {
  logo: File | null;
  banner: File | null;
  // dp: File | null;
  signature: File | null;
}

export default function StoreSetting() {
  const theme = useTheme();
  const countries = Country.getAllCountries();
  const router = useRouter();

  const [fileKeys, setFileKeys] = useState<FileKeyType>({
    logo: null,
    banner: null,
    // dp: null,
    signature: null
  });
  const { data: user } = useGetUserQuery();

  const [updateSellerStore, { isLoading: isUpdating, error: updateError, reset: updateReset }] = useUpdateSellerStoreByIdMutation();
  const handleError = useApiErrorHandler();

  const {
    data: sellerStore,
    refetch: refetchSellerStore,
    isLoading: storeLoading,
    isUninitialized: storeUninitialized
  } = useGetSellerStoreBySellerIdQuery(user?.profileId ?? '', {
    skip: !user?.profileId
  });

  const handleFileChange = async (event: ChangeEvent<HTMLInputElement>, fieldName: keyof typeof formik.values) => {
    if (!event.target.files) return;
    const file = event.target.files[0] || null;
    setFileKeys((prevKeys) => ({
      ...prevKeys,
      [fieldName]: file ?? null
    }));

    formik.setFieldValue(fieldName, file?.name || '');
  };

  const handleStartDateChange = (newValue: Date | null) => {
    formik.setFieldValue('unavailabilityStartDate', newValue);
  };

  const handleEndDateChange = (newValue: Date | null) => {
    formik.setFieldValue('unavailabilityEndDate', newValue);
  };

  const formik = useFormik<ISellerStore>({
    initialValues: {
      description: sellerStore?.description ?? '',
      logo: sellerStore?.logo ?? '',
      banner: sellerStore?.banner ?? '',
      signature: sellerStore?.signature ?? '',
      storeName: sellerStore?.storeName ?? '',
      addressLine1: sellerStore?.addressLine1 ?? '',
      addressLine2: sellerStore?.addressLine2 ?? '',
      pincode: sellerStore?.pincode ?? '',
      city: sellerStore?.city ?? '',
      state: sellerStore?.state ?? '',
      country: sellerStore?.country ?? '',
      legalName: sellerStore?.legalName ?? '',
      workingHours: sellerStore?.workingHours ?? 0,
      hideWorkingHours: sellerStore?.hideWorkingHours ?? false,
      allowBulkOrder: sellerStore?.allowBulkOrder ?? false,
      allowCategorisation: sellerStore?.allowCategorisation ?? false,
      unavailabilityStartDate: sellerStore?.unavailabilityStartDate ? dayjs(sellerStore.unavailabilityStartDate) : null,
      unavailabilityEndDate: sellerStore?.unavailabilityEndDate ? dayjs(sellerStore.unavailabilityEndDate) : null
    },
    validationSchema: storesettingValidation,
    enableReinitialize: true,
    onSubmit: async (values) => {
      handleSubmit(values);
    }
  });

  useEffect(() => {
    if (sellerStore) {
      formik.resetForm({
        values: {
          description: sellerStore?.description ?? '',
          logo: sellerStore?.logo ?? '',
          banner: sellerStore?.banner ?? '',
          signature: sellerStore?.signature ?? '',
          storeName: sellerStore?.storeName ?? '',
          addressLine1: sellerStore?.addressLine1 ?? '',
          addressLine2: sellerStore?.addressLine2 ?? '',
          pincode: sellerStore?.pincode ?? '',
          city: sellerStore?.city ?? '',
          state: sellerStore?.state ?? '',
          country: sellerStore?.country ?? '',
          legalName: sellerStore?.legalName ?? '',
          workingHours: sellerStore?.workingHours ?? 0,
          hideWorkingHours: sellerStore?.hideWorkingHours ?? false,
          allowBulkOrder: sellerStore?.allowBulkOrder ?? false,
          allowCategorisation: sellerStore?.allowCategorisation ?? false,
          unavailabilityStartDate: sellerStore?.unavailabilityStartDate ? dayjs(sellerStore.unavailabilityStartDate) : null,
          unavailabilityEndDate: sellerStore?.unavailabilityEndDate ? dayjs(sellerStore.unavailabilityEndDate) : null
        }
      });
    }
  }, [sellerStore]);

  const handleSubmit = async (values: ISellerStore) => {
    const changedFiles: Record<string, File> = {};
    Object.keys(fileKeys).forEach((key) => {
      if (fileKeys[key as keyof FileKeyType]) {
        changedFiles[key] = fileKeys[key as keyof FileKeyType] as File;
      }
    });

    const body: Partial<ISellerStorePayload> = {
      description: values.description,
      storeName: values.storeName,
      addressLine1: values.addressLine1,
      addressLine2: values.addressLine2 ?? '',
      pincode: values.pincode,
      country: values.country ?? values.country,
      state: values.state ?? values.state,
      city: values.city ?? values.city,
      legalName: values?.legalName,
      workingHours: values?.workingHours,
      hideWorkingHours: values?.hideWorkingHours,
      allowBulkOrder: values?.allowBulkOrder,
      allowCategorisation: values?.allowCategorisation,
      unavailabilityStartDate: values?.unavailabilityStartDate ? values?.unavailabilityStartDate.toISOString() : null,
      unavailabilityEndDate: values?.unavailabilityEndDate ? values?.unavailabilityEndDate.toISOString() : null
    };

    if (changedFiles && Object.keys(changedFiles).length) {
      Object.keys(changedFiles).forEach((file) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        body[file as keyof ISellerStore] = changedFiles[file] as any;
      });
    }

    await updateSellerStore({
      sellerStoreId: sellerStore?.id ?? '',
      body
    }).unwrap();

    await refetchSellerStore();

    openSnackbar({
      open: true,
      message: 'Store updated successfully',
      variant: 'alert',
      alert: { color: 'success' }
    } as SnackbarProps);
    router.push('/account/profile');
  };

  useEffect(() => {
    if (updateError) {
      handleError(updateError);
      updateReset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [handleError, updateError]);

  const states = useMemo(() => {
    if (!formik.values.country) return [];
    const country = countries.find((c) => c.name === formik.values.country);
    return State.getStatesOfCountry(country?.isoCode);
  }, [countries, formik.values.country]);

  const countryValue = useMemo(() => {
    return countries.find((item) => item.name === formik.values.country);
  }, [countries, formik.values.country]);

  if (storeLoading && storeUninitialized) return <Loading />;
  if (!sellerStore) return <SellerStoreAction />;

  return (
    <form onSubmit={formik.handleSubmit}>
      <Grid container spacing={1}>
        <Grid item xs={12} sm={12} md={6} lg={4}>
          <MainCard title="Brand">
            <Grid container spacing={3}>
              {['logo', 'banner', 'signature'].map((item) => (
                <Grid item xs={6} key={item}>
                  <Stack spacing={2.5} alignItems="center" sx={{ m: { xs: 1, sm: 2 } }}>
                    <FormLabel
                      htmlFor={item}
                      sx={{
                        position: 'relative',
                        borderRadius: '50%',
                        overflow: 'hidden',
                        '&:hover .MuiBox-root': { opacity: 1 },
                        cursor: 'pointer'
                      }}
                    >
                      <Avatar
                        alt={item}
                        src={
                          fileKeys[item as keyof FileKeyType]
                            ? URL.createObjectURL(fileKeys[item as keyof FileKeyType] as File)
                            : (sellerStore?.[item as keyof ISellerStore] as string)
                        }
                        sx={{ width: { xs: 60, sm: 76 }, height: { xs: 60, sm: 76 } }}
                      />
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          backgroundColor: theme.palette.mode === ThemeMode.DARK ? 'rgba(255, 255, 255, .75)' : 'rgba(0,0,0,.65)',
                          width: '100%',
                          height: '100%',
                          opacity: 0,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <Stack spacing={0.5} alignItems="center">
                          <Camera
                            style={{
                              color: theme.palette.secondary.lighter,
                              fontSize: '1.5rem'
                            }}
                          />
                          <Typography
                            sx={{
                              color: 'secondary.lighter',
                              width: '100%',
                              textAlign: 'center',
                              display: 'flex',
                              justifyContent: 'center'
                            }}
                            variant="caption"
                          >
                            {`Upload ${item}`}
                          </Typography>
                        </Stack>
                      </Box>
                    </FormLabel>
                    <input
                      type="file"
                      id={item}
                      placeholder="Outlined"
                      style={{ display: 'none' }}
                      onChange={(e: ChangeEvent<HTMLInputElement>) => handleFileChange(e, item as keyof ISellerStore)}
                      accept={ALLOWED_STORE_FILE_EXTENTIONS.join(', ')}
                    />
                    <Stack spacing={0.5} alignItems="center">
                      <Typography variant="h5" sx={{ textTransform: 'capitalize' }}>
                        {item}
                      </Typography>
                    </Stack>
                  </Stack>
                </Grid>
              ))}
            </Grid>
          </MainCard>
        </Grid>
        <Grid item xs={12} sm={12} md={6} lg={8}>
          <MainCard>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <InputLabel htmlFor="company">Company Legal Name</InputLabel>
                <OutlinedInput
                  id="legalName"
                  name="legalName"
                  fullWidth
                  value={formik.values.legalName}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.legalName && Boolean(formik.errors.legalName)}
                />
                {formik.touched.legalName && formik.errors.legalName && (
                  <Typography variant="caption" color="error">
                    {formik.errors.legalName}
                  </Typography>
                )}
              </Grid>
              <Grid item xs={12} sm={6}>
                <InputLabel htmlFor="company">Store Name</InputLabel>
                <OutlinedInput
                  id="storeName"
                  name="storeName"
                  fullWidth
                  value={formik.values.storeName}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.storeName && Boolean(formik.errors.storeName)}
                />
                {formik.touched.storeName && formik.errors.storeName && (
                  <Typography variant="caption" color="error">
                    {formik.errors.storeName}
                  </Typography>
                )}
              </Grid>
              <Grid item xs={12}>
                <InputLabel htmlFor="description">Description</InputLabel>
                <TextField
                  placeholder="Type Store Description"
                  id="description"
                  name="description"
                  multiline
                  rows={3}
                  fullWidth
                  value={formik.values.description}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.description && Boolean(formik.errors.description)}
                  helperText={formik.touched.description && formik.errors.description}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <InputLabel htmlFor="addressLine1">Address Line 1</InputLabel>
                <TextField
                  placeholder="Address Line 1"
                  id="addressLine1"
                  name="addressLine1"
                  multiline
                  rows={3}
                  fullWidth
                  value={formik.values.addressLine1}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.addressLine1 && Boolean(formik.errors.addressLine1)}
                  helperText={formik.touched.addressLine1 && formik.errors.addressLine1}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <InputLabel htmlFor="addressLine2">Address Line 2</InputLabel>
                <TextField
                  placeholder="Address Line 2"
                  id="addressLine2"
                  name="addressLine2"
                  multiline
                  rows={3}
                  fullWidth
                  value={formik.values.addressLine2}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.addressLine2 && Boolean(formik.errors.addressLine2)}
                  helperText={formik.touched.addressLine2 && formik.errors.addressLine2}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                {' '}
                <InputLabel htmlFor="product-image" sx={{ paddingLeft: '18px', fontSize: '14px' }}>
                  Country
                </InputLabel>
                <Autocomplete
                  id="country"
                  options={countries}
                  autoHighlight
                  fullWidth
                  value={countryValue}
                  onBlur={() => {
                    formik.setFieldTouched('country', true);
                  }}
                  getOptionLabel={(option) => option.name}
                  onChange={(e, newValue) => {
                    formik.setFieldValue('country', newValue?.name);
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Select a country"
                      name="country"
                      error={formik.touched.country && Boolean(formik.errors.country)}
                      helperText={formik.touched.country && formik.errors.country}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                {' '}
                <InputLabel htmlFor="product-image" sx={{ paddingLeft: '18px', fontSize: '14px' }}>
                  State
                </InputLabel>
                <Autocomplete
                  id="state"
                  options={states}
                  autoHighlight
                  fullWidth
                  value={states.find((item) => item.name === formik.values.state)}
                  onBlur={() => {
                    formik.setFieldTouched('country', true);
                  }}
                  getOptionLabel={(option) => option.name}
                  onChange={(e, newValue) => {
                    formik.setFieldValue('state', newValue?.name);
                  }}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Select a state"
                      name="state"
                      error={formik.touched.state && Boolean(formik.errors.state)}
                      helperText={formik.touched.state && formik.errors.state}
                    />
                  )}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                {' '}
                <InputLabel htmlFor="product-image" sx={{ paddingLeft: '18px', fontSize: '14px' }}>
                  City
                </InputLabel>
                <OutlinedInput
                  placeholder="City"
                  id="city"
                  name="city"
                  fullWidth
                  value={formik.values.city}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.city && Boolean(formik.errors.city)}
                />
                {formik.touched.city && formik.errors.city && (
                  <Typography variant="caption" color="error">
                    {formik.errors.city}
                  </Typography>
                )}
              </Grid>
              <Grid item xs={12} sm={6}>
                <InputLabel htmlFor="pincode">PinCode</InputLabel>
                <OutlinedInput
                  id="pincode"
                  name="pincode"
                  fullWidth
                  value={formik.values.pincode}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.pincode && Boolean(formik.errors.pincode)}
                />
                {formik.touched.pincode && formik.errors.pincode && (
                  <Typography variant="caption" color="error">
                    {formik.errors.pincode}
                  </Typography>
                )}
              </Grid>

              <Grid item xs={12} sm={6}>
                <InputLabel htmlFor="pincode">Working Hours</InputLabel>
                <OutlinedInput
                  id="workingHours"
                  name="workingHours"
                  fullWidth
                  value={formik.values.workingHours}
                  onChange={(e) => {
                    const onlyNums = e.target.value.replace(/[^0-9]/g, '');
                    formik.setFieldValue('workingHours', onlyNums);
                  }}
                  onBlur={formik.handleBlur}
                  error={formik.touched.workingHours && Boolean(formik.errors.workingHours)}
                />
                {formik.touched.workingHours && formik.errors.workingHours && (
                  <Typography variant="caption" color="error">
                    {formik.errors.workingHours}
                  </Typography>
                )}
              </Grid>
              <Grid item xs={12} sm={6}>
                <Stack direction="row" spacing={2} marginTop={2}>
                  <LocalizationProvider dateAdapter={AdapterDayjs}>
                    <DatePicker
                      label={`Unavailable From`}
                      value={formik.values.unavailabilityStartDate as any}
                      onChange={handleStartDateChange}
                    />
                    <DatePicker
                      label={`Unavailable To`}
                      value={formik.values.unavailabilityEndDate as any}
                      onChange={handleEndDateChange}
                    />
                  </LocalizationProvider>
                </Stack>
              </Grid>
              <Grid item xs={12} sm={12} sx={{ display: 'flex', justifyContent: 'space-around' }}>
                <FormControlLabel
                  control={<Checkbox name="hideWorkingHours" checked={formik.values.hideWorkingHours} onChange={formik.handleChange} />}
                  label="Show/Hide Working hours"
                />
                <FormControlLabel
                  control={<Checkbox name="allowBulkOrder" checked={formik.values.allowBulkOrder} onChange={formik.handleChange} />}
                  label="Allow Bulk Orders"
                />
                <FormControlLabel
                  control={
                    <Checkbox name="allowCategorisation" checked={formik.values.allowCategorisation} onChange={formik.handleChange} />
                  }
                  label="Allow Categorisation"
                />
              </Grid>
            </Grid>
          </MainCard>
        </Grid>
        <Grid item xs={12}>
          <Stack direction="row" justifyContent="flex-end" alignItems="center" spacing={2}>
            <Button type="submit" variant="contained" disabled={isUpdating}>
              Update Profile
            </Button>
          </Stack>
        </Grid>
      </Grid>
    </form>
  );
}
