/* Replace with your SQL commands */
-- Drop triggers
DROP TRIGGER IF EXISTS mdt_weight_based_shipping_rules ON main.weight_based_shipping_rules;
DROP TRIGGER IF EXISTS mdt_product_shipping_charges ON main.product_shipping_charges;
DROP TRIGGER IF EXISTS mdt_seller_shipping_charges ON main.seller_shipping_charges;
DROP TRIGGER IF EXISTS mdt_seller_shipping_profiles ON main.seller_shipping_profiles;
DROP TRIGGER IF EXISTS mdt_shipping_methods ON main.shipping_methods;

-- Drop indexes
DROP INDEX IF EXISTS idx_weight_based_shipping_rules_profile;
DROP INDEX IF EXISTS idx_product_shipping_charges_profile;
DROP INDEX IF EXISTS idx_product_shipping_charges_variant;
DROP INDEX IF EXISTS idx_product_shipping_charges_product;
DROP INDEX IF EXISTS idx_seller_shipping_charges_location;
DROP INDEX IF EXISTS idx_seller_shipping_profiles_method_id;
DROP INDEX IF EXISTS idx_seller_shipping_profiles_seller_id;
DROP INDEX IF EXISTS idx_shipping_methods_type;

-- Drop tables in reverse order of creation
DROP TABLE IF EXISTS main.weight_based_shipping_rules;
DROP TABLE IF EXISTS main.product_shipping_charges;
DROP TABLE IF EXISTS main.seller_shipping_charges;
DROP TABLE IF EXISTS main.seller_shipping_profiles;
DROP TABLE IF EXISTS main.shipping_methods;
