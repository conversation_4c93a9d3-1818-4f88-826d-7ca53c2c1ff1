import React, {useEffect, useState} from 'react';
import {View, Text, ScrollView, StyleSheet} from 'react-native';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import CustomButton from '../../../../components/CustomButton/CustomButton';
import CustomTextInput from '../../../../components/InputFields/CustomTextInput';
import {Menu, RadioButton, TextInput} from 'react-native-paper';
import customColors from '../../../../theme/customColors';

export interface CustomizationOption {
  id: string;
  label: string;
  value: string;
}

export interface CustomizationField {
  id: string;
  name: string;
  label: string;
  placeholder: string;
  fieldType: 'text' | 'number' | 'textarea' | 'dropdown' | 'checkbox' | 'radio';
  isRequired: boolean;
  productCustomizationOptions?: CustomizationOption[];
}

interface Props {
  fields: CustomizationField[];
  onSubmit: (values: any) => void;
  onChange?: (values: any) => void;
  prefillValues?: {customizationFieldId: string; value: string}[];
  submitButtonTitle?: string;
  submitButtonMode?: 'default' | 'step'; // optional if you want dynamic behavior inside
  onSecondSubmit?: () => void; // only for 'step' mode
}

const ProductCustomizationForm: React.FC<Props> = ({
  fields,
  submitButtonMode,
  submitButtonTitle,
  onSecondSubmit,
  onSubmit,
  onChange,
  prefillValues = [],
}) => {
  const [menuVisibleMap, setMenuVisibleMap] = useState<Record<string, boolean>>(
    {},
  );

  const openMenu = (fieldId: string) =>
    setMenuVisibleMap(prev => ({...prev, [fieldId]: true}));

  const closeMenu = (fieldId: string) =>
    setMenuVisibleMap(prev => ({...prev, [fieldId]: false}));

  const getInitialValues = (
    // eslint-disable-next-line @typescript-eslint/no-shadow
    fields: CustomizationField[],
    // eslint-disable-next-line @typescript-eslint/no-shadow
    prefillValues: {customizationFieldId: string; value: string}[],
  ) => {
    const prefillMap = prefillValues.reduce((acc, item) => {
      acc[item.customizationFieldId] = item.value;
      return acc;
    }, {} as Record<string, string>);

    return fields.reduce((acc, field) => {
      acc[field.name] = prefillMap[field.id] || '';
      return acc;
    }, {} as Record<string, any>);
  };

  const initialValues = getInitialValues(fields, prefillValues);
  const validationSchema = Yup.object(
    fields.reduce((acc, field) => {
      if (field.isRequired) {
        acc[field.name] = Yup.string().required(`${field.label} is required`);
      }
      return acc;
    }, {} as Record<string, Yup.StringSchema>),
  );

  const renderField = (
    field: CustomizationField,
    handleChange: any,
    values: any,
    errors: any,
    touched: any,
    setFieldTouched: any,
  ) => {
    const error = touched[field.name] && errors[field.name];
    switch (field.fieldType) {
      case 'text':
      case 'number':
        return (
          <CustomTextInput
            key={field.id}
            placeholder={field.placeholder}
            keyboardType={field.fieldType === 'number' ? 'numeric' : 'default'}
            value={values[field.name]}
            onChangeText={handleChange(field.name)}
            onBlur={() => setFieldTouched(field.name)}
            title={''}
            style={styles.numberInput}
            error={!!error}
          />
        );
      case 'textarea':
        return (
          <CustomTextInput
            key={field.id}
            placeholder={field.placeholder}
            multiline
            numberOfLines={4}
            value={values[field.name]}
            onChangeText={handleChange(field.name)}
            onBlur={() => setFieldTouched(field.name)}
            title={''}
            error={!!error}
          />
        );
      case 'dropdown': {
        const isVisible = !!menuVisibleMap[field.id];
        return (
          <View key={field.id} style={styles.input}>
            <Menu
              visible={isVisible}
              onDismiss={() => closeMenu(field.id)}
              contentStyle={styles.menuContent}
              anchor={
                <CustomTextInput
                  onPress={() => openMenu(field.id)}
                  mode="outlined"
                  placeholder={field.placeholder || 'Choose an option'}
                  value={values[field.name]}
                  editable={false}
                  right={
                    <TextInput.Icon
                      icon="chevron-down"
                      forceTextInputFocus={false}
                      onPress={() => openMenu(field.id)}
                    />
                  }
                  error={!!error}
                  title={''}
                />
              }>
              {field.productCustomizationOptions?.map(option => (
                <Menu.Item
                  key={option.id}
                  onPress={() => {
                    handleChange(field.name)(option.value);
                    closeMenu(field.id);
                  }}
                  title={option.label}
                />
              ))}
            </Menu>
          </View>
        );
      }
      case 'radio':
        return (
          <RadioButton.Group
            key={field.id}
            onValueChange={handleChange(field.name)}
            value={values[field.name]}>
            {(field.productCustomizationOptions || []).map(option => (
              <RadioButton.Item
                key={option.id}
                label={option.label}
                value={option.value}
                mode="android"
                position="trailing"
              />
            ))}
          </RadioButton.Group>
        );
      default:
        return null;
    }
  };

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit,
  });
  useEffect(() => {
    if (onChange) {
      onChange(formik.values);
    }
  }, [formik.values, onChange]);
  return (
    <ScrollView contentContainerStyle={styles.container}>
      {fields.map(field => (
        <View key={field.id} style={styles.field}>
          <Text style={styles.label}>{field.label}</Text>
          {renderField(
            field,
            formik.handleChange,
            formik.values,
            formik.errors,
            formik.touched,
            formik.setFieldTouched,
          )}
        </View>
      ))}
      <CustomButton
        title={submitButtonTitle || 'SAVE'}
        onPress={() => {
          if (submitButtonMode === 'step') {
            if (submitButtonTitle === 'GO TO CART') {
              onSecondSubmit?.();
            } else {
              formik.handleSubmit();
            }
          } else {
            formik.handleSubmit();
          }
        }}
      />{' '}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  field: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 4,
    fontWeight: 'bold',
    color: customColors.textBlack,
  },
  input: {
    borderRadius: 8,
    padding: 0,
    textAlignVertical: 'top',
    height: 4 * 24,
  },
  numberInput: {
    borderRadius: 8,
    padding: 0,
    textAlignVertical: 'top',
    height: 3 * 24,
  },
  menuContent: {
    backgroundColor: customColors.white,
    borderRadius: 0,
  },
});

export default ProductCustomizationForm;
