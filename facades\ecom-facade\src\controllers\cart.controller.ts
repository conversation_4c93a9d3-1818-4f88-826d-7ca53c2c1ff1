import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
} from '@loopback/rest';
import {
  Cart,
  CartItem,
  CartItemDto,
  CheckoutDto,
  CustomizationValue,
  Order,
} from '../models';
import {
  CONTENT_TYPE,
  ModifiedRestService,
  restService,
  STATUS_CODE,
} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {service} from '@loopback/core';
import {CartService} from '../services';
import {OrderEntity} from 'cashfree-pg';

const basePath = '/carts';

export class CartController {
  constructor(
    @restService(Cart)
    public cartProxy: ModifiedRestService<Cart>,
    @service(CartService)
    private readonly cartService: CartService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCart]})
  @post(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Cart model instance',
        content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Cart)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Cart, {
            title: 'NewCart',
            exclude: ['id'],
            partial: true,
          }),
        },
      },
    })
    cart: Partial<Cart>,
  ): Promise<Cart> {
    return this.cartService.createCart(cart);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCart]})
  @post(`${basePath}/items`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Cart Add Item model instance',
      },
    },
  })
  async addItemToCart(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(CartItemDto, {
            title: 'AddNewItem',
            exclude: ['id'],
            partial: true,
          }),
        },
      },
    })
    cart: Partial<Cart>,
  ): Promise<Cart> {
    return this.cartService.addItemToCart(cart);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCart]})
  @patch(`${basePath}/items`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Cart update Item model instance',
      },
    },
  })
  async updateCartItem(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(CartItem, {
            title: 'UpdateItem',
            exclude: ['id', 'cartId'],
            partial: true,
          }),
        },
      },
    })
    cartItem: Omit<CartItem, 'id' | 'cartId'>,
  ): Promise<void> {
    return this.cartService.updateCartItemQuantity(cartItem);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCart]})
  @del(`${basePath}/items/product/{productVariantId}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Cart update Item model instance',
      },
    },
  })
  async removeCartItem(
    @param.path.string('productVariantId') productVariantId: string,
  ): Promise<void> {
    return this.cartService.removeCartItem(productVariantId);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCart]})
  @get(`${basePath}/count`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Cart model count',
        content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
      },
    },
  })
  async count(@param.where(Cart) where?: Where<Cart>): Promise<Count> {
    return this.cartProxy.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCart]})
  @get(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Array of Cart model instances',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'array',
              items: getModelSchemaRef(Cart, {includeRelations: true}),
            },
          },
        },
      },
    },
  })
  async find(@param.filter(Cart) filter?: Filter<Cart>): Promise<Cart[]> {
    const carts = await this.cartProxy.find(filter);
    return this.cartService.getCartsWithPresignedUrl(carts);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCart]})
  @get(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Cart model instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(Cart, {includeRelations: true}),
          },
        },
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Cart, {exclude: 'where'}) filter?: FilterExcludingWhere<Cart>,
  ): Promise<Cart> {
    return this.cartProxy.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateCart]})
  @patch(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Cart PATCH successs',
      },
    },
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Cart, {partial: true}),
        },
      },
    })
    cart: Cart,
  ): Promise<void> {
    await this.cartProxy.updateById(id, cart);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateCart]})
  @put(`${basePath}/{id}`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Cart PUT success',
      },
    },
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() cart: Cart,
  ): Promise<void> {
    await this.cartProxy.replaceById(id, cart);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateOrder]})
  @post(`${basePath}/checkout`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Cart checkout instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(Order, {includeRelations: true}),
          },
        },
      },
    },
  })
  async checkout(
    @requestBody({
      required: true,
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(CheckoutDto, {
            title: 'Checkout',
          }),
        },
      },
    })
    checkoutDto: CheckoutDto,
  ): Promise<OrderEntity> {
    return this.cartService.checkout(
      checkoutDto.billingAddressId,
      checkoutDto.shippingAddressId,
      checkoutDto.useDukeCoins ?? false,
      checkoutDto.ecomDukeCoinsApplied ?? 0,
      checkoutDto.discountConditionId,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCart]})
  @get(`${basePath}/active`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Cart model instance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: getModelSchemaRef(Cart, {includeRelations: true}),
          },
        },
      },
    },
  })
  async getActiveCart(
    @param.filter(Cart, {exclude: 'where'}) filter?: FilterExcludingWhere<Cart>,
  ): Promise<Cart | null> {
    const actriveCart = await this.cartService.getActiveCart(filter);
    if (!actriveCart) return actriveCart;
    return this.cartService.getCartWithPresignedUrl(actriveCart);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateCart]})
  @patch(`${basePath}/{cartId}/customizations`, {
    responses: {
      [STATUS_CODE.NO_CONTENT]: {
        description: 'Cart item customization updated/added',
      },
    },
  })
  async updateCartItemCustomizations(
    @param.path.string('cartId') cartId: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: {
            type: 'array',
            items: getModelSchemaRef(CustomizationValue, {partial: true}),
          },
        },
      },
    })
    customizations: Partial<CustomizationValue>[],
  ): Promise<void> {
    await this.cartService.updateCartItemCustomizations(cartId, customizations);
  }
}
