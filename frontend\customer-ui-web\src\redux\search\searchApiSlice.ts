import {ApiSliceIdentifier} from 'enums/api.enum';
import {apiSlice} from '../../redux/apiSlice';
import {ProductVariant} from 'types/product';
import {FilterGroup, IFilterWithKeyword} from 'types/filter';

export const searchApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getSearchSuggestions: builder.query<ProductVariant[], string>({
      query: keyword => ({
        url: '/search/suggestions',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          keyword,
        },
      }),
    }),

    search: builder.query<ProductVariant[], IFilterWithKeyword>({
      query: ({keyword, facetValueIds, collectionIds, ...filter}) => ({
        url: '/search',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          keyword: keyword,
          facetValueIds,
          collectionIds,
          filter: JSON.stringify(filter),
        },
      }),
    }),
    filters: builder.query<
      FilterGroup[],
      {keyword?: string; facetValueIds?: string[]; collectionIds?: string[]}
    >({
      query: ({keyword, facetValueIds, collectionIds}) => ({
        url: '/search/filters',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          keyword,
          facetValueIds,
          collectionIds,
        },
      }),
    }),
  }),
});
export const {
  useGetSearchSuggestionsQuery,
  useLazyGetSearchSuggestionsQuery,
  useSearchQuery,
  useFiltersQuery,
  useLazySearchQuery,
} = searchApiSlice;
