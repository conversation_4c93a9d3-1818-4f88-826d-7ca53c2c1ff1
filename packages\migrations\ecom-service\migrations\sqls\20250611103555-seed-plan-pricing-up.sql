-- Insert pricing tiers for Free Plan
INSERT INTO main.plan_pricing (plan_id, min_sales_threshold, max_sales_threshold, price)
SELECT id, 0, 100, 0.00
FROM main.plans
WHERE key = 'free-plan';

-- Insert pricing tiers for Basic Plan
INSERT INTO main.plan_pricing (plan_id, min_sales_threshold, max_sales_threshold, price)
SELECT id, 0, 1000, 499.00
FROM main.plans
WHERE key = 'basic-plan';

INSERT INTO main.plan_pricing (plan_id, min_sales_threshold, max_sales_threshold, price)
SELECT id, 1001, 5000, 999.00
FROM main.plans
WHERE key = 'basic-plan';

-- Insert pricing tiers for Advanced Plan
INSERT INTO main.plan_pricing (plan_id, min_sales_threshold, max_sales_threshold, price)
SELECT id, 0, 5000, 1499.00
FROM main.plans
WHERE key = 'advanced-plan';

INSERT INTO main.plan_pricing (plan_id, min_sales_threshold, max_sales_threshold, price)
SELECT id, 5001, 10000, 1999.00
FROM main.plans
WHERE key = 'advanced-plan';