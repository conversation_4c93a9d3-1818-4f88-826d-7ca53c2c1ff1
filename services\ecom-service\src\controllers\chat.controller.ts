import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Chat} from '../models';
import {ChatRepository} from '../repositories';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';

const basePath = '/chats';

export class ChatController {
  constructor(
    @repository(ChatRepository)
    public chatRepository: ChatRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateChat]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Chat model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Chat)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Chat, {
            title: 'NewChat',
            exclude: ['id'],
          }),
        },
      },
    })
    chat: Omit<Chat, 'id'>,
  ): Promise<Chat> {
    return this.chatRepository.create(chat);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewChat]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'Chat model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(@param.where(Chat) where?: Where<Chat>): Promise<Count> {
    return this.chatRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewChat]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Chat model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Chat, {includeRelations: true}),
        },
      },
    },
  })
  async find(@param.filter(Chat) filter?: Filter<Chat>): Promise<Chat[]> {
    return this.chatRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateChat]})
  @patch(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Chat PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Chat, {partial: true}),
        },
      },
    })
    chat: Chat,
    @param.where(Chat) where?: Where<Chat>,
  ): Promise<Count> {
    return this.chatRepository.updateAll(chat, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewChat]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'Chat model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Chat, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Chat, {exclude: 'where'}) filter?: FilterExcludingWhere<Chat>,
  ): Promise<Chat> {
    return this.chatRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateChat]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Chat PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Chat, {partial: true}),
        },
      },
    })
    chat: Chat,
  ): Promise<void> {
    await this.chatRepository.updateById(id, chat);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteChat]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Chat DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.chatRepository.deleteById(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewChat]})
  @get('/chats/my')
  @response(STATUS_CODE.OK, {
    description: 'Get chats for current user',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Chat, {includeRelations: true}),
        },
      },
    },
  })
  async findMyChats(): Promise<Chat[]> {
    return this.chatRepository.find();
  }
}
