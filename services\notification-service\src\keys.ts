import {BindingKey} from '@loopback/context';
import {ZeptoMailOptions} from 'zeptomail';
import {FCMConfig, FCMNotification, Msg91Config} from './types';

export namespace ZeptoBindings {
  export const Config = BindingKey.create<ZeptoMailOptions>(
    'ec.notification.config.zepto',
  );
}

export namespace Msg91Bindings {
  export const Config = BindingKey.create<Msg91Config | null>(
    'ec.notification.config.msg91',
  );
}

export namespace FCMBindings {
  export const Notification =
    BindingKey.create<FCMNotification>('fcm.notification');

  export const Config = BindingKey.create<FCMConfig[]>('fcm.configs');
}
