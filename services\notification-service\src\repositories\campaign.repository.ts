import {Getter, inject} from '@loopback/core';
import {PgDataSource} from '../datasources';
import {Campaign, CampaignRelations} from '../models';
import {SequelizeUserModifyCrudRepository} from '@sourceloop/core/sequelize';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {NotifDbSourceName} from '@sourceloop/notification-service';

export class CampaignRepository extends SequelizeUserModifyCrudRepository<
  Campaign,
  typeof Campaign.prototype.id,
  CampaignRelations
> {
  constructor(
    @inject(`datasources.${NotifDbSourceName}`) dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {
    super(Campaign, dataSource, getCurrentUser);
  }
}
