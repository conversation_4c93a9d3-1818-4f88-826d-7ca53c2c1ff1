'use client';

import React, {useState} from 'react';
import {
  Box,
  Typography,
  Button,
  TextField,
  Card,
  CardContent,
  Divider,
  Radio,
  RadioGroup,
  FormControlLabel,
  IconButton,
  Grid,
} from '@mui/material';
import {styled} from '@mui/system';
import {Add, Forbidden, Gift} from 'iconsax-react';

const StyledContainer = styled(Box)(({theme}) => ({
  width: '100%',
  maxWidth: '80%',
  padding: 16,
  backgroundColor: '#fff',
  borderRadius: 8,
  boxShadow: '0px 2px 10px rgba(0, 0, 0, 0.1)',
  [theme.breakpoints.down('sm')]: {
    maxWidth: '95%',
    padding: 24,
  },
}));

const StyledCard = styled(Card)({
  borderRadius: '8px',
  boxShadow: 'none',
  border: '1px solid #e0e0e0',
  marginBottom: 16,
  padding: 16,
  backgroundColor: '#f5f5f5',
  marginTop: '4%',
});

const CouponContainer = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  backgroundColor: '#f5f5f5',
  padding: '10px',
  borderRadius: '30px',
  marginTop: '16px',
  justifyContent: 'space-between',
});

const CheckoutButton = styled(Button)({
  width: '100%',
  backgroundColor: '#9A2D8E',
  color: 'white',
  fontWeight: 'bold',
  marginTop: '16px',
  padding: '12px',
  borderRadius: '30px',
  textTransform: 'none',
  '&:hover': {
    backgroundColor: '#9A2D8E',
    color: 'white',
  },
});

interface PaymentOptionProps {
  value: string;
  label: string;
  icon: string;
}

const PaymentOption: React.FC<PaymentOptionProps> = ({value, label, icon}) => (
  <Box
    display="flex"
    alignItems="center"
    justifyContent="space-between"
    sx={{
      border: '1px solid #ddd',
      borderRadius: 8,
      padding: '4px 16px',
      backgroundColor: '#F8F9FA',
      marginBottom: 1,
      width: '100%',
    }}
  >
    <FormControlLabel
      value={value}
      control={<Radio sx={{color: 'navy'}} />}
      label={label}
    />
    <img
      src={`/assets/images/footer/${icon}.svg`}
      alt={label}
      style={{width: 50, height: 20}}
    />
  </Box>
);

const PaymentUI = () => {
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [gstInvoice, setGstInvoice] = useState(false);
  const [couponApplied, setCouponApplied] = useState(true);

  return (
    <Box justifyContent="flex-end">
      <StyledContainer>
        <Grid
          sx={{
            margin: {xs: '2% 5%', sm: '2% 10%'},
          }}
        >
          <Typography variant="h5" fontWeight="bold" gutterBottom>
            Payment Method
          </Typography>

          <CardContent>
            <RadioGroup
              value={paymentMethod}
              onChange={e => setPaymentMethod(e.target.value)}
            >
              <PaymentOption value="upi" label="UPI Payment" icon="upi" />
              <PaymentOption
                value="netbanking"
                label="Net Banking"
                icon="mastercard"
              />
            </RadioGroup>
          </CardContent>

          <>
            <Grid container justifyContent="space-between" alignItems="center">
              <Typography variant="body1" fontSize={'16px'} fontWeight="bold">
                Item(s) Total [MRP] -
              </Typography>
              <Typography variant="body1" fontSize={'16px'} fontWeight="bold">
                ₹1500
              </Typography>
            </Grid>
            <Grid container justifyContent="space-between" alignItems="center">
              <Typography variant="body1" fontSize={'16px'} fontWeight="bold">
                Shop Discount -
              </Typography>
              <Typography variant="body1" fontSize={'16px'} fontWeight="bold">
                ₹1500
              </Typography>
            </Grid>
            <Grid container justifyContent="space-between" alignItems="center">
              <Typography variant="body1" fontSize={'16px'} fontWeight="bold">
                Sub Total -
              </Typography>
              <Typography variant="body1" fontSize={'16px'} fontWeight="bold">
                ₹1500
              </Typography>
            </Grid>
            <Divider sx={{my: 2}} />
            <Grid container justifyContent="space-between" alignItems="center">
              <Typography variant="body1">Special Discount -</Typography>
              <Typography variant="body1">₹1500</Typography>
            </Grid>
            <Grid container justifyContent="space-between" alignItems="center">
              <Typography variant="body1">Promo Code -</Typography>
              <Typography variant="body1">₹1500</Typography>
            </Grid>
            <Grid container justifyContent="space-between" alignItems="center">
              <Typography variant="body1">Gift Wrap -</Typography>
              <Typography variant="body1">₹1500</Typography>
            </Grid>
            <Divider sx={{my: 2}} />
            <Grid container justifyContent="space-between" alignItems="center">
              <Typography variant="body1">Delivery Charge -</Typography>
              <Typography variant="body1" fontWeight="bold">
                ₹50
              </Typography>
            </Grid>
            <Grid container justifyContent="space-between" alignItems="center">
              <Typography variant="h6">Total Payable -</Typography>
              <Typography variant="h6" fontWeight="bold">
                ₹1050
              </Typography>
            </Grid>
            <Grid
              container
              justifyContent="space-between"
              alignItems="center"
              marginTop={'2%'}
            >
              <Typography variant="h6">Estimated delivery</Typography>
              <Typography variant="h6" fontWeight="bold">
                25 May - 08 Jun
              </Typography>
            </Grid>
          </>
          <StyledCard>
            <Box
              display="flex"
              alignItems="center"
              justifyContent="space-between"
            >
              <Typography fontWeight="bold">GST Invoice</Typography>
              <Radio
                checked={gstInvoice}
                onChange={() => setGstInvoice(!gstInvoice)}
                sx={{
                  color: 'navy',
                  '&.Mui-checked': {
                    color: 'navy',
                  },
                }}
              />
            </Box>
            {gstInvoice && (
              <Box mt={2}>
                <Typography variant="body1" fontWeight="500">
                  GST Number
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  size="small"
                  sx={{bgcolor: '#fff', mb: 2, borderRadius: '8px'}}
                />
                <Typography variant="body1" fontWeight="500">
                  Business Name
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  size="small"
                  sx={{bgcolor: '#fff', mb: 2, borderRadius: '8px'}}
                />
                <Typography variant="body1" fontWeight="500">
                  Address
                </Typography>
                <TextField
                  fullWidth
                  variant="outlined"
                  size="small"
                  multiline
                  rows={3}
                  sx={{bgcolor: '#fff', borderRadius: '8px'}}
                />
              </Box>
            )}
          </StyledCard>
          {/* Install App Promo */}
          <Typography mt={2} fontSize="14px" color="purple">
            Install our app and get an additional discount of Rs 100 on Your
            First Order for Product above Rs500.
          </Typography>

          {/* Coupon Input + App Store Button */}
          <Box display="flex" alignItems="center" mt={1}>
            <TextField
              variant="outlined"
              size="small"
              sx={{bgcolor: '#fff', borderRadius: '8px', flex: 1, mr: 1}}
            />
            <img
              src="https://developer.apple.com/assets/elements/badges/download-on-the-app-store.svg"
              alt="App Store"
              style={{height: '40px'}}
            />
          </Box>

          {couponApplied && (
            <CouponContainer>
              <Box display="flex" alignItems="center">
                <Gift />
                <Typography fontSize="14px">Coupon code Applied</Typography>
              </Box>
              <IconButton onClick={() => setCouponApplied(false)}>
                <Forbidden />
              </IconButton>
            </CouponContainer>
          )}

          <CheckoutButton>Proceed to checkout</CheckoutButton>
        </Grid>
      </StyledContainer>
    </Box>
  );
};

const AddressCard: React.FC<{title: string}> = ({title}) => {
  return (
    <Grid
      sx={{
        margin: ' 2%',
      }}
    >
      <Box sx={{mb: 3}}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h5" fontWeight="bold">
            {title}
          </Typography>
          <Typography variant="body2" color="textSecondary">
            2 addresses
          </Typography>
        </Box>
        <Card
          variant="outlined"
          sx={{mb: 2, p: 2, borderRadius: 2, marginTop: '2%'}}
        >
          <CardContent>
            <Box
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Box>
                <Typography variant="body1" fontWeight="bold">
                  Dummy, address of dummy
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Location map, directions to dummy
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Bangalore, Akshya Nagar 1st Block 1st Cross.
                </Typography>
              </Box>
              <Button
                sx={{
                  minWidth: 'auto',
                  textTransform: 'none',
                  cursor: 'default',
                  '&:hover': {
                    cursor: 'pointer',
                    backgroundColor: 'transparent',
                  },
                }}
              >
                Edit
              </Button>
            </Box>
          </CardContent>
        </Card>
        <Button
          variant="outlined"
          fullWidth
          startIcon={<Add />}
          sx={{borderRadius: 2}}
        >
          Add address
        </Button>
      </Box>
    </Grid>
  );
};

const ShippingBillingUI: React.FC = () => {
  return (
    <StyledContainer>
      <Grid
        sx={{
          margin: ' 6%',
        }}
      >
        <AddressCard title="Shipping Address" />
        <Typography variant="body2" color="textSecondary" sx={{mb: 4}}>
          Estimated delivery: 25 May - 08 Jun
          <br /> Dispatches from Mumbai
        </Typography>
        <Divider sx={{my: 3}} />
        <AddressCard title="Billing Address" />
      </Grid>
    </StyledContainer>
  );
};

const Checkout = () => {
  return (
    <Box display="flex" flexDirection="column" gap={3} marginBottom={'2.5%'}>
      <PaymentUI />
      <ShippingBillingUI />
    </Box>
  );
};

export default Checkout;
