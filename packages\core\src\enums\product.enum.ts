export enum OptionUnit {
  COLOR = 'color',
  SIZE = 'size',
  WEIGHT = 'weight',
  VOLUME = 'volume',
  LENGTH = 'length',
  WIDTH = 'width',
  HEIGHT = 'height',
  MATERIAL = 'material',
  STYLE = 'style',
  PATTERN = 'pattern',
  CAPACITY = 'capacity',
  DIMENSION = 'dimension',
  FLAVOR = 'flavor',
  SCENT = 'scent',
  PACKAGE_QUANTITY = 'package_quantity',
  PIECES = 'pieces',
  AGE_GROUP = 'age_group',
  GENDER = 'gender',
  FIT = 'fit',
  BRAND = 'brand',
}

export enum ProductStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}
