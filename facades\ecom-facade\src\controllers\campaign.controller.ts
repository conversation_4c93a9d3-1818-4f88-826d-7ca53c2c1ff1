import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Campaign} from '../models';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {PermissionKeys} from '@local/core';
import {authorize} from 'loopback4-authorization';
import {CONTENT_TYPE, restService, STATUS_CODE} from '@sourceloop/core';
import {CampaignService} from '../services';
import {service} from '@loopback/core';
import {CampaignProxyType} from '../datasources/configs';

const basePath = '/campaigns';

export class CampaignController {
  constructor(
    @service(CampaignService)
    private readonly campaignService: CampaignService,
    @restService(Campaign)
    private readonly campaignHelperService: CampaignProxyType,
  ) {}
  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCampaign]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Campaign model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Campaign)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Campaign, {
            title: 'NewCampaign',
            exclude: ['id'],
          }),
        },
      },
    })
    campaign: Omit<Campaign, 'id'>,
  ): Promise<Campaign> {
    return this.campaignService.createCampaign(campaign);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCampaign]})
  @get(`${basePath}/count`)
  @response(200, {
    description: 'Campaign model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(@param.where(Campaign) where?: Where<Campaign>): Promise<Count> {
    return this.campaignHelperService.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCampaign]})
  @get(basePath)
  @response(200, {
    description: 'Array of Campaign model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Campaign, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Campaign) filter?: Filter<Campaign>,
  ): Promise<Campaign[]> {
    return this.campaignHelperService.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCampaign]})
  @get(`${basePath}/{id}`)
  @response(200, {
    description: 'Campaign model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Campaign, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Campaign, {exclude: 'where'})
    filter?: FilterExcludingWhere<Campaign>,
  ): Promise<Campaign> {
    return this.campaignHelperService.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateCampaign]})
  @patch(`${basePath}/{id}`)
  @response(204, {
    description: 'Campaign PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(Campaign, {partial: true}),
        },
      },
    })
    campaign: Campaign,
  ): Promise<void> {
    await this.campaignHelperService.updateById(id, campaign);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateCampaign]})
  @put(`${basePath}/{id}`)
  @response(204, {
    description: 'Campaign PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() campaign: Campaign,
  ): Promise<void> {
    await this.campaignHelperService.replaceById(id, campaign);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteCampaign]})
  @del(`${basePath}/{id}`)
  @response(204, {
    description: 'Campaign DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.campaignHelperService.deleteById(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateCampaign]})
  @post(`${basePath}/send`)
  @response(STATUS_CODE.OK, {
    description: 'Send campaign response',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Campaign)}},
  })
  async sendCampaign(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: {
            type: 'object',
            required: ['campaignKey'],
            properties: {
              campaignKey: {
                type: 'string',
                description: 'The key of the campaign to send',
              },
            },
          },
        },
      },
    })
    body: {
      campaignKey: string;
    },
    @param.header.string('Authorization')
    token: string,
  ): Promise<Campaign> {
    return this.campaignService.sendCampaign(body.campaignKey, token);
  }
}
