import {model, property, hasMany} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {SellerShippingProfile} from './seller-shipping-profile.model';

export enum ShippingMethodType {
  ECOMDUKES = 'ECOMDUKES',
  SELF_SHIPPING = 'SELF_SHIPPING',
}

@model({
  name: 'shipping_methods',
})
export class ShippingMethod extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'boolean',
    default: true,
    name: 'is_active',
  })
  isActive?: boolean;

  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      enum: Object.values(ShippingMethodType),
    },
  })
  type: ShippingMethodType;

  @hasMany(() => SellerShippingProfile, {keyTo: 'shippingMethodId'})
  sellerProfiles: SellerShippingProfile[];

  constructor(data?: Partial<ShippingMethod>) {
    super(data);
  }
}

export interface ShippingMethodRelations {
  sellerProfiles?: SellerShippingProfile[];
}

export type ShippingMethodWithRelations = ShippingMethod &
  ShippingMethodRelations;
