import { apiSlice } from 'redux/apiSlice';
import { Faq, Feature, Plan } from 'types/auth';
import { ApiSliceIdentifier } from 'enums/api.enum';
import { FaqVisibility } from 'enums/faqvisibility.enum';

export const tokenApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getPlansWithToken: builder.query<Plan[], string>({
      query: (code) => ({
        url: `/plans`,
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: { status: 'ACTIVE' },
            include: [
              {
                relation: 'planFeatureValues',
                scope: {
                  include: [
                    {
                      relation: 'featureValue',
                      scope: {
                        include: ['feature']
                      }
                    }
                  ]
                }
              },
              {
                relation: 'planPricings',
                scope: {
                  order: ['minSalesThreshold ASC']
                }
              }
            ],
            skip: 0,
            order: 'name ASC'
          })
        },
        headers: {
          Authorization: `Bearer ${code}`
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    getFeaturesWithToken: builder.query<Feature[], string>({
      query: (code) => ({
        url: `/features`,
        method: 'GET',
        headers: {
          Authorization: `Bearer ${code}`
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    getTokenWithFaqs: builder.query<Faq[], string>({
      query: (code) => ({
        url: `/faqs`,
        method: 'GET',
        params: {
          filter: JSON.stringify({
            where: {
              visibility: { inq: [FaqVisibility.SELLER, FaqVisibility.ALL] }
            },
            order: ['priority DESC']
          })
        },
        headers: {
          Authorization: `Bearer ${code}`
        },
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    })
  })
});
export const { useGetPlansWithTokenQuery, useGetFeaturesWithTokenQuery, useGetTokenWithFaqsQuery } = tokenApiSlice;
