import {injectable, inject, BindingScope} from '@loopback/core';
import {ProductVariant} from '../models';
import {PgDataSource} from '../datasources';
import {FilterExcludingWhere, repository} from '@loopback/repository';
import {ProductVariantRepository} from '../repositories';
import {fieldsExcludeMetaFields} from '../constants';

@injectable({scope: BindingScope.TRANSIENT})
export class TopSellingProductsService {
  constructor(
    @inject('datasources.pg') private readonly pgDataSource: PgDataSource,
    @repository(ProductVariantRepository)
    public productVariantRepository: ProductVariantRepository,
  ) {}

  async getTopSellingProductVariants(
    filter?: FilterExcludingWhere<ProductVariant>,
    limit = 10,
  ): Promise<ProductVariant[]> {
    const result = await this.pgDataSource.execute(
      `
    SELECT
      oli.product_variant_id,
      SUM(oli.quantity) AS total_sold
    FROM
      main.order_line_items oli
    JOIN
      main.orders o ON o.id = oli.order_id
    WHERE
      oli.deleted = false
      AND o.status = 'paid'
    GROUP BY
      oli.product_variant_id
    ORDER BY
      total_sold DESC
    LIMIT $1
  `,
      [limit],
    );

    const rows = result[0] as {product_variant_id: string}[];

    const productVariantIds = rows.map(r => r.product_variant_id);

    if (!productVariantIds.length) return [];

    return this.productVariantRepository.find({
      fields: fieldsExcludeMetaFields,
      where: {id: {inq: productVariantIds}},
      include: filter?.include,
    });
  }
}
