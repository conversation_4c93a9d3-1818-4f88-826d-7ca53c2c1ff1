import {ModifiedRestService, RestOperationTemplate} from '@sourceloop/core';
import {Chat} from '../../models';

export interface ChatProxyType extends ModifiedRestService<Chat> {
  createChat(chat: Partial<Chat>, token: string): Promise<Chat>;
}

export const ChatProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'POST',
      url: '/chats',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      createChat: ['body', 'token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/chats',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
      query: {
        filter: '{filter}',
      },
    },
    functions: {
      find: ['filter', 'token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/chats/count',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
      query: {
        where: '{where}',
      },
    },
    functions: {
      count: ['where', 'token'],
    },
  },
  {
    template: {
      method: 'GET',
      url: '/chats/{id}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
      query: {
        filter: '{filter}',
      },
    },
    functions: {
      findById: ['id', 'filter', 'token'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: '/chats/{id}',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      updateById: ['id', 'body', 'token'],
    },
  },
  {
    template: {
      method: 'DELETE',
      url: '/chats/{id}',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
    },
    functions: {
      deleteById: ['id', 'token'],
    },
  },
];
