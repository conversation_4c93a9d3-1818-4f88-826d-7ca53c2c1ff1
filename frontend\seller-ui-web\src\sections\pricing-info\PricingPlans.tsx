'use client';

import { ListItemText } from '@mui/material';
import { Box, Typography, Grid, Stack, Button, List, ListItem } from '@mui/material';
import MainCard from 'components/MainCard';
import { Fragment, useEffect, useState } from 'react';
import { setGuestToken } from '../../redux/auth/authSlice';
import { useGetGuestTokenMutation } from '../../redux/app/terms-and-condition/termsApiSlice';
import { useDispatch } from 'react-redux';
import { useGetFeaturesWithTokenQuery, useGetPlansWithTokenQuery } from '../../redux/ecom/guest-tokenApiSlice';

export default function PlansPricing({ onViewMoreClick }: { onViewMoreClick: () => void }) {
  const [guestCode, setGuestCode] = useState<string | null>(null);

  const { data: plans = [] } = useGetPlansWithTokenQuery(guestCode!, {
    skip: !guestCode
  });
  const { data: features = [] } = useGetFeaturesWithTokenQuery(guestCode!, {
    skip: !guestCode
  });
  const [getToken] = useGetGuestTokenMutation();
  const dispatch = useDispatch();

  const currencySymbols: Record<string, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    INR: '₹'
  };

  useEffect(() => {
    const handleLogin = async () => {
      const result = await getToken().unwrap();
      if (result?.accessToken) {
        dispatch(setGuestToken(result));
        setGuestCode(result.accessToken);
      }
    };

    handleLogin();
  }, [getToken, dispatch]);

  return (
    <Box sx={{ px: { xs: 2, sm: 4, md: 8 }, py: 6 }}>
      <Box
        sx={{
          bgcolor: 'white',
          borderRadius: '16px',
          padding: 4,
          mt: 2,
          pl: { xs: 3, sm: 6, md: 10 }
        }}
      >
        <Grid item xs={12}>
          <Typography
            variant="h4"
            fontWeight="bold"
            color="#000050"
            gutterBottom
            sx={{
              color: '#000050',
              fontWeight: 'bold',
              mb: 3,
              textAlign: 'left',
              fontSize: { xs: '1rem', sm: '1.2rem', md: '1.995rem' }
            }}
          >
            Plans pricing
          </Typography>
        </Grid>
        <Grid item container spacing={3} xs={12} alignItems="center">
          {plans.map((plan, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <MainCard>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Box>
                      <Grid container spacing={3}>
                        <Grid item xs={12}>
                          <Stack spacing={0} textAlign="center">
                            <Typography variant="h4">{plan.name}</Typography>
                          </Stack>
                        </Grid>
                        <Grid item xs={12}>
                          <Stack spacing={0.5} alignItems="center">
                            {plan.planPricings.map((pricing) => (
                              <Typography key={pricing.id} variant="body2">
                                {currencySymbols[plan.currency] ?? plan.currency} {pricing.price} for {pricing.minSalesThreshold}–
                                {pricing.maxSalesThreshold} sales
                              </Typography>
                            ))}
                          </Stack>
                        </Grid>
                        <Grid item xs={12}>
                          <Button color="secondary" variant="outlined" fullWidth>
                            Order Now
                          </Button>
                        </Grid>
                      </Grid>
                    </Box>
                  </Grid>
                  <Grid item xs={12}>
                    <List
                      sx={{
                        m: 0,
                        p: 0,
                        '&> li': {
                          px: 0,
                          py: 0.625
                        }
                      }}
                      component="ul"
                    >
                      {features.slice(0, 8).map((feature) => (
                        <Fragment key={feature.id}>
                          <ListItem key={feature.id}>
                            <ListItemText primary={feature.name} sx={{ textAlign: 'center' }} />
                          </ListItem>
                        </Fragment>
                      ))}
                    </List>
                    <Typography
                      variant="h5"
                      color="#5847f9"
                      sx={{ mt: 1, cursor: 'pointer', textAlign: 'center', textDecoration: 'underline' }}
                      onClick={onViewMoreClick}
                    >
                      View More
                    </Typography>
                  </Grid>
                </Grid>
              </MainCard>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
}
