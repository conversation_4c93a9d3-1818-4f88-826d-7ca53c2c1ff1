import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from 'redux/apiSlice';
import { IFilter } from 'redux/app/types/filter';
import { BulkSection, PageSection } from 'types/cms';
import { buildFilterParams } from 'utils/buildFilterParams';

export const pageSectionApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    upsertPageSection: builder.mutation<PageSection, BulkSection>({
      query: (body) => ({
        url: `page-sections/bulk`,
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body
      })
    }),
    getPageSections: builder.query<PageSection[], IFilter>({
      query: (filter) => ({
        url: `page-sections`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),
    removePageSection: builder.mutation<void, string>({
      query: (id) => ({
        url: `/page-sections/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),
    reorderPageSection: builder.mutation<void, { id: string; displayOrder: number }>({
      query: ({ id, displayOrder }) => ({
        url: `/page-sections/${id}/reorder`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: {
          displayOrder
        }
      })
    })
  })
});

export const { useUpsertPageSectionMutation, useGetPageSectionsQuery, useRemovePageSectionMutation, useReorderPageSectionMutation } =
  pageSectionApiSlice;
