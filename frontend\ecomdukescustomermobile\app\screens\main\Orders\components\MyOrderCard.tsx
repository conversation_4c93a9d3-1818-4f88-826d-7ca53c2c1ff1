import React, {useState} from 'react';
import {View, Text, StyleSheet, Image, TouchableOpacity} from 'react-native';
import {Icon} from 'react-native-paper';
import {colors} from '../../../../theme/colors';
import customColors from '../../../../theme/customColors';
import StepIndicator from 'react-native-step-indicator';
import {
  getOrderStatusColor,
  getOrderStatusLabel,
} from '../../../../utils/order';
import {OrderLineItem, OrderStatus} from '../../../../redux/order/order';
import {Product} from '../../../../redux/product/product';
import ReviewModal from '../../review/ReviewModal';

interface OrderCardProps {
  productImage: string;
  productName: string;
  deliveredOn: string;
  deliveryAddress: string;
  quantity: number;
  price: string;
  showDetails: boolean;
  onToggleDetails: () => void;
  isDelivered: boolean;
  status: string;
  stepConfig: {
    label: string;
    status: string[];
    message: string;
  }[];
  activeStep: number;
  onPress: () => void;
  isReviewed?: boolean;
  handleReview?: (product: Product, rating: number) => void;
  product?: Product;
  reviewId: string;
  productVariantId: string;
  orderLineItemId: string;
  customerId: string;
  isReview: boolean;
  refetch?: () => void;
  orderLineItems: OrderLineItem[];
}

const MyOrderCard: React.FC<OrderCardProps> = ({
  productImage,
  productName,
  deliveredOn,
  deliveryAddress,
  quantity,
  price,
  activeStep,
  showDetails,
  onToggleDetails,
  isDelivered,
  status,
  onPress,
  reviewId,
  productVariantId,
  orderLineItemId,
  customerId,
  isReview,
  refetch,
  orderLineItems,
}) => {
  const currencyCodeSymbolMap: Map<string, string> = new Map([
    ['INR', '₹'],
    ['USD', '$'],
  ]);

  const [isReviewModalVisible, setReviewModalVisible] = useState(false);

  const customStyles = {
    stepIndicatorSize: 30,
    currentStepIndicatorSize: 40,
    separatorStrokeWidth: 10,
    currentStepStrokeWidth: 3,
    stepStrokeCurrentColor: colors.tertiary,
    stepStrokeWidth: 2,
    stepStrokeFinishedColor: colors.tertiary,
    stepStrokeUnFinishedColor: colors.gray.medium,
    separatorFinishedColor: colors.tertiary,
    separatorUnFinishedColor: colors.gray.medium,
    stepIndicatorFinishedColor: colors.tertiary,
    stepIndicatorUnFinishedColor: customColors.white,
    stepIndicatorCurrentColor: customColors.white,
    stepIndicatorLabelFontSize: 15,
    currentStepIndicatorLabelFontSize: 15,
    stepIndicatorLabelFinishedColor: colors.tertiary,
    stepIndicatorLabelUnFinishedColor: colors.tertiary,
    labelColor: colors.gray.dark,
    labelSize: 13,
    currentStepLabelColor: customColors.textBlack,
  };

  const labels = ['Order Placed', 'Shipped', 'Out for delivery', 'Delivered'];
  const icons = ['thumb-up', 'airplane', 'van-utility', 'gift'];

  const renderStepIndicator = ({
    position,
    stepStatus,
  }: {
    position: number;
    stepStatus: 'finished' | 'current' | 'unfinished';
  }) => {
    return (
      <Icon
        source={icons[position]}
        size={20}
        color={
          stepStatus === 'finished'
            ? customColors.white
            : stepStatus === 'current'
            ? colors.tertiary
            : colors.gray.medium
        }
      />
    );
  };
  const handleReviewDelete = () => {
    if (refetch) {
      refetch();
    }
  };

  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
      <View style={styles.card}>
        <View style={styles.row}>
          <Image source={{uri: productImage}} style={styles.productImage} />
          <View style={styles.info}>
            <Text
              numberOfLines={2}
              ellipsizeMode="tail"
              style={styles.productName}>
              {productName}
            </Text>
            <Text style={styles.price}>
              {' '}
              {`${currencyCodeSymbolMap.get('INR') || ''}${price}`}
            </Text>

            <View style={styles.row}>
              <Icon
                source={'checkbox-blank-circle'}
                size={15}
                color={getOrderStatusColor(status as OrderStatus)}
              />
              <Text
                style={[
                  styles.deliveredText,
                  {
                    color:
                      status.toLowerCase() === 'delivered'
                        ? customColors.successGreen
                        : colors.tertiary,
                  },
                ]}>
                {status === 'delivered'
                  ? `Delivered on ${deliveredOn}`
                  : getOrderStatusLabel(status as OrderStatus)}
              </Text>
            </View>

            {isDelivered && (
              <View style={styles.reviewSection}>
                <TouchableOpacity onPress={() => setReviewModalVisible(true)}>
                  <View style={styles.reviewTextView}>
                    <Icon
                      size={25}
                      source={'star-outline'}
                      color={customColors.primaryContainer}
                    />
                    <Text style={styles.reviewText}>
                      {isReview ? 'Edit Review' : 'Rate this product now'}
                    </Text>
                  </View>
                </TouchableOpacity>
                <ReviewModal
                  visible={isReviewModalVisible}
                  onDismiss={() => setReviewModalVisible(false)}
                  reviewId={reviewId}
                  productVariantId={productVariantId}
                  orderLineItemId={orderLineItemId}
                  customerId={customerId}
                  refetch={refetch}
                  onDeleteSuccess={handleReviewDelete}
                  orderLineItems={orderLineItems}
                />
              </View>
            )}
            <TouchableOpacity onPress={onToggleDetails} style={styles.arrow}>
              <Icon
                source={showDetails ? 'chevron-up' : 'chevron-down'}
                size={24}
                color={colors.tertiary}
              />
            </TouchableOpacity>
          </View>
        </View>

        {showDetails && (
          <>
            <View style={styles.addressBlock}>
              <View style={styles.addressTextWrapper}>
                <Text style={styles.addressLabel}>Delivery Address</Text>
                <Text
                  style={styles.addressText}
                  numberOfLines={8}
                  ellipsizeMode="tail">
                  {deliveryAddress}
                </Text>
              </View>

              <View style={styles.invoiceBlock}>
                <Text style={styles.qty}>Quantity: {quantity}</Text>
                <Text>Invoice</Text>
              </View>
            </View>

            <StepIndicator
              customStyles={customStyles}
              currentPosition={activeStep}
              labels={labels}
              stepCount={labels.length}
              renderStepIndicator={renderStepIndicator}
            />
            <View style={styles.actionRow}>
              <TouchableOpacity style={styles.buyBtn}>
                <Text style={styles.buyText}>Buy Again</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.returnBtn}>
                <Text style={styles.returnText}>Return/Refund</Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default MyOrderCard;

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.gray.card,
    borderRadius: 15,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: colors.gray.light,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    gap: 10,
  },
  productImage: {
    height: 100,
    width: 100,
    borderRadius: 10,
    marginRight: 10,
  },
  info: {
    flex: 1,
  },
  productName: {
    fontWeight: 'bold',
    fontSize: 16,
  },
  productDesc: {
    color: colors.gray.dark,
    marginVertical: 4,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.green.success,
    marginRight: 6,
  },
  deliveredText: {
    fontSize: 13,
    fontWeight: 'bold',
    color: colors.green.success,
  },
  reviewLink: {
    color: colors.tertiary,
    marginTop: 4,
    fontWeight: 'bold',
  },
  price: {
    fontWeight: 'bold',
    fontSize: 16,
    color: colors.tertiary,
    top: 0,
    right: 0,
    marginTop: 10,
    marginBottom: 10,
  },
  addressTextWrapper: {
    flex: 1, // allow to take up all available space
    paddingRight: 8, // space between text and invoiceBlock
  },

  addressBlock: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 15,
  },
  addressLabel: {
    fontWeight: 'bold',
    marginBottom: 5,
  },
  addressText: {
    color: colors.gray.dark,
  },
  invoiceBlock: {
    alignItems: 'flex-end',
  },
  qty: {
    fontWeight: '600',
    marginBottom: 5,
  },
  downloadBtn: {
    backgroundColor: colors.tertiary,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  downloadText: {
    fontWeight: 'bold',
    color: customColors.white,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  buyBtn: {
    backgroundColor: customColors.primaryContainer,
    padding: 10,
    borderRadius: 25,
    flex: 1,
    marginRight: 10,
    alignItems: 'center',
  },
  returnBtn: {
    backgroundColor: colors.gray.medium,
    padding: 10,
    borderRadius: 25,
    flex: 1,
    alignItems: 'center',
  },
  buyText: {
    color: customColors.white,
    fontWeight: 'bold',
  },
  returnText: {
    fontWeight: 'bold',
  },
  arrow: {alignSelf: 'flex-end', marginTop: 8},
  reviewSection: {
    marginTop: 8,
    alignItems: 'flex-start',
  },

  starsRow: {
    flexDirection: 'row',
    marginBottom: 4,
  },

  reviewText: {
    color: colors.tertiary,
    fontSize: 14,
    fontWeight: 'bold',
  },
  reviewTextView: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    gap: 10,
  },
});
