import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {Duke<PERSON>oin} from '../models';
import {ConfigurationRepository, DukeCoinRepository} from '../repositories';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys, TransactionType} from '@local/core';

const basePath = '/duke-coins';

export class Duke<PERSON>oinController {
  constructor(
    @repository(DukeCoinRepository)
    public dukeCoinRepository: DukeCoinRepository,
    @repository(ConfigurationRepository)
    public configurationRepository: ConfigurationRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateDukeCoin]})
  @post(basePath, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'DukeCoin model instance',
        content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(DukeCoin)}},
      },
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(DukeCoin, {
            title: 'NewDukeCoin',
            exclude: ['id'],
          }),
        },
      },
    })
    dukeCoin: Omit<DukeCoin, 'id'>,
  ): Promise<DukeCoin> {
    return this.dukeCoinRepository.create(dukeCoin);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDukeCoin]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'DukeCoin model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(@param.where(DukeCoin) where?: Where<DukeCoin>): Promise<Count> {
    return this.dukeCoinRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDukeCoin]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of DukeCoin model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(DukeCoin, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(DukeCoin) filter?: Filter<DukeCoin>,
  ): Promise<DukeCoin[]> {
    return this.dukeCoinRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateDukeCoin]})
  @patch(basePath)
  @response(STATUS_CODE.OK, {
    description: 'DukeCoin PATCH success count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(DukeCoin, {partial: true}),
        },
      },
    })
    dukeCoin: DukeCoin,
    @param.where(DukeCoin) where?: Where<DukeCoin>,
  ): Promise<Count> {
    return this.dukeCoinRepository.updateAll(dukeCoin, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDukeCoin]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'DukeCoin model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(DukeCoin, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(DukeCoin, {exclude: 'where'})
    filter?: FilterExcludingWhere<DukeCoin>,
  ): Promise<DukeCoin> {
    return this.dukeCoinRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateDukeCoin]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'DukeCoin PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(DukeCoin, {partial: true}),
        },
      },
    })
    dukeCoin: DukeCoin,
  ): Promise<void> {
    await this.dukeCoinRepository.updateById(id, dukeCoin);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateDukeCoin]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'DukeCoin PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() dukeCoin: DukeCoin,
  ): Promise<void> {
    await this.dukeCoinRepository.replaceById(id, dukeCoin);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteDukeCoin]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'DukeCoin DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.dukeCoinRepository.deleteById(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateDukeCoin]})
  @post(`${basePath}/users/{id}/coins/add`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'Coins added',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              properties: {
                balance: {type: 'number'},
              },
            },
          },
        },
      },
    },
  })
  async addCoins(
    @param.path.string('id') userId: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: {
            type: 'object',
            properties: {
              amount: {type: 'number', minimum: 1},
              description: {type: 'string'},
            },
            required: ['amount'],
          },
        },
      },
    })
    body: {amount: number; description?: string},
  ): Promise<{balance: number}> {
    const {amount, description} = body;

    if (amount <= 0) {
      throw new Error('Amount to add must be positive.');
    }

    await this.dukeCoinRepository.create({
      coinsChanged: amount,
      transactionType: TransactionType.Earn,
      description: description ?? 'Added coins',
    });

    const balance = await this.dukeCoinRepository.getUserBalance();

    return {balance};
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDukeCoin]})
  @get(`${basePath}/users/coins/balance`, {
    responses: {
      [STATUS_CODE.OK]: {
        description: 'User coin balance',
        content: {
          [CONTENT_TYPE.JSON]: {
            schema: {
              type: 'object',
              properties: {
                balance: {type: 'number'},
                maxApplicable: {type: 'number'},
              },
            },
          },
        },
      },
    },
  })
  async getCoinBalance(): Promise<{balance: number; maxApplicable: number}> {
    const balance = await this.dukeCoinRepository.getUserBalance();

    const config = await this.configurationRepository.findOne({
      where: {key: 'dukes_coins.max_applicable_per_transaction'},
    });

    const maxAllowed = config ? Number(config.value) : 0;
    const maxApplicable = Math.min(balance, maxAllowed);

    return {
      balance,
      maxApplicable,
    };
  }
}
