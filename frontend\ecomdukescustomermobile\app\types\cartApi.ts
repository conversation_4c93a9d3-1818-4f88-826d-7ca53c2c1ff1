import {ProductVariant} from '../redux/product/product';

export enum CartStatus {
  Active = 'active',
  InActive = 'in-active',
  Abandoned = 'abandoned',
  Completed = 'completed',
}

export interface Cart {
  id: string;
  customerId: string;
  status: CartStatus | string;
  cartItems?: CartItem[];
  promoCode?: PromoCode;
  promoCodeId?: string;
}
export interface PromoCode {
  id: string;
  code: string;
  value: number;
  minCartValue?: number;
  maxDiscountCap?: number;
  usageLimitPerUser?: number;
  usageLimitTotal?: number;
  isFirstTimeUserOnly?: boolean;
  isRepeatUserOnly?: boolean;
  validFrom: string;
  validTill: string;
  isActive: boolean;
  categoryIds?: string[];
  productIds?: string[];
  appliesTo: AppliesTo;
}
export enum AppliesTo {
  all = 'all',
  products = 'products',
  categories = 'categories',
}
export interface CartItem {
  id: string;
  quantity: number;
  cartId: string;
  productVariantId: string;
  cart?: Cart;
  productVariant?: ProductVariant;
  customizations?: Record<string, any>;
}
export interface IFilter {
  limit?: number;
  skip?: number;
  order?: Array<Record<string, unknown> | string>;
  where?: Record<string, unknown>;
  fields?: Record<string, boolean>;
  include?: Array<Record<string, unknown> | string>;
}

export const buildFilterParams = (filterOptions: IFilter = {}): string => {
  const {limit, skip, order, where, fields, include} = filterOptions;

  const filter = {
    ...(limit !== undefined && {limit}),
    ...(skip !== undefined && {offset: skip}),
    ...(order !== undefined && {order}),
    ...(where !== undefined && {where}),
    ...(fields !== undefined && {fields}),
    ...(include !== undefined && {include}),
  };
  return JSON.stringify(filter);
};
export interface CheckoutCartRequest {
  shippingAddressId: string;
  billingAddressId: string;
}
export interface CheckoutCartResponse {
  cart_details: null | any;
  cf_order_id: string;
  created_at: string;
  customer_details: {
    customer_id: string;
    customer_name: string;
    customer_email: string;
    customer_phone: string;
    customer_uid: string | null;
  };
  entity: string;
  order_amount: number;
  order_currency: string;
  order_expiry_time: string;
  order_id: string;
  order_meta: {
    notify_url: string | null;
    payment_methods: string | null;
    payment_methods_filters: string | null;
    return_url: string | null;
  };
  order_note: string;
  order_splits: any[];
  order_status: string;
  order_tags: string | null;
  payment_session_id: string;
  products: {
    one_click_checkout: {
      enabled: boolean;
      conditions: any[];
    };
    verify_pay: {
      enabled: boolean;
      conditions: any[];
    };
  };
  terminal_data: any | null;
}
