import {Getter, inject} from '@loopback/core';
import {PgDataSource} from '../datasources';
import {UserFcm, UserFcmRelations} from '../models';
import {NotifDbSourceName} from '@sourceloop/notification-service';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {SequelizeUserModifyCrudRepository} from '@sourceloop/core/sequelize';

export class UserFcmRepository extends SequelizeUserModifyCrudRepository<
  UserFcm,
  typeof UserFcm.prototype.id,
  UserFcmRelations
> {
  constructor(
    @inject(`datasources.${NotifDbSourceName}`) dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {
    super(UserFcm, dataSource, getCurrentUser);
  }
}
