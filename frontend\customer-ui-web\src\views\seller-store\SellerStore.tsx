'use client';

import React, {useState} from 'react';
import {styled} from '@mui/material/styles';
import {
  Button,
  Box,
  Typography,
  Rating,
  Stack,
  useTheme,
  Menu,
  MenuItem,
  Divider,
  Tooltip,
} from '@mui/material';
import ShareIcon from '@mui/icons-material/Share';
import PhoneIcon from '@mui/icons-material/Phone';
import MessageIcon from '@mui/icons-material/Message';
import {CloseCircle, Copy, Sms} from 'iconsax-react';
import Image from 'next/image';
import {
  useCreateChatMutation,
  useGetUserChatsQuery,
} from 'redux/chat/chatApiSlice';
import {useRouter} from 'next/navigation';
import {ChatDto} from 'types/chat';

const RoundedButton = styled(Button)(({theme}) => ({
  borderRadius: 24,
  padding: '10px 20px',
  textTransform: 'none',
  boxShadow: 'none',
  fontWeight: 500,
  '&:hover': {
    boxShadow: theme.shadows[2],
  },
}));

interface StoreProfileSectionProps {
  allowBulkOrder?: boolean;
  sellerId: string;
  phoneNumber?: number;
  email?: string;
  onChatCreated?: (chat: ChatDto) => void; // ✅ new prop
}

const StoreProfileSection: React.FC<StoreProfileSectionProps> = ({
  allowBulkOrder,
  sellerId,
  phoneNumber,
  email,
  onChatCreated,
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [anchorElContact, setAnchorElContact] = useState<null | HTMLElement>(
    null,
  );
  const openContactMenu = Boolean(anchorElContact);
  const openMenu = Boolean(anchorEl);
  const [createChat] = useCreateChatMutation();
  const [, setCopied] = useState(false);
  const {data: userChats = []} = useGetUserChatsQuery({});
  const router = useRouter();

  const storeLink = `${process.env.NEXT_PUBLIC_CUSTOMER_UI_URL}/seller-stores/${sellerId}`;

  const handleClickMenu = (event: React.MouseEvent<HTMLElement>) =>
    setAnchorEl(event.currentTarget);
  const handleCloseMenu = () => setAnchorEl(null);
  const handleContactMenuClick = (event: React.MouseEvent<HTMLElement>) =>
    setAnchorElContact(event.currentTarget);
  const handleCloseContactMenu = () => setAnchorElContact(null);

  const handleCopy = () => {
    navigator.clipboard.writeText(storeLink);
    setCopied(true);
  };

  const handleWhatsappShare = () => {
    const message = `Hey! 👋 Check out this amazing store on Ecomdukes: ${storeLink}`;
    window.open(`https://wa.me/?text=${encodeURIComponent(message)}`, '_blank');
  };

  const handleEmailShare = () => {
    const subject = 'Check out this amazing store!';
    const body = `Hey! 👋 Check out this amazing store on Ecomdukes: ${storeLink}`;
    window.open(
      `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`,
    );
  };

  const handleFacebookShare = () => {
    const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(storeLink)}`;
    window.open(url, '_blank');
  };

  const handleMessageSeller = async (sellerId: string) => {
    const existingChat = userChats.find(c => c.sellerId === sellerId);
    const chat = existingChat ?? (await createChat({sellerId}).unwrap());
    sessionStorage.setItem('selectedChat', JSON.stringify(chat));
    router.push('/message');
  };

  return (
    <Box sx={{px: 4, py: 6, backgroundColor: 'background.paper'}}>
      <Stack
        direction="row"
        alignItems="center"
        justifyContent="space-between"
        flexWrap="wrap"
        mb={5}
      >
        <Stack direction="row" alignItems="center" spacing={14}>
          <Typography variant="h5" fontWeight={700} color="#000845">
            About store
          </Typography>
          <Box display="flex" alignItems="center">
            <Typography variant="h5" fontWeight={700} color="#000845" mr={1}>
              Store rating :
            </Typography>
            <Rating
              name="store-rating"
              value={4}
              readOnly
              sx={{color: '#000845'}}
            />
          </Box>
        </Stack>
        <Typography variant="body1" fontWeight={500}>
          Order Closed Till :{' '}
          <Box component="span" color={theme.palette.primary.main}>
            20-08-2023
          </Box>
        </Typography>
      </Stack>

      <Typography variant="body1" color="text.secondary" mb={5}>
        In publishing and graphic design, Lorem ipsum is a placeholder text
        commonly used to demonstrate the visual form...
      </Typography>

      <Stack
        direction="row"
        flexWrap="wrap"
        spacing={1.5}
        useFlexGap
        rowGap={1.5}
        columnGap={2}
        mb={3}
      >
        <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
          <RoundedButton
            variant="outlined"
            onClick={handleClickMenu}
            sx={{
              px: 1.5,
              py: 1.2,
              minWidth: 'auto',
              backgroundColor: '#F5F5F5',
              color: '#000845',
              borderColor: '#000845',
            }}
          >
            <ShareIcon />
          </RoundedButton>
          <Typography variant="h5" color="#000845">
            Share this store with your friends
          </Typography>
        </Box>

        {allowBulkOrder && (
          <RoundedButton
            variant="contained"
            onClick={handleClickMenu}
            sx={{
              background: theme.palette.primary.main,
              color: '#fff',
              minWidth: 220,
              px: 2.5,
              py: 1.2,
              ml: 18,
            }}
          >
            Custom Bulk Order Request
          </RoundedButton>
        )}

        <Menu anchorEl={anchorEl} open={openMenu} onClose={handleCloseMenu}>
          <MenuItem
            onClick={() => {
              handleEmailShare();
              handleCloseMenu();
            }}
          >
            <Sms size="20" style={{marginRight: 8}} /> Email
          </MenuItem>
          <MenuItem
            onClick={() => {
              handleWhatsappShare();
              handleCloseMenu();
            }}
          >
            <Image
              src="/assets/images/footer/whatsapp.svg"
              alt="Whatsapp"
              width={25}
              height={25}
              style={{marginRight: 8}}
            />{' '}
            Whatsapp
          </MenuItem>
          <MenuItem
            onClick={() => {
              handleFacebookShare();
              handleCloseMenu();
            }}
          >
            <Image
              src="/assets/images/footer/facebook.svg"
              alt="Facebook"
              width={20}
              height={20}
              style={{marginRight: 8}}
            />{' '}
            Facebook
          </MenuItem>
          <Divider />
          <Tooltip title={storeLink} placement="left" arrow>
            <MenuItem
              onClick={() => {
                handleCopy();
                handleCloseMenu();
              }}
            >
              <Copy size="20" style={{marginRight: 8}} /> Copy Link
            </MenuItem>
          </Tooltip>
          <MenuItem onClick={handleCloseMenu} sx={{justifyContent: 'center'}}>
            <CloseCircle size="20" />
          </MenuItem>
        </Menu>

        <RoundedButton
          variant="outlined"
          startIcon={<PhoneIcon />}
          onClick={handleContactMenuClick}
          sx={{
            backgroundColor: '#F5F5F5',
            minWidth: 180,
            color: 'black',
            borderColor: 'black',
            px: 2.5,
            py: 1.2,
            ml: 26,
          }}
        >
          Contact Details
        </RoundedButton>

        <Menu
          anchorEl={anchorElContact}
          open={openContactMenu}
          onClose={handleCloseContactMenu}
        >
          {email && (
            <MenuItem>
              <Typography variant="body2">
                📧 Email: <strong>{email}</strong>
              </Typography>
              <Tooltip title="Copy Email">
                <Copy
                  size="18"
                  style={{cursor: 'pointer', marginLeft: 4}}
                  onClick={() => {
                    navigator.clipboard.writeText(email);
                    handleCloseContactMenu();
                  }}
                />
              </Tooltip>
            </MenuItem>
          )}
          {phoneNumber && (
            <MenuItem>
              <Typography variant="body2">
                📞 Phone: <strong>{phoneNumber}</strong>
              </Typography>
              <Tooltip title="Copy Phone Number">
                <Copy
                  size="18"
                  style={{cursor: 'pointer'}}
                  onClick={() => {
                    navigator.clipboard.writeText(`${phoneNumber}`);
                    handleCloseContactMenu();
                  }}
                />
              </Tooltip>
            </MenuItem>
          )}
          {phoneNumber && (
            <MenuItem
              onClick={() => {
                const whatsappMsg = `Hi! I'm interested in your store on Ecomdukes.`;
                const whatsappNumber = phoneNumber
                  .toString()
                  .replace(/\D/g, '');
                window.open(
                  `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(whatsappMsg)}`,
                  '_blank',
                );
                handleCloseContactMenu();
              }}
            >
              <Typography variant="body2">💬 WhatsApp</Typography>
            </MenuItem>
          )}
          <MenuItem
            onClick={handleCloseContactMenu}
            sx={{justifyContent: 'center'}}
          >
            <CloseCircle size="20" />
          </MenuItem>
        </Menu>

        <Box flexGrow={1} />

        <RoundedButton
          variant="contained"
          startIcon={<MessageIcon />}
          onClick={() => handleMessageSeller(sellerId)}
          sx={{
            backgroundColor: '#000845',
            color: '#fff',
            minWidth: 180,
            px: 2.5,
            py: 1.2,
          }}
        >
          Message Seller
        </RoundedButton>
      </Stack>
    </Box>
  );
};

export default StoreProfileSection;
