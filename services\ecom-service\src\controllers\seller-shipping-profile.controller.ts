import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {SellerShippingProfile} from '../models';
import {SellerShippingProfileDto} from '../models/dto/seller-shipping-profile.dto';
import {SellerShippingProfileRepository} from '../repositories';
import {STATUS_CODE, CONTENT_TYPE} from '@sourceloop/core';
import {service} from '@loopback/core';
import {SellerShippingService} from '../services/seller-shipping.service';

const basePath = '/seller-shipping-profiles';

export class SellerShippingProfileController {
  constructor(
    @repository(SellerShippingProfileRepository)
    public sellerShippingProfileRepository: SellerShippingProfileRepository,
    @service(SellerShippingService)
    private sellerShippingService: SellerShippingService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'SellerShippingProfile model instance with related entities',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(SellerShippingProfile)},
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerShippingProfileDto, {
            title: 'NewSellerShippingProfile',
          }),
        },
      },
    })
    sellerShippingProfileDto: SellerShippingProfileDto,
  ): Promise<SellerShippingProfile> {
    return this.sellerShippingService.createShippingProfile(
      sellerShippingProfileDto,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'SellerShippingProfile model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(SellerShippingProfile) where?: Where<SellerShippingProfile>,
  ): Promise<Count> {
    return this.sellerShippingProfileRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of SellerShippingProfile model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SellerShippingProfile, {
            includeRelations: true,
          }),
        },
      },
    },
  })
  async find(
    @param.filter(SellerShippingProfile) filter?: Filter<SellerShippingProfile>,
  ): Promise<SellerShippingProfile[]> {
    return this.sellerShippingProfileRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/seller/{sellerId}`)
  @response(STATUS_CODE.OK, {
    description: 'Array of SellerShippingProfile model instances for a seller',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SellerShippingProfile, {
            includeRelations: true,
          }),
        },
      },
    },
  })
  async findBySellerId(
    @param.path.string('sellerId') sellerId: string,
    @param.filter(SellerShippingProfile, {exclude: 'where'})
    filter?: FilterExcludingWhere<SellerShippingProfile>,
  ): Promise<SellerShippingProfile[]> {
    return this.sellerShippingService.getSellerShippingProfiles(
      sellerId,
      filter,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerShippingProfile PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerShippingProfileDto, {partial: true}),
        },
      },
    })
    sellerShippingProfileDto: Partial<SellerShippingProfileDto>,
  ): Promise<void> {
    return this.sellerShippingService.updateShippingProfile(
      id,
      sellerShippingProfileDto,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'SellerShippingProfile model instance with all relations',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(SellerShippingProfile, {
          includeRelations: true,
        }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
  ): Promise<SellerShippingProfile> {
    return this.sellerShippingService.getShippingProfileWithRelations(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerShippingProfile DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.sellerShippingProfileRepository.deleteById(id);
  }
}
