'use client';

// import { useFormik } from 'formik';
// import { convertToRaw, EditorState } from 'draft-js';
// import 'react-draft-wysiwyg/dist/react-draft-wysiwyg.css';
// import {
//   TextField,
//   Button,
//   Select,
//   MenuItem,
//   Box,
//   Typography,
//   Grid,
//   Autocomplete,
//   RadioGroup,
//   FormControlLabel,
//   Radio,
//   Tabs,
//   Tab
// } from '@mui/material';
// import { useRouter } from 'next/navigation';
// import { openSnackbar } from 'api/snackbar';
// import { SnackbarProps } from 'types/snackbar';
// import React, { useEffect, useState } from 'react';
// import MainCard from 'components/MainCard';
// import { InputLabel } from '@mui/material';
// import { CampaignSchema } from '../../../validations/campaign';
// import { FormControl } from '@mui/material';
// import { FormLabel } from '@mui/material';
// import dynamic from 'next/dynamic';
// import { EditorProps } from 'react-draft-wysiwyg';
// import { useCreateCampaignMutation } from 'redux/app/campaigns/campaignApiSlice';
// import { NotificationType } from 'enums/notification-type.enum';
// import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
// import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
// import { DemoContainer } from '@mui/x-date-pickers/internals/demo';
// import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
// import dayjs from 'dayjs';
// import { useGetGroupsQuery } from 'redux/app/campaigns/groupApiSlice';
// import draftToHtml from 'draftjs-to-html';

// const Editor = dynamic<EditorProps>(() => import('react-draft-wysiwyg').then((mod) => mod.Editor as unknown as React.FC<EditorProps>), {
//   ssr: false
// });

// export enum Campaign {
//   CAMPAIGNDETAILS = 'campaignDetails',
//   NOTIFICATIONCONTENT = 'notificationContent'
// }

// function CampaignForm() {
//   const router = useRouter();
//   const [createCampaign] = useCreateCampaignMutation();

//   const { data: AllGroups } = useGetGroupsQuery({
//     skip: 0,
//     order: ['createdOn DESC']
//   });

//   const [groups, setGroups] = useState<{ id: string; name: string }[]>([]);
//   const [selectedGroups, setSelectedGroups] = useState<{ id: string; name: string } | null>();
//   const [tab, setTab] = useState('campaignDetails');

//   const handleGroupSelection = (event: any, newValue: { id: string; name: string } | null) => {
//     if (newValue) {
//       setSelectedGroups(newValue);
//       formik.setFieldValue('groupId', newValue.id);
//     } else {
//       setSelectedGroups(null);
//     }
//   };

//   const handleChange = (event: React.SyntheticEvent, newValue: string) => {
//     setTab(newValue);
//   };

//   const handleCampaignSubmit = async (values: any) => {
//     try {
//       const campaignData = {
//         name: values.name,
//         type: values.notificationType,
//         subject: values.subject,
//         clickAction: values.clickAction,
//         sent: values.sent,
//         body: draftToHtml(convertToRaw(values.body.getCurrentContent())),
//         isDraft: values.isDraft,
//         groupId: values.groupId
//       };

//       await createCampaign(campaignData as any).unwrap();

//       openSnackbar({
//         open: true,
//         message: 'Campaign created successfully',
//         variant: 'alert',
//         alert: { color: 'success' }
//       } as SnackbarProps);

//       router.push('/marketing/campaigns');
//     } catch {}
//   };

//   const formik = useFormik({
//     initialValues: {
//       name: '',
//       notificationType: NotificationType.EMAIL,
//       subject: '',
//       clickAction: '',
//       body: EditorState.createEmpty(),
//       isDraft: false,
//       sent: dayjs(new Date()),
//       groupId: ''
//     },
//     validationSchema: CampaignSchema,
//     onSubmit: handleCampaignSubmit
//   });

//   useEffect(() => {
//     if (AllGroups?.length) {
//       const formattedGroups = AllGroups.map((group: any) => ({
//         id: group?.id || '',
//         name: group?.name || ''
//       }));

//       setGroups(formattedGroups);
//     }
//   }, [AllGroups]);

//   return (
//     <>
//       <MainCard>
//         <form onSubmit={formik.handleSubmit}>
//           <Box sx={{ borderBottom: 1, borderColor: 'divider', width: '100%' }}>
//             <Tabs value={tab} onChange={handleChange} variant="scrollable" scrollButtons="auto" aria-label="account profile tab">
//               <Tab label="Campaign Details" value={Campaign.CAMPAIGNDETAILS} iconPosition="start" />
//               <Tab label="Notification Content" value={Campaign.NOTIFICATIONCONTENT} iconPosition="start" />
//             </Tabs>
//           </Box>
//           <Grid container spacing={4} padding={2}>
//             {tab === Campaign.CAMPAIGNDETAILS && (
//               <>
//                 <Grid item xs={12} sm={6}>
//                   {' '}
//                   <InputLabel sx={{ mb: 1 }}>Campaign Name</InputLabel>
//                   <TextField
//                     placeholder="Enter campaign name"
//                     fullWidth
//                     name="name"
//                     onChange={formik.handleChange}
//                     onBlur={formik.handleBlur}
//                     error={formik.touched.name && Boolean(formik.errors.name)}
//                     helperText={Boolean(formik.errors.name) && formik.touched.name && formik.errors.name}
//                   />
//                 </Grid>
//                 <Grid item xs={12} sm={6}>
//                   <Typography variant="h6" sx={{ mb: 1 }}>
//                     Notification Type
//                   </Typography>
//                   <Select
//                     fullWidth
//                     displayEmpty
//                     name="notificationType"
//                     value={formik.values.notificationType}
//                     onChange={(e) => {
//                       formik.setFieldValue('notificationType', e.target.value);
//                     }}
//                     onBlur={formik.handleBlur}
//                     error={formik.touched.notificationType && Boolean(formik.errors.notificationType)}
//                   >
//                     <MenuItem disabled value="">
//                       Select Type
//                     </MenuItem>
//                     <MenuItem value={NotificationType.PUSH}>Push</MenuItem>
//                     <MenuItem value={NotificationType.EMAIL}>Email</MenuItem>
//                   </Select>
//                 </Grid>
//                 <Grid item xs={12} sm={6} sx={{ display: 'flex', alignItems: 'center' }}>
//                   <FormControl>
//                     <FormLabel id="demo-radio-buttons-group-label">Draft</FormLabel>
//                     <RadioGroup
//                       row
//                       name="isDraft"
//                       value={formik.values.isDraft ? 'true' : 'false'}
//                       onChange={(e) => formik.setFieldValue('isDraft', e.target.value === 'true')}
//                       aria-labelledby="demo-radio-buttons-group-label"
//                     >
//                       <FormControlLabel value="true" control={<Radio />} label="Yes" />
//                       <FormControlLabel value="false" control={<Radio />} label="No" />
//                     </RadioGroup>
//                   </FormControl>
//                 </Grid>
//                 {formik.values.isDraft && (
//                   <Grid item xs={12} sm={6} sx={{ display: 'flex', alignItems: 'center' }}>
//                     <LocalizationProvider dateAdapter={AdapterDayjs}>
//                       <DemoContainer components={['DateTimePicker']}>
//                         <DateTimePicker
//                           label="Send Date"
//                           name="sent"
//                           value={formik.values.sent}
//                           onChange={(newValue) => formik.setFieldValue('sent', newValue)}
//                         />
//                       </DemoContainer>
//                     </LocalizationProvider>
//                   </Grid>
//                 )}
//                 <Grid item xs={12} sm={6}>
//                   <Typography variant="h6">Topic/Group</Typography>
//                   <Autocomplete
//                     options={groups.map((group) => ({
//                       id: group.id,
//                       name: group.name
//                     }))}
//                     getOptionLabel={(option) => option.name}
//                     value={selectedGroups}
//                     onChange={handleGroupSelection}
//                     renderInput={(params) => <TextField {...params} placeholder="Select groups" variant="outlined" margin="dense" />}
//                   />
//                 </Grid>
//               </>
//             )}
//             {tab === Campaign.NOTIFICATIONCONTENT && formik.values.notificationType === NotificationType.EMAIL && (
//               <>
//                 <Grid item xs={12} sm={6}>
//                   {' '}
//                   <InputLabel sx={{ mb: 1 }}>Subject</InputLabel>
//                   <TextField
//                     placeholder="Enter Subject"
//                     fullWidth
//                     name="subject"
//                     onChange={formik.handleChange}
//                     onBlur={formik.handleBlur}
//                     error={formik.touched.subject && Boolean(formik.errors.subject)}
//                     helperText={Boolean(formik.errors.subject) && formik.touched.subject && formik.errors.subject}
//                   />
//                 </Grid>
//                 <Grid item xs={12}>
//                   <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
//                     Message
//                   </Typography>
//                   <Box sx={{ p: 2, border: '1px solid #ccc', borderRadius: 2, minHeight: 200 }}>
//                     <Editor
//                       editorState={formik.values.body}
//                       onEditorStateChange={(editorState) => formik.setFieldValue('body', editorState)}
//                       wrapperClassName="wrapper-class"
//                       editorClassName="editor-class"
//                       toolbarClassName="toolbar-class"
//                     />
//                   </Box>
//                 </Grid>
//               </>
//             )}
//             {tab === Campaign.NOTIFICATIONCONTENT && formik.values.notificationType === NotificationType.PUSH && (
//               <>
//                 <Grid item xs={12}>
//                   <Typography variant="h6" sx={{ mt: 2, mb: 1 }}>
//                     Message
//                   </Typography>
//                   <Box sx={{ p: 2, border: '1px solid #ccc', borderRadius: 2, minHeight: 200 }}>
//                     <Editor
//                       editorState={formik.values.body}
//                       onEditorStateChange={(editorState) => formik.setFieldValue('body', editorState)}
//                       wrapperClassName="wrapper-class"
//                       editorClassName="editor-class"
//                       toolbarClassName="toolbar-class"
//                     />
//                   </Box>
//                 </Grid>
//                 <Grid item xs={12} sm={6}>
//                   {' '}
//                   <InputLabel sx={{ mb: 1 }}>Click Action URL</InputLabel>
//                   <TextField
//                     placeholder="https://yourwebsite.com/page"
//                     fullWidth
//                     name="clickAction"
//                     onChange={formik.handleChange}
//                     onBlur={formik.handleBlur}
//                     error={formik.touched.clickAction && Boolean(formik.errors.clickAction)}
//                     helperText={Boolean(formik.errors.clickAction) && formik.touched.clickAction && formik.errors.clickAction}
//                   />
//                 </Grid>
//               </>
//             )}
//           </Grid>
//           <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
//             {tab === Campaign.CAMPAIGNDETAILS && (
//               <Button variant="contained" color="primary" onClick={() => setTab(Campaign.NOTIFICATIONCONTENT)}>
//                 Next
//               </Button>
//             )}
//             {tab === Campaign.NOTIFICATIONCONTENT && (
//               <Button type="submit" variant="contained" color="primary">
//                 Submit
//               </Button>
//             )}
//           </Box>
//         </form>
//       </MainCard>
//     </>
//   );
// }

const CampaignForm = () => {
  return <>campaign Form</>;
};

export default CampaignForm;
