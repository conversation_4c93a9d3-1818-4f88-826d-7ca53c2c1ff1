import React from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import AddressCard from './AddressCard';
import {AddressDto} from '../../../../types/customerApi';
import customColors from '../../../../theme/customColors';

interface Props {
  visible: boolean;
  addresses: AddressDto[];
  selectedId: string | undefined;
  onClose: () => void;
  onSelect: (id: string) => void;
  onEdit: (address: AddressDto) => void;
  title?: string;
}

const AddressSelectionModal: React.FC<Props> = ({
  visible,
  addresses,
  selectedId,
  onClose,
  onSelect,
  onEdit,
  title = 'Choose Address',
}) => {
  return (
    <Modal visible={visible} animationType="slide" transparent>
      <View style={styles.overlay}>
        <View style={styles.modalContent}>
          <Text style={styles.title}>{title}</Text>
          <ScrollView contentContainerStyle={styles.list}>
            {addresses.map(addr => (
              <AddressCard
                key={addr.id}
                address={addr}
                isSelected={selectedId === addr.id}
                onSelect={onSelect}
                onEdit={onEdit}
              />
            ))}
          </ScrollView>

          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>Done</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

export default AddressSelectionModal;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: '#00000088',
    justifyContent: 'center',
    padding: 16,
  },
  modalContent: {
    backgroundColor: customColors.white,
    borderRadius: 12,
    padding: 16,
    maxHeight: '80%',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
    color: customColors.textBlack,
  },
  list: {
    paddingBottom: 16,
  },
  closeButton: {
    marginTop: 10,
    backgroundColor: customColors.primary,
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
  },
  closeButtonText: {
    color: customColors.white,
    fontWeight: 'bold',
  },
});
