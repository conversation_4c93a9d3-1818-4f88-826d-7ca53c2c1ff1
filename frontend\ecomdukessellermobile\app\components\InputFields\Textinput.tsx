import {StyleSheet, View, ViewStyle} from 'react-native';
import React from 'react';
import {TextInput, Text, HelperText} from 'react-native-paper';
import {colors} from '../../theme/colors';
import {FONTS} from '../../theme/fonts';
import customColors from '../../theme/customColors';
type Props = {
  title?: string;
  touched?: boolean;
  errors?: string;
  isHelperTextVisible?: boolean;
  autoCapitalize?: string;
  containerStyle?: ViewStyle;
  textInputStyle?: ViewStyle;
} & React.ComponentProps<typeof TextInput>;

const CustomTextInput = ({
  title = '',
  touched = false,
  errors = '',
  isHelperTextVisible = true,
  autoCapitalize,
  containerStyle,
  textInputStyle,
  ...rest
}: Props) => {
  return (
    <View style={[containerStyle]}>
      {title && (
        <Text variant="bodyMedium" style={styles.title}>
          {title ?? ''}
        </Text>
      )}
      <TextInput
        textContentType="oneTimeCode"
        mode="outlined"
        autoCapitalize={autoCapitalize || 'none'}
        activeOutlineColor={customColors.appBlue}
        outlineColor={colors.gray.medium}
        outlineStyle={{
          borderWidth: 1.5,
        }}
        underlineColor="transparent"
        style={[styles.textInput, textInputStyle]}
        {...rest}
        error={touched && errors?.length > 0}
      />
      {isHelperTextVisible && (
        <HelperText
          numberOfLines={1}
          style={styles.helperText}
          type="error"
          padding="none"
          visible={touched && errors?.length > 0}>
          {errors}
        </HelperText>
      )}
    </View>
  );
};

export default CustomTextInput;

const styles = StyleSheet.create({
  helperText: {
    marginLeft: 20,
  },
  textInput: {
    fontFamily: FONTS.regular,
    fontSize: 14,
    height: 40,
  },
  title: {
    marginBottom: 5,
    marginLeft: 20,
    color: colors.gray.text,
  },
});
