import {injectable, BindingScope, inject} from '@loopback/core';
import {RestBindings, Request} from '@loopback/rest';
import {
  PaymentEntityPaymentMethod,
  PaymentWebhook,
  PaymentWebhookDataEntity,
} from 'cashfree-pg';
import {WebhookEventType} from '../types';
import {
  IAuthUserWithPermissions,
  ILogger,
  LOGGER,
  ModifiedRestService,
  restService,
} from '@sourceloop/core';
import {
  BillingAddress,
  Cart,
  CreateZohoInvoiceRequestDto,
  Customer,
  DukeCoin,
  Order,
  OrderLineItemWithRelations,
  Payment,
  ZohoInvoiceItem,
} from '../models';
import {OrderProxyType, PaymentProxyType} from '../datasources/configs';
import {CartStatus, OrderStatus, PermissionKeys} from '@local/core';
import {
  AuthCodeBindings,
  CodeWriterFn,
} from '@sourceloop/authentication-service';
import * as jwt from 'jsonwebtoken';
import {systemUser} from '../constants';
import {Configuration} from '../models/ecom-service/configuration.model';
import {DukeCoinProxyType} from '../datasources/configs/duke-coin-proxy.config';

@injectable({scope: BindingScope.TRANSIENT})
export class CashfreeWebhookService {
  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @restService(Order)
    private orderProxy: OrderProxyType,
    @restService(Cart)
    private cartProxy: ModifiedRestService<Cart>,
    @restService(Payment)
    private paymentProxy: PaymentProxyType,
    @inject(LOGGER.LOGGER_INJECT)
    public logger: ILogger,
    @inject(AuthCodeBindings.CODEWRITER_PROVIDER)
    private readonly codeWriter: CodeWriterFn,
    @restService(Customer)
    public customerProxy: ModifiedRestService<Customer>,
    @restService(Configuration)
    private configProxy: ModifiedRestService<Configuration>,
    @restService(DukeCoin)
    private readonly dukeProxy: DukeCoinProxyType,
  ) {}

  async handlePaymentWebhook(event: PaymentWebhook): Promise<void> {
    const raw = event as unknown as Buffer;
    const json: PaymentWebhook = JSON.parse(raw.toString('utf-8'));

    console.log(
      '🚀 ~ CashfreeWebhookService ~ handlePaymentWebhook ~ event:',
      json,
    );
    this.logger.info('Payment webhook invoked with event type', json.type);
    switch (json.type) {
      case WebhookEventType.PAYMENT_SUCCESS_WEBHOOK:
        return this.handlePaymentSuccess(json.data);
      case WebhookEventType.PAYMENT_FAILED_WEBHOOK:
        return this.handlePaymentFailed(json.data);
      default:
        break;
    }
  }

  private async generateInternalToken() {
    const codePayload: IAuthUserWithPermissions = {
      ...systemUser,
      permissions: [
        PermissionKeys.CreateNotification,
        PermissionKeys.CreateNotificationNum,
        PermissionKeys.CreatePayment,
        PermissionKeys.ViewPayment,
        PermissionKeys.ViewOrder,
        PermissionKeys.UpdateOrder,
        PermissionKeys.CreateOrder,
        PermissionKeys.UpdateCart,
        PermissionKeys.CreateCart,
        PermissionKeys.ViewCustomer,
        PermissionKeys.UpdateCustomer,
      ],
    };

    const token = await this.codeWriter(
      jwt.sign(codePayload, process.env.JWT_SECRET as string, {
        expiresIn: '1h',
        audience: '',
        issuer: process.env.JWT_ISSUER,
        algorithm: 'HS256',
      }),
    );
    return `Bearer ${token}`;
  }

  private getPaymentMethodName(
    paymentMethod?: PaymentEntityPaymentMethod,
  ): string | undefined {
    if (!paymentMethod || typeof paymentMethod !== 'object') return undefined;
    const keys = Object.keys(paymentMethod);
    return keys.length > 0 ? keys[0] : undefined;
  }

  private async handlePaymentSuccess(data?: PaymentWebhookDataEntity) {
    const orderId = data?.order?.order_id;

    if (!orderId || !data?.payment) {
      return;
    }
    this.logger.info('Payment Success Event Triggered for order', orderId);
    const token = await this.generateInternalToken();

    await this.orderProxy.updateById(
      orderId,
      {status: OrderStatus.Paid},
      token,
    );

    const payment = await this.paymentProxy.create(
      new Payment({
        orderId,
        paymentMethod: this.getPaymentMethodName(data.payment?.payment_method),
        paymentStatus: data.payment?.payment_status,
        transactionId: data.payment?.cf_payment_id?.toString(),
        amount: data.payment?.payment_amount,
        currency: data.payment?.payment_currency,
        paymentDate: data.payment?.payment_time,
      }),
      token,
    );
    const order = await this.orderProxy.findById(
      orderId,
      {
        fields: {cartId: true},
        include: [
          {
            relation: 'orderLineItems',
            scope: {
              include: [{relation: 'productVariant'}],
            },
          },
        ],
      },
      token,
    );
    await this.cartProxy.updateById(
      order.cartId,
      {
        status: CartStatus.Completed,
      },
      token,
    );
    const customer = await this.customerProxy.findById(
      data.customer_details?.customer_id ?? '',
      {fields: {zohoContactId: true}},
      token,
    );
    const invoicePayload: CreateZohoInvoiceRequestDto =
      new CreateZohoInvoiceRequestDto({
        payment,
        customerName: data.customer_details?.customer_name,
        customerEmail: data.customer_details?.customer_email,
        customerPhone: data.customer_details?.customer_phone,
        zohoContactId: customer?.zohoContactId ?? '',
        billingAddress: {
          address: '123 Street Name',
          city: 'Mumbai',
          state: 'Maharashtra',
          zip: '400001',
          country: 'India',
        } as BillingAddress,
        items:
          (order?.orderLineItems?.map(
            (lineItem: OrderLineItemWithRelations) => ({
              itemId: lineItem.productVariant?.zohoItemId ?? '',
              name: lineItem.productVariant?.name ?? 'Product',
              quantity: lineItem.quantity,
              price: parseFloat(
                (lineItem.unitPrice as unknown as string) ?? '0',
              ),
              description: 'E-commerce order item',
            }),
          ) as ZohoInvoiceItem[]) ?? [],
      });
    const invoiceRes = await this.paymentProxy.createInvoice(
      invoicePayload,
      token,
    );
    const invoiceId = invoiceRes.invoiceId;

    if (invoiceId) {
      await this.orderProxy.updateById(
        orderId,
        {invoiceId, invoiceUrl: invoiceRes.invoiceUrl},
        token,
      );
      this.logger.info(
        `Invoice ${invoiceId} created and linked to order ${orderId}`,
      );
      if (
        invoiceRes.contactId &&
        invoiceRes.contactId !== customer.zohoContactId
      ) {
        await this.customerProxy.updateById(
          customer.id,
          {
            zohoContactId: invoiceRes.contactId,
          },
          token,
        );
        this.logger.info(
          `Updated customer ${customer.id} with Zoho contact ID ${invoiceRes.contactId}`,
        );
      }
    } else {
      this.logger.warn(`Invoice creation failed for order ${orderId}`);
    }
  }

  private async handlePaymentFailed(data?: PaymentWebhookDataEntity) {}
}
