import {RestOperationTemplate} from '@sourceloop/core';
import {Groups} from '../../models';
import {Filter} from '@loopback/repository';

export type GroupProxyType = {
  findGroups(token: string, filter?: Filter<Groups>): Promise<Groups[]>;
};

export const GroupProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'GET',
      url: '/groups',
      headers: {
        accept: 'application/json',
        Authorization: '{token}',
      },
      query: {
        filter: '{filter}',
      },
    },
    functions: {
      findGroups: ['token', 'filter'],
    },
  },
];
