'use client';

import {useEffect} from 'react';
import {Box, Grid, Typography} from '@mui/material';
import {useLazyGetProductVariantsQuery} from 'redux/ecom/ecomApiSlice';
import ProductCard from 'views/products/ProductCard';
import {ReviewStatus} from 'types/review';
import {User} from 'types/user-profile';

interface ViewAllGiftsProps {
  isLoggedIn?: boolean;
  user?: User;
}

export default function ViewAllGifts({isLoggedIn, user}: ViewAllGiftsProps) {
  const [fetchGiftVariants, {data = [], isLoading}] =
    useLazyGetProductVariantsQuery();

  useEffect(() => {
    fetchGiftVariants({
      include: [
        {
          relation: 'featuredAsset',
          scope: {
            fields: {preview: true, id: true},
          },
        },
        {
          relation: 'product',
          required: true,
          scope: {
            fields: {description: true, id: true},
            where: {isGiftWrapAvailable: true},
          },
        },
        {
          relation: 'productVariantPrice',
          scope: {
            fields: {
              price: true,
              mrp: true,
              currencyCode: true,
            },
          },
        },
        ...(isLoggedIn
          ? [
              {
                relation: 'wishlist',
                scope: {
                  where: {
                    deleted: false,
                    customerId: user?.profileId,
                  },
                  fields: {id: true},
                },
              },
            ]
          : []),
        {
          relation: 'reviews',
          scope: {
            fields: {
              rating: true,
            },
            where: {
              status: ReviewStatus.APPROVED,
            },
          },
        },
      ],
      fields: {
        name: true,
        id: true,
        featuredAssetId: true,
        productId: true,
      },
    });
  }, [fetchGiftVariants]);

  return (
    <Box p={3}>
      <Typography variant="h4" mb={2}>
        Gift Products
      </Typography>
      <Grid container spacing={2}>
        {isLoading ? (
          <Typography>Loading...</Typography>
        ) : (
          data.map(variant => (
            <Grid item xs={12} sm={6} md={3} key={variant.id}>
              <ProductCard productVariant={variant} />
            </Grid>
          ))
        )}
      </Grid>
    </Box>
  );
}
