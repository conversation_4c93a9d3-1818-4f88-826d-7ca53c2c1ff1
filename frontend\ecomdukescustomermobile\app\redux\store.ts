import {configureStore} from '@reduxjs/toolkit';
import authReducer from './auth/authSlice';
import apiMonitorReducer from './apiMonitor/apiMonitorSlice';
import apiRetryReducer from './retry/retrySlice';
import {apiSlice} from './apiSlice';
import {TypedUseSelectorHook, useSelector} from 'react-redux';
import storageMiddleware from './middlewares/storeMiddleware';
import apiMonitorMiddleware from './middlewares/apiMonitorMiddleware';
import cartReducer from './cart/cartSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    cart: cartReducer,
    apiMonitor: apiMonitorReducer,
    apiRetry: apiRetryReducer,
    [apiSlice.reducerPath]: apiSlice.reducer,
  },
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware().concat(
      storageMiddleware,
      apiMonitorMiddleware,
      apiSlice.middleware,
    ),
  devTools: true,
});

export type RootState = ReturnType<typeof store.getState>;

export type AppDispatch = typeof store.dispatch;
export const useTypedSelector: TypedUseSelectorHook<RootState> = useSelector;
