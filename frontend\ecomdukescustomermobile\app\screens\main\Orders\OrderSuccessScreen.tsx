import React from 'react';
import {
  ScrollView,
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
} from 'react-native';
import {ActivityIndicator, Icon} from 'react-native-paper';
import {Images} from '../../../assets/images';
import customColors from '../../../theme/customColors';
import {colors} from '../../../theme/colors';
import {RouteProp, useRoute} from '@react-navigation/native';
import {fieldsExcludeMetaFields, IFilter} from '../../../types/api';
import {useGetOrderByIdQuery} from '../../../redux/order/orderApiSlice';
import {useGetUserQuery} from '../../../redux/auth/authApiSlice';
import {OnboardStackParamList} from '../../../navigations/types';

const OrderSuccessScreen = () => {
  const {data: user} = useGetUserQuery();
  const currencyCodeSymbolMap: Map<string, string> = new Map([
    ['INR', '₹'],
    ['USD', '$'],
  ]);

  type OrderSuccessRouteProp = RouteProp<OnboardStackParamList, 'orderSuccess'>;
  const route = useRoute<OrderSuccessRouteProp>();
  const orderId = route.params?.orderId;
  const filter: IFilter = {
    include: [
      {
        relation: 'orderLineItems',
        scope: {
          fields: fieldsExcludeMetaFields,
          include: [
            {
              relation: 'productVariant',
              scope: {
                fields: fieldsExcludeMetaFields,
                include: [
                  {
                    relation: 'featuredAsset',
                    scope: {
                      fields: fieldsExcludeMetaFields,
                    },
                  },
                ],
              },
            },
          ],
        },
      },
      {
        relation: 'customer',
      },
      {
        relation: 'promoCode',
      },
      {
        relation: 'shippingAddress',
      },
      {
        relation: 'billingAddress',
      },
    ],
  };

  const {data: order, isLoading: orderLoading} = useGetOrderByIdQuery({
    id: orderId ?? '',
    filter,
  });
  const subTotal =
    (Number(order?.totalAmount) || 0) - (Number(order?.discount) || 0);
  const totalMRP = order?.orderLineItems.reduce(
    (sum, item) => sum + item.unitPrice * item.quantity,
    0,
  );
  const deliveryCharge = 50;
  const promoCodeValue = order?.promoCode.value;
  const totalPayable = (totalMRP ?? 0) + deliveryCharge - (promoCodeValue ?? 0);
  const summaryData = [
    ['Item(s) Total [MRP]', totalMRP ?? 0],
    ['Shop Discount', 0],
    ['Sub Total', subTotal ?? 0],
    ['Special Discount', 0],
    [
      'Promo Code',
      promoCodeValue
        ? `- ${currencyCodeSymbolMap.get('INR')}${promoCodeValue}`
        : '- ₹0',
    ],
    ['Gift Wrap', 0],
    ['Delivery Charge', deliveryCharge],
  ];
  const date = new Date(order?.createdOn || '');
  return (
    <View style={styles.mainContainer}>
      {orderLoading && <ActivityIndicator />}
      <ScrollView contentContainerStyle={styles.container}>
        <View style={styles.greetingCard}>
          <Text style={styles.title}>Thank You! </Text>
          <Text style={styles.subtitle}>
            Your order <Text style={styles.bold}>#{order?.id}</Text> has been
            placed!
          </Text>
          <Text style={styles.message}>
            We sent an email to{' '}
            <Text style={styles.boldBlack}>{user?.email}</Text> with your order
            confirmation and receipt. If the email hasn't arrived within two
            minutes, please check your spam folder to
          </Text>
          <View style={styles.infoRow}>
            <View style={styles.leftSection}>
              <Icon
                source="clock-outline"
                size={30}
                color={customColors.primaryContainer}
              />
              <View style={styles.textContainer}>
                <Text style={styles.labelText}>Time Placed</Text>
                <Text style={styles.valueText}>{date.toLocaleString()}</Text>
              </View>
            </View>

            <View style={styles.rightSection}>
              <Icon
                source="printer"
                size={30}
                color={customColors.primaryContainer}
              />
              <Text style={styles.printText}>Print</Text>
            </View>
          </View>

          <Image
            source={Images.logo}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>
        {/* <View style={styles.card}>
          <View style={styles.leftSection}>
            <Icon
              source="map-marker"
              size={48}
              color={customColors.primaryContainer}
            />
            <Text style={styles.label}>Shipping</Text>
          </View>
          <View style={styles.rightSection}>
            <Text style={styles.text}>{order?.shippingAddress?.name}</Text>
            <Text style={styles.text}>
              {order?.shippingAddress?.addressLine1},{' '}
              {order?.shippingAddress?.city}, {order?.shippingAddress?.state}{' '}
              {order?.shippingAddress?.zipCode},{' '}
              {order?.shippingAddress?.country}
            </Text>
            <Text style={styles.text}>
              {order?.shippingAddress?.phoneNumber}
            </Text>
          </View>
        </View>

        <View style={styles.card}>
          <View style={styles.leftSection}>
            <Icon
              source="credit-card"
              size={48}
              color={customColors.primaryContainer}
            />
            <Text style={styles.label}>Billing Details</Text>
          </View>
          <View style={styles.rightSection}>
            <Text style={styles.text}>{order?.billingAddress?.name}</Text>
            <Text style={styles.text}>
              {order?.billingAddress?.addressLine1},{' '}
              {order?.billingAddress?.city}, {order?.billingAddress?.state}{' '}
              {order?.billingAddress?.zipCode}, {order?.billingAddress?.country}
            </Text>
            <Text style={styles.text}>
              {order?.billingAddress?.phoneNumber}
            </Text>
          </View>
        </View>

        <View style={styles.card}>
          <View style={styles.leftSection}>
            <Icon
              source="van-passenger"
              size={48}
              color={customColors.primaryContainer}
            />
            <Text style={styles.label}>Shipping Method</Text>
          </View>
          <View style={styles.rightSection}>
            <Text style={styles.text}>
              {user?.firstName}
              {''}
              {user?.lastName},
            </Text>
            <Text style={styles.text}>Sacramento, CA 94203 US</Text>
            <Text style={styles.text}>{user?.phone}</Text>
          </View>
        </View> */}
        <View style={styles.card1}>
          <View style={styles.row}>
            <Icon
              source="map-marker"
              size={28}
              color={customColors.primaryContainer}
            />
            <Text style={styles.label1}>Shipping</Text>
          </View>
          <View style={styles.info}>
            <Text style={styles.text1}>{order?.shippingAddress?.name}</Text>
            <Text style={styles.text1}>
              {order?.shippingAddress?.addressLine1},{' '}
              {order?.shippingAddress?.city}, {order?.shippingAddress?.state}{' '}
              {order?.shippingAddress?.zipCode},{' '}
              {order?.shippingAddress?.country}
            </Text>
            <Text style={styles.text1}>
              {order?.shippingAddress?.phoneNumber}
            </Text>
          </View>
        </View>

        <View style={styles.card1}>
          <View style={styles.row}>
            <Icon
              source="credit-card"
              size={28}
              color={customColors.primaryContainer}
            />
            <Text style={styles.label1}>Billing Details</Text>
          </View>
          <View style={styles.info}>
            <Text style={styles.text1}>{order?.billingAddress?.name}</Text>
            <Text style={styles.text1}>
              {order?.billingAddress?.addressLine1},{' '}
              {order?.billingAddress?.city}, {order?.billingAddress?.state}{' '}
              {order?.billingAddress?.zipCode}, {order?.billingAddress?.country}
            </Text>
            <Text style={styles.text1}>
              {order?.billingAddress?.phoneNumber}
            </Text>
          </View>
        </View>

        <View style={styles.card1}>
          <View style={styles.row}>
            <Icon
              source="truck-delivery-outline"
              size={28}
              color={customColors.primaryContainer}
            />
            <Text style={styles.label1}>Shipping Method</Text>
          </View>
          <View style={styles.info}>
            <Text style={styles.text1}>Preferred Method: U.S. Standard</Text>
            <Text style={styles.text1}>(normally 4-5 business days)</Text>
          </View>
        </View>

        <View style={styles.summaryCard}>
          <Text style={styles.sectionHeader}>Order List</Text>
          <View style={styles.divider} />

          <FlatList
            data={order?.orderLineItems || []}
            keyExtractor={item => item.id.toString()}
            renderItem={({item}) => {
              const currencySymbol = currencyCodeSymbolMap.get('INR') || '';
              return (
                <View style={styles.orderItem}>
                  <Image
                    source={{
                      uri: item.productVariant?.featuredAsset.previewUrl,
                    }}
                    style={styles.productImage}
                    resizeMode="cover"
                  />
                  <View style={styles.itemDetails}>
                    <Text style={styles.itemName} numberOfLines={2}>
                      {item.productVariant?.name}
                    </Text>

                    <Text style={styles.itemMeta}>
                      #{item.productVariantId}
                    </Text>
                    <Text style={styles.qty}> Qty: {item.quantity}</Text>
                  </View>
                  <View style={styles.itemPriceContainer}>
                    <Text style={styles.itemPrice}>
                      {currencySymbol}
                      {item.totalPrice}
                    </Text>
                  </View>
                </View>
              );
            }}
            scrollEnabled={false}
          />

          <View style={styles.summaryBlock}>
            <Text style={styles.sectionHeader}>Order Summary</Text>
            <View style={styles.divider} />

            {summaryData.map(([label, value], i) => (
              <View key={i} style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>{label}</Text>
                <Text style={styles.summaryValue}>
                  {typeof value === 'number'
                    ? `${currencyCodeSymbolMap.get('INR') || ''} ${value}`
                    : value}
                </Text>
              </View>
            ))}
            <View style={styles.totalRow}>
              <Text style={styles.totalLabel}>Total Payable</Text>
              <Text style={styles.totalValue}>
                {currencyCodeSymbolMap.get('INR') || ''}
                {totalPayable ?? 0}
              </Text>
            </View>

            <View style={styles.estimatedDelivery}>
              <Text style={styles.estimatedLabel}>Estimated delivery</Text>
              <Text style={styles.estimatedValue}>25 May - 08 Jun</Text>
            </View>
          </View>

          <View style={styles.rateButton}>
            <Text style={styles.rateButtonText}>Rate and review App</Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: colors.gray.elevation,
    padding: 15,
  },
  container: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: colors.gray.elevation,
  },
  greetingCard: {
    backgroundColor: customColors.white,
    borderRadius: 10,
    marginBottom: 20,
    padding: 30,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },

  title: {
    fontSize: 30,
    fontWeight: 'bold',
    textAlign: 'center',
    marginVertical: 10,
    marginBottom: 30,
    color: colors.tertiary,
  },
  subtitle: {
    textAlign: 'center',
    fontSize: 13,
    marginVertical: 10,
    color: colors.tertiary,
  },

  message: {
    textAlign: 'center',
    fontSize: 14,
    color: colors.gray.dark,
    marginBottom: 20,
  },
  boldBlack: {
    fontWeight: 'bold',
    color: customColors.textBlack,
  },
  bold: {
    fontWeight: 'bold',
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },

  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },

  textContainer: {
    marginLeft: 8,
  },

  labelText: {
    fontSize: 14,
    fontWeight: '600',
    color: customColors.textBlack,
  },

  valueText: {
    fontSize: 12,
    color: colors.gray.dark,
  },

  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  printText: {
    marginLeft: 6,
    fontSize: 14,
    color: customColors.textBlack,
  },
  infoItem: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  infoText: {
    textAlign: 'center',
    fontSize: 12,
  },
  logo: {
    width: 500,
    height: 80,
    alignSelf: 'center',
    marginVertical: 20,
  },

  cardContent: {
    marginLeft: 10,
  },
  cardTitle: {
    fontWeight: 'bold',
    marginBottom: 5,
    color: customColors.textBlack,
  },

  card: {
    flexDirection: 'row',
    backgroundColor: colors.elevation.level1,
    borderRadius: 12,
    padding: 16,
    alignItems: 'flex-start',
    marginBottom: 12,
  },

  label: {
    marginTop: 6,
    fontWeight: 'bold',
    color: colors.tertiary,
    textAlign: 'center',
  },

  text: {
    color: colors.gray.dark,
    fontSize: 14,
    marginBottom: 2,
  },
  summaryCard: {
    backgroundColor: customColors.white,
    borderRadius: 10,
    padding: 16,
    marginBottom: 20,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionHeader: {
    fontSize: 13,
    fontWeight: 'bold',
    color: colors.tertiary,
    marginBottom: 8,
  },
  divider: {
    height: 1,
    backgroundColor: colors.gray.medium,
    marginBottom: 12,
  },
  orderItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 12,
    borderBottomWidth: 1,
    borderColor: colors.gray.medium,
    backgroundColor: customColors.white,
  },
  productImage: {
    width: 70,
    height: 70,
    borderRadius: 8,
    marginRight: 12,
  },
  itemDetails: {
    flex: 1,
    justifyContent: 'space-between',
  },
  itemName: {
    fontSize: 14,
    fontWeight: '600',
    color: customColors.textBlack,
    marginBottom: 4,
  },
  itemDesc: {
    fontSize: 12,
    color: colors.gray.dark,
    marginBottom: 4,
  },
  itemMeta: {
    fontSize: 12,
    color: colors.gray.text,
    marginTop: 8,
  },
  qty: {
    fontSize: 12,
    marginTop: 6,
    color: colors.gray.text,
  },
  itemPriceContainer: {
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    marginLeft: 8,
  },
  itemPrice: {
    fontSize: 14,
    fontWeight: '600',
    color: customColors.textBlack,
  },
  summaryBlock: {
    backgroundColor: colors.background,
    padding: 12,
    borderRadius: 8,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  summaryLabel: {
    fontSize: 13,
    color: customColors.textBlack,
  },
  summaryValue: {
    fontSize: 13,
    fontWeight: '600',
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: colors.gray.medium,
  },
  totalLabel: {
    fontWeight: 'bold',
    fontSize: 13,
  },
  totalValue: {
    fontWeight: 'bold',
    fontSize: 13,
  },
  estimatedDelivery: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
  },
  estimatedLabel: {
    color: customColors.textBlack,
    fontSize: 13,
  },
  estimatedValue: {
    fontWeight: 'bold',
    fontSize: 13,
  },
  rateButton: {
    backgroundColor: colors.tertiary,
    marginTop: 20,
    borderRadius: 30,
    paddingVertical: 12,
    alignItems: 'center',
  },
  rateButtonText: {
    color: customColors.white,
    fontWeight: 'bold',
    fontSize: 15,
  },
  card1: {
    backgroundColor: colors.gray.card,
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  label1: {
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
    color: customColors.textBlack,
  },
  info: {
    marginLeft: 36,
  },
  text1: {
    fontSize: 14,
    color: colors.gray.dark,
    marginBottom: 4,
  },
});

export default OrderSuccessScreen;
