'use client';

import {
  Avatar,
  Skeleton,
  Stack,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
} from '@mui/material';
import IconButton from 'components/@extended/IconButton';
import {HambergerMenu} from 'iconsax-react';
import {User} from 'types/seller';

interface Props {
  loading: boolean;
  user?: Partial<User> | null;
  preSignedPhotoUrl?: string;
  lastUpdated?: string | null;
  handleDrawerOpen: () => void;
}

export default function ChatHeader({
  loading,
  user,
  preSignedPhotoUrl,
  lastUpdated,
  handleDrawerOpen,
}: Props) {
  const rawName =
    user?.firstName || user?.lastName
      ? `${user?.firstName ?? ''} ${user?.lastName ?? ''}`.trim()
      : 'Seller';

  const displayName =
    rawName.length > 12 ? `${rawName.slice(0, 12)}...` : rawName;

  return (
    <Stack direction="row" alignItems="center" spacing={1}>
      <IconButton onClick={handleDrawerOpen} color="secondary" size="large">
        <HambergerMenu />
      </IconButton>

      {loading || !user ? (
        <List disablePadding>
          <ListItem disablePadding disableGutters>
            <ListItemAvatar>
              <Skeleton variant="circular" width={40} height={40} />
            </ListItemAvatar>
            <ListItemText
              sx={{my: 0}}
              primary={<Skeleton animation="wave" height={24} width={80} />}
              secondary={<Skeleton animation="wave" height={16} width={100} />}
            />
          </ListItem>
        </List>
      ) : (
        <Stack direction="row" spacing={1} alignItems="center">
          <Avatar
            alt={displayName}
            src={preSignedPhotoUrl || undefined}
            sx={{width: 40, height: 40}}
          >
            {!preSignedPhotoUrl &&
              (() => {
                const initials = `${user.firstName?.[0] ?? ''}${user.lastName?.[0] ?? ''}`;
                return initials.toUpperCase();
              })()}
          </Avatar>
          <Stack>
            <Typography variant="subtitle1" noWrap>
              {displayName}
            </Typography>
          </Stack>
        </Stack>
      )}
    </Stack>
  );
}
