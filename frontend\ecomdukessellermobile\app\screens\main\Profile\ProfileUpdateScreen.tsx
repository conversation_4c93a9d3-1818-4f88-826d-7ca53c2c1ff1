import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ImageBackground,
  SafeAreaView,
} from 'react-native';
import {useFormik} from 'formik';
import CustomTextInput from '../../../components/InputFields/Textinput';
import CustomButton from '../../../components/CustomButton/ContainedButton';
import {Gender, ProfileDto} from '../../../types/profile';
import {profileUpdateSchema} from '../../../validations';
import {
  useGetUserQuery,
  useLazyGetUserQuery,
  useUpdateUserMutation,
} from '../../../redux/auth/authApiSlice';
import Toast from 'react-native-toast-message';
import CustomDropDown from '../../../components/InputFields/Dropdown';

import {useDispatch} from 'react-redux';
import {setUserDetails} from '../../../redux/auth/authSlice';
import {User} from '../../../redux/auth/user.model';
import {HelperText, Icon, Text} from 'react-native-paper';
import {colors} from '../../../theme/colors';
import {FONTS} from '../../../theme/fonts';
import customColors from '../../../theme/customColors';
import styleConstants from '../../../theme/styleConstants';
import {openPicker} from 'react-native-image-crop-picker';
import DatePicker from 'react-native-date-picker';
import moment from 'moment';
import PhoneInput from 'react-native-phone-number-input';
import type {CountryCode} from 'react-native-country-picker-modal';
import {parsePhoneNumberWithError} from 'libphonenumber-js';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';
type ImageValue =
  | string
  | {
      path: string;
      mime: string;
      filename: string;
    }
  | undefined
  | null;

const ProfileUpdateScreen = ({navigation}: any) => {
  const [showDatePicker, setShowDatePicker] = useState(false);
  const {data: user, refetch} = useGetUserQuery();
  const [getUserDetails] = useLazyGetUserQuery();
  const phoneInput = useRef<PhoneInput>(null);
  const dispatch = useDispatch();
  const [updateProfile, {isLoading: updateLoading}] = useUpdateUserMutation();
  const [defaultCode, setDefaultCode] = useState<CountryCode>('US');

  useEffect(() => {
    refetch();
  }, []);

  const formik = useFormik<ProfileDto>({
    initialValues: {
      firstName: user?.firstName ?? '',
      lastName: user?.lastName ?? '',
      designation: user?.designation ?? '',
      email: user?.email ?? '',
      phone: user?.phone ?? '',
      gender: (user?.gender as Gender) ?? '',
      photoUrl: user?.photoUrl,
      dob: user?.dob ? user?.dob : moment().format(),
    },
    validationSchema: profileUpdateSchema,
    onSubmit: values => {
      const fullPhone =
        phoneInput.current?.getNumberAfterPossiblyEliminatingZero()
          ?.formattedNumber ?? formik.values.phone;
      const finalValues = {
        ...values,
        phone: fullPhone,
      };

      handleUpdate(finalValues);
    },
  });
  useEffect(() => {
    if (user) {
      let nationalNumber = '';

      try {
        const parsed = parsePhoneNumberWithError(
          user.phone.startsWith('+') ? user.phone : `+${user.phone}`,
        );
        console.log('parsed', parsed);

        if (parsed?.country) {
          const countryCode = parsed.country.toUpperCase() as CountryCode;
          nationalNumber = parsed.nationalNumber || '';
          setDefaultCode('IN');
          console.log('nationalNumber', nationalNumber);
        }
      } catch (e) {
        console.error('Failed to parse number:', e);
      }

      formik.setValues({
        firstName: user?.firstName ?? '',
        lastName: user?.lastName ?? '',
        designation: user?.designation ?? '',
        email: user?.email ?? '',
        phone: nationalNumber ?? '',
        gender: (user?.gender as Gender) ?? '',
        photoUrl: user?.photoUrl,
        dob: user?.dob ? user?.dob : moment().format(),
      });
    }
  }, [user]);

  const handleUpdate = async (values: ProfileDto) => {
    const finalFormdata: Partial<User> = {
      firstName: values.firstName,
      lastName: values.lastName,
      email: values.email,
      phone: values.phone,
      gender: values.gender ? values.gender : undefined,
      designation: values.designation ? values?.designation : undefined,
      dob: values.dob ? values?.dob : undefined,
      photoUrl: values?.photoUrl ?? undefined,
    };
    await updateProfile({
      userId: user?.id ?? '',
      body: finalFormdata,
    }).unwrap();
    refetch();
    Toast.show({
      type: 'success',
      text1: 'Store updated successfully!',
    });

    const userDetail = await getUserDetails().unwrap();
    if (userDetail) {
      dispatch(setUserDetails(userDetail));
    }
    navigation.pop();
  };

  const getImageSource = (value: ImageValue) => {
    if (typeof value === 'object' && value?.path) {
      return {uri: value.path};
    } else if (typeof value === 'string' && value) {
      return {uri: value};
    } else {
      return undefined;
    }
  };
  const handleImagePicker = async (fieldKey: string) => {
    try {
      const image = await openPicker({
        width: 500,
        height: 500,
        multiple: false,
        compressImageQuality: 0.8,
        mediaType: 'photo',
      });

      if (!image) {
        return;
      }
      formik.setFieldValue(fieldKey, image);
    } catch (error: any) {}
  };
  return (
    <SafeAreaView style={styles.mainContainer}>
      <KeyboardAwareScrollView
        nestedScrollEnabled
        style={styles.scrolllViewStyle}
        contentContainerStyle={styles.scrollviewContainer}>
        <View style={styles.profileImageContainer}>
          <ImageBackground
            source={getImageSource(formik?.values?.photoUrl)}
            resizeMode="contain"
            style={styles.profileImage}>
            <TouchableOpacity
              onPress={() => {
                handleImagePicker('photoUrl');
              }}
              style={styles.profileImageEditIcon}>
              <Icon
                source={'account-edit'}
                color={customColors.white}
                size={25}
              />
            </TouchableOpacity>
          </ImageBackground>
        </View>
        <CustomTextInput
          title="First Name"
          value={formik.values.firstName}
          onChangeText={formik.handleChange('firstName')}
          onBlur={formik.handleBlur('firstName')}
          touched={formik.touched.firstName}
          errors={formik.errors.firstName}
        />
        <CustomTextInput
          title="Last Name"
          value={formik.values.lastName}
          onChangeText={formik.handleChange('lastName')}
          onBlur={formik.handleBlur('lastName')}
          touched={formik.touched.lastName}
          errors={formik.errors.lastName}
        />
        <CustomTextInput
          title="Designation"
          value={formik.values.designation}
          onChangeText={formik.handleChange('designation')}
          onBlur={formik.handleBlur('designation')}
          touched={formik.touched.designation}
          errors={formik.errors.designation}
        />
        <CustomTextInput
          title="Email"
          value={formik.values.email}
          onChangeText={formik.handleChange('email')}
          onBlur={formik.handleBlur('email')}
          keyboardType="email-address"
          touched={formik.touched.email}
          errors={formik.errors.email}
        />
        <View style={styles.phoneInputView}>
          <Text variant="labelMedium" style={styles.phoneNumberTitle}>
            Phone Number
          </Text>
          <PhoneInput
            disableArrowIcon
            key={defaultCode}
            ref={phoneInput}
            placeholder=" "
            defaultValue={formik.values.phone}
            defaultCode={defaultCode}
            layout="second"
            onChangeFormattedText={text => {
              formik.handleChange('phone')(text);
              formik.setFieldTouched('phone', true);
            }}
            containerStyle={styles.phoneContainer}
            textContainerStyle={styles.phoneTextContainer}
            codeTextStyle={styles.codeText}
            textInputStyle={styles.codeText}
            flagButtonStyle={styles.flagButton}
          />

          <HelperText
            style={styles.helperText}
            type="error"
            padding="none"
            visible={
              formik.touched.phone &&
              typeof formik.errors.phone === 'string' &&
              formik.errors.phone?.length > 0
            }>
            {formik.errors.phone}
          </HelperText>
        </View>

        <CustomDropDown
          title="Gender"
          value={formik.values.gender}
          touched={formik.touched.gender}
          errors={formik.errors.gender}
          placeholder="Select your gender"
          data={[
            {label: 'Male', value: Gender.Male},
            {label: 'Female', value: Gender.Female},
            {label: 'Other', value: Gender.Other},
            {label: 'Unknown', value: Gender.Unknown},
          ]}
          labelField="label"
          valueField="value"
          onChange={(item: any) => formik.setFieldValue('gender', item.value)}
          onDropdownFocus={() => formik.setFieldTouched('gender', true)}
          onDropdownBlur={() => formik.handleBlur('gender')}
          onRemove={() => {
            formik.setFieldValue('gender', '');
          }}
        />
        <TouchableOpacity
          onPress={() => setShowDatePicker(true)}
          style={styles.datePickerContainer}>
          <Text variant="bodyMedium" style={styles.title}>
            {'Date of Birth'}
          </Text>
          <View style={styles.dateTimeContainer}>
            <Text variant="bodyMedium" style={styles.dateText}>
              {moment(formik.values.dob).format('DD/MM/YYYY')}
            </Text>
          </View>
        </TouchableOpacity>

        <CustomButton
          title="Update"
          disabled={updateLoading}
          loading={updateLoading}
          onPress={() => formik.handleSubmit()}
        />
      </KeyboardAwareScrollView>
      <DatePicker
        modal
        mode="date"
        open={showDatePicker}
        date={new Date()}
        onConfirm={date => {
          formik.setFieldValue('dob', moment(date).format());
          setShowDatePicker(false);
        }}
        onCancel={() => {
          setShowDatePicker(false);
        }}
      />
    </SafeAreaView>
  );
};

export default ProfileUpdateScreen;

const styles = StyleSheet.create({
  mainContainer: {
    backgroundColor: colors.surface,
    flex: 1,
    justifyContent: 'center',
  },
  scrolllViewStyle: {
    margin: styleConstants.spacing.x20,
    borderRadius: styleConstants.borderRadii.b10,
  },
  scrollviewContainer: {
    backgroundColor: customColors.white,
    borderRadius: styleConstants.borderRadii.b10,
    padding: styleConstants.spacing.x20,
    flexGrow: 1,
  },
  button: {
    marginTop: 10,
  },
  centeredView: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  datePickerCloseButton: {
    alignSelf: 'flex-end',
    marginBottom: 15,
  },
  datePickerContainer: {
    marginBottom: 10,
  },
  dateText: {
    fontFamily: FONTS.regular,
    fontSize: 16,
  },
  dateTimeContainer: {
    borderColor: colors.gray.dark,
    borderRadius: 35,
    borderWidth: 1,
    height: 35,
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  modalView: {
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 20,
    elevation: 5,
    margin: 20,
    padding: 35,
    paddingTop: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  profileImage: {
    backgroundColor: customColors.white,
    borderColor: colors.primary,
    borderRadius: 85,
    borderWidth: 1,
    height: 170,
    overflow: 'hidden',
    width: 170,
  },
  profileImageContainer: {
    alignSelf: 'center',
    marginBottom: styleConstants.spacing.x20,
  },
  profileImageEditIcon: {
    alignItems: 'center',
    backgroundColor: 'rgba(108, 122, 137,.8)',
    bottom: 0,
    height: 35,
    justifyContent: 'center',
    position: 'absolute',
    width: '100%',
  },
  title: {marginBottom: 5, marginLeft: 20},
  helperText: {
    marginLeft: 20,
  },
  phoneInputView: {width: '100%'},
  phoneNumberTitle: {marginBottom: 5, marginLeft: styleConstants.spacing.x20},
  phoneTextContainer: {
    backgroundColor: colors.onPrimary,
    borderRadius: 20,
    marginLeft: 0,
    paddingHorizontal: 0,
    paddingLeft: 0,
    paddingVertical: 0,
  },
  phoneContainer: {
    backgroundColor: colors.onPrimary,
    borderColor: colors.navy.deep,
    borderRadius: 20,
    borderWidth: 1,
    height: 40,
    width: '100%',
  },
  codeText: {
    fontFamily: FONTS.regular,
    fontSize: 14,
  },
  flagButton: {
    backgroundColor: customColors.borderGrey,
    borderBottomLeftRadius: 20,
    borderTopLeftRadius: 20,
    marginRight: styleConstants.spacing.s10,
  },
});
