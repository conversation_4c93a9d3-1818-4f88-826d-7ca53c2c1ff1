export const enum PermissionKey {
  ViewNotification = 'ViewNotification',
  CreateNotification = 'CreateNotification',
  UpdateNotification = 'UpdateNotification',
  DeleteNotification = 'DeleteNotification',
  CanGetNotificationAccess = 'CanGetNotificationAccess',

  ViewNotificationNum = '1',
  CreateNotificationNum = '2',
  UpdateNotificationNum = '3',
  DeleteNotificationNum = '4',
  CanGetNotificationAccessNum = '5',

  CreateDukeCoin = 'CreateDukeCoin',
  CreateReferralProgram = 'CreateReferralProgram',
  CreateReferral = 'CreateReferral',
  CreateUser = 'CreateUser',
  Onboard = 'Onboard',
  ViewReferral = 'ViewReferral',
  ViewReferralProgram = 'ViewReferralProgram',
  ViewUser = 'ViewUser',
  ViewDukeCoin = 'ViewDukeCoin',
  UpdateDukeCoin = 'UpdateDukeCoin',
  UpdateReferral = 'UpdateReferral',
  UpdateReferralProgram = 'UpdateReferralProgram',
  ViewConfiguration = 'ViewConfiguration',

  CreateCampaign = 'CreateCampaign',
  ViewCampaign = 'ViewCampaign',
  UpdateCampaign = 'UpdateCampaign',
  DeleteCampaign = 'DeleteCampaign',
}
