'use client';
// import Stack from '@mui/material/Stack';
// import Typography from '@mui/material/Typography';
// import MainCard from 'components/MainCard';
// import { Box, Chip, Grid, Button } from '@mui/material';
// import { useGetCampaignByIdQuery, useSendCampaignMutation } from 'redux/app/campaigns/campaignApiSlice';
// import { useEffect } from 'react';
// import Loader from 'components/Loader';
// import { SnackbarProps } from 'types/snackbar';
// import { openSnackbar } from 'api/snackbar';

// interface CampaignViewProps {
//   id: string;
//   refetch: () => void;
// }

// const DisplayItem = ({ label, value }: { label: string; value: string | number | undefined }) => (
//   <Box>
//     <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'text.primary', mb: 0.5 }}>
//       {label}:
//     </Typography>
//     <Box
//       sx={{
//         backgroundColor: '#f5f5f5',
//         px: 2,
//         py: 1.2,
//         borderRadius: 2,
//         fontSize: '0.95rem',
//         color: 'text.primary'
//       }}
//     >
//       {value}
//     </Box>
//   </Box>
// );

// function CampaignView({ id, refetch: refetchCampaignList }: CampaignViewProps) {
//   const { data, isLoading, error, refetch } = useGetCampaignByIdQuery({ id });
//   const [sendCampaign] = useSendCampaignMutation();

//   useEffect(() => {
//     refetch();
//   }, [refetch]);

//   if (isLoading) return <Loader />;
//   if (error) return <Typography color="error">Error loading Campaign details.</Typography>;
//   if (!id) return <Typography>No Campaign data available</Typography>;

//   const getCampaignTypeLabel = (type: number): string => {
//     const typeLabels: Record<number, string> = {
//       0: 'Push Notification',
//       1: 'Email',
//       2: 'SMS'
//     };
//     return typeLabels[type] || 'Unknown';
//   };

//   const handleSend = async () => {
//     try {
//       await sendCampaign({ campaignKey: data?.campaignKey! }).unwrap();
//       refetchCampaignList();
//       refetch();
//       openSnackbar({
//         open: true,
//         message: 'Campaign Sent successfully',
//         variant: 'alert',
//         alert: { color: 'success' }
//       } as SnackbarProps);
//     } catch {}
//   };

//   return (
//     <Grid container spacing={2.5}>
//       <Grid item xs={12} md={4}>
//         <MainCard
//           title={
//             <Stack direction="row" justifyContent="space-between" alignItems="center">
//               <Typography variant="h5">{getCampaignTypeLabel(data?.type || 0)}</Typography>
//               <Chip label={data?.isDraft ? 'Draft' : 'Sent'} size="small" color={data?.isDraft ? 'warning' : 'success'} />
//             </Stack>
//           }
//         >
//           <Stack spacing={1}>
//             {data && (
//               <>
//                 <DisplayItem label="Name" value={`${data.name}`} />
//                 <DisplayItem label="Subject" value={data.subject} />
//               </>
//             )}
//           </Stack>
//         </MainCard>
//       </Grid>
//       <Grid item xs={12} md={8}>
//         <MainCard
//           title={
//             <Stack direction="row" justifyContent="space-between" alignItems="center">
//               <Typography variant="h5">Message</Typography>
//               <Button variant="contained" size="medium" disabled={!data?.isDraft} onClick={() => handleSend()}>
//                 Send Campaign
//               </Button>
//             </Stack>
//           }
//         >
//           <Box
//             sx={{
//               height: 220,
//               overflowY: 'auto'
//             }}
//           >
//             <Box
//               sx={{
//                 mt: 2,
//                 p: 2,
//                 border: '1px solid #ccc',
//                 borderRadius: 2
//               }}
//             >
//               <iframe src={data?.body as string} title="HTML Content" width="100%" style={{ border: 'none' }} />
//             </Box>
//           </Box>
//         </MainCard>
//       </Grid>
//     </Grid>
//   );
// }

const CampaignForm = () => {
  return <>campaign Form</>;
};

export default CampaignForm;
