import {service} from '@loopback/core';
import {ListService} from '../services';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {post, getModelSchemaRef, requestBody, response} from '@loopback/rest';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {Groups} from '../models';

const basePath = '/lists';

export class ListController {
  constructor(
    @service(ListService)
    private readonly listService: ListService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCampaign]})
  @post(`${basePath}/subscribe`)
  @response(STATUS_CODE.OK, {
    description: 'Subscribe contact to list',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Groups)}},
  })
  async subscribeContact(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: {
            type: 'object',
            required: ['listKey', 'contactInfo', 'topicId'],
            properties: {
              listKey: {type: 'string'},
              topicId: {type: 'string'},
              contactInfo: {
                type: 'object',
                required: ['firstName', 'lastName', 'email'],
                properties: {
                  firstName: {type: 'string'},
                  lastName: {type: 'string'},
                  email: {type: 'string', format: 'email'},
                },
              },
            },
          },
        },
      },
    })
    payload: {
      listKey: string;
      topicId: string;
      contactInfo: {
        firstName: string;
        lastName: string;
        email: string;
      };
    },
  ): Promise<Groups> {
    const {listKey, contactInfo, topicId} = payload;
    return this.listService.subscribeContactToList(
      listKey,
      contactInfo,
      topicId,
    );
  }
}
