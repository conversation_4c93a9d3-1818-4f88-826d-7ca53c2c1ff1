import {Getter, inject} from '@loopback/core';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {PgDataSource} from '../datasources';
import {Support, SupportRelations} from '../models';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';

export class SupportRepository extends SequelizeUserModifyCrudRepositoryCore<
  Support,
  typeof Support.prototype.id,
  SupportRelations
> {
  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
  ) {
    super(Support, dataSource, getCurrentUser);
  }
}
