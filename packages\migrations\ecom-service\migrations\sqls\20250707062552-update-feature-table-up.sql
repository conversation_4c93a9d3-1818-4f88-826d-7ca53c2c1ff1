/* Replace with your SQL commands */

ALTER TABLE main.features ADD COLUMN category VARCHAR(100);

-- TOP FEATURES
UPDATE main.features SET category = 'TOP FEATURES' WHERE name IN (
  'GST Filing & Compliances',
  'Product Type',
  'Custom Orders',
  'Custom Bulk Order',
  'Messaging',
  'Profit Calculator',
  'Multiple Payment Methods',
  'No-Redirection Link',
  'Basic Analytics'
);

-- STORE FEATURES
UPDATE main.features SET category = 'STORE FEATURES' WHERE name IN (
  'Store Name & Details (bio)',
  'Store Logo',
  'Location',
  'Seller’s Name',
  'Seller Pic (DP)',
  'Banner Image (Cover Pic)',
  'Messaging',
  'Ratings & Reviews',
  'Share Store Link',
  'Profit Calculator'
);

-- PRODUCTS
UPDATE main.features SET category = 'PRODUCTS' WHERE name IN (
  'No of Products',
  'Images Per Product',
  'Product Videos',
  'Product Type',
  'Add Product Attributes',
  'Gift Packing',
  'Pin Products to top',
  'Disclaimer for product',
  'Product Categorisation',
  'Product Variants',
  'Product Customisation & Personalisation Options',
  'Unique Product Code'
);

-- STORE SETTINGS
UPDATE main.features SET category = 'STORE SETTINGS' WHERE name IN (
  'Open/Close Store',
  'Automated Inventory Management',
  'Seller’s Return Policy-Refund or Replacement and T & C',
  'Promo Codes'
);

-- SHIPPING
UPDATE main.features SET category = 'SHIPPING' WHERE name IN (
  'Shipping Options',
  'Doorstep Pickup',
  'COD',
  'Order Tracking',
  'Share Self Ship Order Tracking Link'
);

-- GST AND COMPLIANCES
UPDATE main.features SET category = 'GST AND COMPLIANCES' WHERE name IN (
  'GST Compliant Invoicing',
  'Debit Notes & Credit Notes',
  'GST Filings',
  'ITC Filing'
);

-- OTHER
UPDATE main.features SET category = 'OTHER' WHERE name IN (
  'Custom Order',
  'Custom Bulk Order',
  'Automated E-mail & SMS',
  'Free Promotions (if eligible)',
  'Payment Gateway',
  'Android & IOS App',
  'SEO',
  'Responsive Web Design',
  'Abandoned Carts'
);

-- PRICING/MONTH
UPDATE main.features SET category = 'PRICING/MONTH' WHERE name IN (
  'When total monthly sales value less than ₹5000',
  'When total monthly sales value exceeds ₹5000',
  'When total monthly sales value exceeds ₹12000',
  'When total monthly sales value exceeds ₹25000'
);
