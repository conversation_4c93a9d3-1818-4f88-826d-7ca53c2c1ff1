import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {WeightBasedShippingRule} from '../models';
import {WeightBasedShippingRuleRepository} from '../repositories';
import {STATUS_CODE, CONTENT_TYPE} from '@sourceloop/core';

const basePath = '/weight-based-shipping-rules';

export class WeightBasedShippingRuleController {
  constructor(
    @repository(WeightBasedShippingRuleRepository)
    public weightBasedShippingRuleRepository: WeightBasedShippingRuleRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'WeightBasedShippingRule model instance',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(WeightBasedShippingRule)},
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(WeightBasedShippingRule, {
            title: 'NewWeightBasedShippingRule',
            exclude: ['id'],
          }),
        },
      },
    })
    weightBasedShippingRule: Omit<WeightBasedShippingRule, 'id'>,
  ): Promise<WeightBasedShippingRule> {
    return this.weightBasedShippingRuleRepository.create(
      weightBasedShippingRule,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'WeightBasedShippingRule model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(WeightBasedShippingRule)
    where?: Where<WeightBasedShippingRule>,
  ): Promise<Count> {
    return this.weightBasedShippingRuleRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of WeightBasedShippingRule model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(WeightBasedShippingRule, {
            includeRelations: true,
          }),
        },
      },
    },
  })
  async find(
    @param.filter(WeightBasedShippingRule)
    filter?: Filter<WeightBasedShippingRule>,
  ): Promise<WeightBasedShippingRule[]> {
    return this.weightBasedShippingRuleRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(basePath)
  @response(STATUS_CODE.OK, {
    description: 'WeightBasedShippingRule PATCH success count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(WeightBasedShippingRule, {partial: true}),
        },
      },
    })
    weightBasedShippingRule: WeightBasedShippingRule,
    @param.where(WeightBasedShippingRule)
    where?: Where<WeightBasedShippingRule>,
  ): Promise<Count> {
    return this.weightBasedShippingRuleRepository.updateAll(
      weightBasedShippingRule,
      where,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'WeightBasedShippingRule model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(WeightBasedShippingRule, {
          includeRelations: true,
        }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(WeightBasedShippingRule, {exclude: 'where'})
    filter?: FilterExcludingWhere<WeightBasedShippingRule>,
  ): Promise<WeightBasedShippingRule> {
    return this.weightBasedShippingRuleRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'WeightBasedShippingRule PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(WeightBasedShippingRule, {partial: true}),
        },
      },
    })
    weightBasedShippingRule: Partial<WeightBasedShippingRule>,
  ): Promise<void> {
    await this.weightBasedShippingRuleRepository.updateById(
      id,
      weightBasedShippingRule,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'WeightBasedShippingRule PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() weightBasedShippingRule: WeightBasedShippingRule,
  ): Promise<void> {
    await this.weightBasedShippingRuleRepository.replaceById(
      id,
      weightBasedShippingRule,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'WeightBasedShippingRule DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.weightBasedShippingRuleRepository.deleteById(id);
  }
}
