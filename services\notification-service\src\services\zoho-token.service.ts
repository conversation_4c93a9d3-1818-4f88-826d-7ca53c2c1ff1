/* eslint-disable @typescript-eslint/naming-convention */
// src/services/zoho-token.service.ts

import {injectable, BindingScope} from '@loopback/core';
import {repository} from '@loopback/repository';
import axios from 'axios';
import {ZohoTokenRepository} from '../repositories';

@injectable({scope: BindingScope.SINGLETON})
export class ZohoTokenService {
  private readonly clientId = process.env.ZOHO_CLIENT_ID!;
  private readonly clientSecret = process.env.ZOHO_CLIENT_SECRET!;
  private readonly redirectUri = process.env.ZOHO_REDIRECT_URI!;
  private readonly refreshToken = process.env.ZOHO_REFRESH_TOKEN!;

  private tokenEndpoint = 'https://accounts.zoho.com/oauth/v2/token';
  private tokenEndpointBasedOnLocation =
    'https://accounts.zoho.in/oauth/v2/token';
  private authorizeEndpoint = 'https://accounts.zoho.com/oauth/v2/auth';

  constructor(
    @repository(ZohoTokenRepository)
    private readonly zohoTokenRepository: ZohoTokenRepository,
  ) {}
  /**
   * Generate the URL where the user should be redirected to get authorization code
   */
  getAuthorizationUrl(): string {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: this.clientId,
      scope: 'ZohoCampaigns.campaign.ALL,ZohoCampaigns.contact.ALL',
      redirect_uri: this.redirectUri,
      access_type: 'offline',
      prompt: 'consent',
    });

    return `${this.authorizeEndpoint}?${params.toString()}`;
  }

  /**
   * Exchange authorization code for access + refresh token
   */
  async exchangeCodeForToken(code: string): Promise<{
    access_token: string;
    refresh_token: string;
    expires_in: number;
  }> {
    const response = await axios.post(this.tokenEndpoint, null, {
      params: {
        grant_type: 'authorization_code',
        client_id: this.clientId,
        client_secret: this.clientSecret,
        redirect_uri: this.redirectUri,
        code,
      },
    });

    return response.data;
  }

  /**
   * Refresh access token using stored refresh token
   */
  async refreshAccessToken(): Promise<string> {
    const response = await axios.post(this.tokenEndpointBasedOnLocation, null, {
      params: {
        grant_type: 'refresh_token',
        client_id: this.clientId,
        client_secret: this.clientSecret,
        refresh_token: this.refreshToken,
      },
    });

    await this.zohoTokenRepository.set('zohoToken', {
      accessToken: response.data.access_token,
      expiresIn: response.data.expires_in, // Optional
      fetchedAt: new Date().toISOString(),
    });
    return response.data.access_token;
  }

  async getValidAccessToken(): Promise<string> {
    const token = await this.zohoTokenRepository.get('zohoToken');
    const isExpired =
      token?.fetchedAt && token?.expiresIn
        ? Date.now() >=
          new Date(token.fetchedAt).getTime() + token.expiresIn * 1000
        : true;

    if (!token?.accessToken || isExpired) {
      return this.refreshAccessToken();
    }

    return token.accessToken;
  }
}
