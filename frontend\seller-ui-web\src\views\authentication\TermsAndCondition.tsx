'use client';
import { <PERSON>, CardContent, Grid, Typography } from '@mui/material';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useGetTermsAndConditionQuery, useGetGuestTokenMutation } from 'redux/app/terms-and-condition/termsApiSlice';
import { setGuestToken } from '../../redux/auth/authSlice';
import draftToHtml from 'draftjs-to-html';
import { Legals } from 'enums/legal-category.enum';

export default function TermsCondition() {
  const [guestCode, setGuestCode] = useState<string | null>(null);
  const [getToken] = useGetGuestTokenMutation();
  const dispatch = useDispatch();

  // Call guest login on mount
  useEffect(() => {
    const handleLogin = async () => {
      const result = await getToken().unwrap();
      if (result?.accessToken) {
        dispatch(setGuestToken(result));
        setGuestCode(result.accessToken);
      }
    };

    handleLogin();
  }, [getToken, dispatch]);

  const { data: termData, isLoading } = useGetTermsAndConditionQuery(guestCode!, {
    skip: !guestCode
  });

  const isHtml = (str: string) => /<\/?[a-z][\s\S]*>/i.test(str);

  return (
    <Grid container sx={{ p: { xs: 2, sm: 4 } }}>
      <Grid item xs={12}>
        <Card sx={{ borderRadius: 3, p: 3 }}>
          {isLoading ? (
            <Typography>Loading...</Typography>
          ) : termData?.length ? (
            termData
              .filter((term) => term.type === Legals.TermsofUse)
              .map((term, index) => {
                let htmlContent: string;

                if (typeof term.data === 'string') {
                  htmlContent = isHtml(term.data) ? term.data : draftToHtml(JSON.parse(term.data));
                } else {
                  htmlContent = draftToHtml(term.data);
                }

                return (
                  <CardContent key={index}>
                    <div dangerouslySetInnerHTML={{ __html: htmlContent }} />
                  </CardContent>
                );
              })
          ) : (
            <Typography>No Terms and Conditions available.</Typography>
          )}
        </Card>
      </Grid>
    </Grid>
  );
}
