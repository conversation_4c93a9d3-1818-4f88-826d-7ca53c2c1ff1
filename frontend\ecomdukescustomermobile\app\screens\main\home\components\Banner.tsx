import {FC} from 'react';
import {SectionItem} from '../../../../types/page-section';
import {Image, StyleSheet} from 'react-native';

export const Banner: FC<{item: SectionItem}> = ({item}) => {
  return (
    <Image
      source={{uri: item.previewUrl || item.imageUrl}}
      style={styles.banner}
      resizeMode="cover"
    />
  );
};

const styles = StyleSheet.create({
  banner: {
    width: '100%',
    height: 150,
    marginBottom: 0,
    paddingBottom: 0,
  },
});
