/* Replace with your SQL commands */

-- Chat permissions
UPDATE main.roles
SET permissions = array_append(permissions, 'CreateChat')
WHERE role_type IN (0, 1, 2);

UPDATE main.roles
SET permissions = array_append(permissions, 'ViewChat')
WHERE role_type IN (0, 1, 2);

UPDATE main.roles
SET permissions = array_append(permissions, 'UpdateChat')
WHERE role_type IN (0, 1, 2);

UPDATE main.roles
SET permissions = array_append(permissions, 'DeleteChat')
WHERE role_type IN (0, 1, 2);

-- ChatMessage permissions
UPDATE main.roles
SET permissions = array_append(permissions, 'CreateChatMessage')
WHERE role_type IN (0, 1, 2);

UPDATE main.roles
SET permissions = array_append(permissions, 'ViewChatMessage')
WHERE role_type IN (0, 1, 2);

UPDATE main.roles
SET permissions = array_append(permissions, 'UpdateChatMessage')
WHERE role_type IN (0, 1, 2);

UPDATE main.roles
SET permissions = array_append(permissions, 'DeleteChatMessage')
WHERE role_type IN (0, 1, 2);
