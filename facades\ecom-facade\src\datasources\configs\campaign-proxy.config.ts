import {ModifiedRestService, RestOperationTemplate} from '@sourceloop/core';
import {Campaign} from '../../models';

export type CampaignProxyType = {
  sendCampaign(campaignKey: string, token: string): Promise<Campaign>;
} & ModifiedRestService<Campaign>;

export const CampaignProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'POST',
      url: `/campaigns/send`,
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: {
        campaignKey: '{campaignKey}',
      },
      query: {},
    },
    functions: {
      sendCampaign: ['campaignKey', 'token'],
    },
  },
];
