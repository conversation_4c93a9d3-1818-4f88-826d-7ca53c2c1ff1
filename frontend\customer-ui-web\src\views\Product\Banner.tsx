import {Box} from '@mui/material';

type BannerSectionProps = {
  imageUrl: string;
  altText?: string;
  link?: string;
};

export default function BannerSection({
  imageUrl,
  altText = 'Banner',
  link,
}: BannerSectionProps) {
  const content = (
    <Box
      sx={{
        width: '100%',
        maxHeight: 500, // adjust as needed
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'hidden',
        borderRadius: 2,
        backgroundColor: '#f5f5f5', // optional fallback background
      }}
    >
      <Box
        component="img"
        src={imageUrl}
        alt={altText}
        sx={{
          width: '100%',
          height: '100%',
          objectFit: 'contain', // ensures the full image is shown without cropping
        }}
      />
    </Box>
  );

  return link ? (
    <a href={link} target="_blank" rel="noopener noreferrer">
      {content}
    </a>
  ) : (
    content
  );
}
