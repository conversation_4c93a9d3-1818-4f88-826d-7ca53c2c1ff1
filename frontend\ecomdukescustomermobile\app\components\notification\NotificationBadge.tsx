import React, {useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import {Icon, Portal, Dialog, Button, Text} from 'react-native-paper';
import {colors} from '../../theme/colors';
import {useDispatch} from 'react-redux';
import {unsetCredentials} from '../../redux/auth/authSlice';
import {useNavigation} from '@react-navigation/native';
import {SCREEN_NAME} from '../../constants/screenNames';
import {AuthStackParamList} from '../../navigations/types';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {unsetMonitor} from '../../redux/apiMonitor/apiMonitorSlice';
import {useTypedSelector} from '../../redux/store';
import customColors from '../../theme/customColors';

const NotificationBadge = () => {
  const dispatch = useDispatch();
  const navigation =
    useNavigation<NativeStackNavigationProp<AuthStackParamList>>();
  const accessToken = useTypedSelector(state => state.auth.accessToken);

  const [showLogoutDialog, setShowLogoutDialog] = useState(false);

  const handleLogout = () => {
    dispatch(unsetCredentials());
    dispatch(unsetMonitor());
  };

  const confirmLogout = () => {
    setShowLogoutDialog(false);
    handleLogout();
  };

  return (
    <View style={styles.container}>
      <View style={styles.iconView}>
        {accessToken ? (
          <TouchableOpacity
            onPress={() => {
              setShowLogoutDialog(true);
            }}>
            <Icon source="logout" size={26} color={colors.tertiary} />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            onPress={() => {
              navigation.navigate(SCREEN_NAME.LOGIN);
            }}>
            <Icon source="login" size={26} color={colors.tertiary} />
          </TouchableOpacity>
        )}
      </View>

      <Portal>
        <Dialog
          visible={showLogoutDialog}
          onDismiss={() => setShowLogoutDialog(false)}
          style={styles.dialogBox}>
          <Dialog.Content>
            <Text style={styles.header}>Are you sure you want to log out?</Text>
          </Dialog.Content>

          <Dialog.Actions style={styles.dialogActions}>
            <Button
              onPress={() => setShowLogoutDialog(false)}
              textColor={colors.gray.dark}>
              Cancel
            </Button>
            <Button onPress={confirmLogout}>Logout</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

export default NotificationBadge;

const styles = StyleSheet.create({
  container: {position: 'relative'},
  notificationCount: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: 'red',
    borderRadius: 8,
    width: 14,
    height: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationCountText: {
    color: customColors.white,
    fontSize: 8,
    fontWeight: 'bold',
  },
  iconView: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  dialogBox: {
    borderRadius: 12,
    backgroundColor: customColors.white,
  },
  header: {fontSize: 16, marginLeft: 10},
  dialogActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
});
