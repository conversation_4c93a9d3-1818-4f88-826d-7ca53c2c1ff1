import {post, requestBody, response, HttpErrors} from '@loopback/rest';
import {service} from '@loopback/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {
  SubscriptionInput,
  SubscriptionService,
} from '../services/subscription.service';

export class SubscriptionController {
  constructor(
    @service(SubscriptionService)
    private readonly subscriptionService: SubscriptionService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCampaign]})
  @post('/subscribe')
  @response(STATUS_CODE.OK, {
    description: 'Subscribe to FCM and Zoho Campaign',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'object',
          properties: {
            fcm: {type: 'string'},
            zoho: {type: 'string'},
          },
        },
      },
    },
  })
  async subscribeToTopics(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: {
            type: 'object',
            required: ['topic', 'zohoListKey', 'zohoTopicId'],
            properties: {
              topic: {type: 'string'},
              fcmToken: {type: 'string'},
              email: {type: 'string'},
              zohoListKey: {type: 'string'},
              zohoTopicId: {type: 'string'},
            },
          },
        },
      },
    })
    subscriptionData: SubscriptionInput,
  ) {
    if (!subscriptionData.fcmToken && !subscriptionData.email) {
      throw new HttpErrors.BadRequest(
        'At least one of "fcmToken" or "email" must be provided.',
      );
    }

    return this.subscriptionService.subscribe(subscriptionData);
  }
}
