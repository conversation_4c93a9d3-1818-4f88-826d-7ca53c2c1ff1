'use client';
import Link from 'next/link';

// material-ui
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

// project-imports
import AuthWrapper2 from 'sections/auth/AuthWrapper2';
import FirebaseRegister from 'sections/auth/auth-forms/AuthRegister';
import { useSearchParams } from 'next/navigation';

// ================================|| REGISTER ||================================ //

export default function RegisterPage() {
  const searchParams = useSearchParams();
  const code = searchParams.get('code');
  return (
    <AuthWrapper2>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Stack direction="row" justifyContent="space-between" alignItems="baseline" sx={{ mb: { xs: -0.5, sm: 0.5, md: 5 } }}>
            <Typography variant="h3">Registration</Typography>
            <Typography component={Link} href={'/login'} variant="body1" sx={{ textDecoration: 'none' }} color="primary">
              Already have an account?
            </Typography>
          </Stack>
        </Grid>
        <Grid item xs={12}>
          <FirebaseRegister token={code} />
        </Grid>
      </Grid>
    </AuthWrapper2>
  );
}
