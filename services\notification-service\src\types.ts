import {
  INotification,
  Message,
  Subscriber,
  Receiver,
  MessageOptions,
} from 'loopback4-notifications';

export interface EmailNotification extends INotification {
  publish(message: EmailMessage): Promise<void>;
}
export interface EmailMessage extends Message {
  receiver: EmailR<PERSON>eiver;
}

export interface EmailReceiver extends Receiver {
  to: EmailSubscriber[];
  cc?: EmailSubscriber[];
  bcc?: EmailSubscriber;
}

export interface EmailSubscriber extends Subscriber {}

export interface ZeptoNotification extends EmailNotification {
  publish(message: ZeptoMessage): Promise<void>;
}

export interface ZeptoMessage extends EmailMessage {
  receiver: <PERSON>eptoReceiver;
}

export interface ZeptoReceiver {
  to: EmailSubscriber[];
}

export interface SMSNotification extends INotification {
  publish(message: SMSMessage): Promise<void>;
}

export interface SMSMessage extends Message {
  receiver: SMSReceiver;
  subject: undefined;
}

export interface SMSReceiver extends Receiver {
  to: SMSSubscriber[];
}

export interface SMSSubscriber extends Subscriber {}
export interface SMSMessageOptions extends MessageOptions {}

export interface Msg91Notification extends SMSNotification {
  publish(message: Msg91Message): Promise<void>;
}

export interface Msg91Message extends SMSMessage {
  receiver: Msg91Receiver;
  smsType: Msg91SMSType;
}

export interface Msg91Receiver extends SMSReceiver {
  to: SMSSubscriber[];
}

export const enum Msg91SMSType {
  Promotional = 'Promotional',
  Transactional = 'Transactional',
  Otp = 'Otp',
}

export type Msg91Config = {
  baseUrl: string;
  authKey: string;
  otpTemplateId: string;
};

export interface FCMSubscriber extends Subscriber {
  deviceToken: string;
}

export interface FCMReceiver extends Receiver {
  to: FCMSubscriber[];
}

export interface FCMMessage extends Message {
  receiver: FCMReceiver;
  data?: Record<string, string>; // Custom data payload
  notification?: {
    title: string;
    body: string;
  };
}

export interface FCMNotification extends INotification {
  publish(message: FCMMessage): Promise<void>;
}

export interface FCMConfig {
  projectId: string;
  privateKey: string;
  clientEmail: string;
}
