import {
  repository,
} from '@loopback/repository';
import {
  param,
  get,
  getModelSchemaRef,
} from '@loopback/rest';
import {
  EcomdukeserviceRequest,
  Ecomdukeservice,
} from '../models';
import {EcomdukeserviceRequestRepository} from '../repositories';

export class EcomdukeserviceRequestEcomdukeserviceController {
  constructor(
    @repository(EcomdukeserviceRequestRepository)
    public ecomdukeserviceRequestRepository: EcomdukeserviceRequestRepository,
  ) { }

  @get('/ecomdukeservice-requests/{id}/ecomdukeservice', {
    responses: {
      '200': {
        description: 'Ecomdukeservice belonging to EcomdukeserviceRequest',
        content: {
          'application/json': {
            schema: getModelSchemaRef(Ecomdukeservice),
          },
        },
      },
    },
  })
  async getEcomdukeservice(
    @param.path.string('id') id: typeof EcomdukeserviceRequest.prototype.id,
  ): Promise<Ecomdukeservice> {
    return this.ecomdukeserviceRequestRepository.ecomdukeservice(id);
  }
}
