'use client';

import React, {useMemo, useRef, useState} from 'react';
import {
  Grid,
  Typography,
  Avatar,
  Button,
  Box,
  Paper,
  Rating,
} from '@mui/material';
import {createTheme, ThemeProvider} from '@mui/material/styles';

const theme = createTheme({
  palette: {
    primary: {main: '#4A46BD'},
    text: {primary: '#333333', secondary: '#666666'},
    background: {default: '#F7F7F7'},
  },
  typography: {
    fontFamily: 'Inter, Arial, sans-serif',
    h4: {fontSize: '2rem', fontWeight: 400, color: '#333333'},
    body1: {fontSize: '1rem', lineHeight: 1.5, color: '#555555'},
    body2: {fontSize: '0.9rem', color: '#333333', fontWeight: 600},
    caption: {fontSize: '0.8rem', color: '#888888'},
  },
  components: {
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: '12px',
          boxShadow: '0px 2px 8px rgba(0, 0, 0, 0.05)',
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '8px',
          textTransform: 'none',
          borderColor: '#000845',
          color: '#000845',
          '&:hover': {
            borderColor: '#000845',
            backgroundColor: 'rgba(74, 70, 189, 0.04)',
          },
        },
      },
    },
    MuiRating: {
      styleOverrides: {
        iconFilled: {color: '#000845'},
        iconEmpty: {color: '#DEDEDE'},
      },
    },
  },
});

interface ApiReview {
  id?: string;
  rating: number;
  review?: string;
  reviewAssets: string[];
  previewAssets: string[];
  createdOn: string;
}

interface ProductVariantWithReviews {
  id: string;
  name: string;
  reviews?: ApiReview[];
  featuredAsset?: {preview: string};
}

interface Review {
  id: string;
  text: string;
  author: string;
  date: string;
  rating: number;
  avatar: string;
  previewAssets: string[];
  reviewAssets: string[];
  productId: string;
  productName: string;
  productThumbnail?: string;
}

interface StoreReviewsProps {
  productVariants: ProductVariantWithReviews[];
  isLoading: boolean;
  isError: boolean;
}

export default function StoreReviews({
  productVariants,
  isLoading,
  isError,
}: StoreReviewsProps) {
  const reviewSectionRef = useRef<HTMLDivElement | null>(null);
  const [showAllReviews, setShowAllReviews] = useState(false);

  const {topReviews, otherReviews} = useMemo(() => {
    if (isLoading || isError || !productVariants)
      return {topReviews: [], otherReviews: []};

    const assetBaseUrl = 'https://assets.ecomdukes.in';

    const allReviews: Review[] = productVariants.flatMap(variant =>
      variant.reviews && Array.isArray(variant.reviews)
        ? variant.reviews.map(apiReview => ({
            id:
              apiReview.id ||
              `review-${Math.random().toString(36).substring(2, 9)}`,
            text: apiReview.review || 'No review text provided.',
            author: 'Customer',
            date: new Date(apiReview.createdOn).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            }),
            rating: apiReview.rating,
            avatar:
              'https://cdn.pixabay.com/photo/2015/03/04/22/35/avatar-659652_960_720.png',
            previewAssets: apiReview.reviewAssets.map(
              asset => `${assetBaseUrl}/${asset}`,
            ),
            reviewAssets: apiReview.reviewAssets.map(
              asset => `${assetBaseUrl}/${asset}`,
            ),
            productId: variant.id,
            productName: variant.name,
            productThumbnail: variant.featuredAsset?.preview
              ? `${assetBaseUrl}/${variant.featuredAsset.preview}`
              : undefined,
          }))
        : [],
    );

    const sorted = [...allReviews].sort((a, b) => b.rating - a.rating);
    return {
      topReviews: sorted.slice(0, 2),
      otherReviews: sorted.slice(2),
    };
  }, [productVariants, isLoading, isError]);

  if (isLoading) {
    return <Typography>Loading reviews...</Typography>;
  }

  if (isError) {
    return <Typography color="error">Error loading reviews.</Typography>;
  }

  return (
    <ThemeProvider theme={theme}>
      <Box
        ref={reviewSectionRef}
        sx={{
          backgroundColor: theme.palette.background.default,
          display: 'flex',
          flexDirection: 'column',
          px: 2,
        }}
      >
        <Typography variant="h5" fontWeight="bold" sx={{mb: 2, mt: 4}}>
          Reviews
        </Typography>

        <Box
          sx={{
            width: '100%',
            maxWidth: 1380,
            display: 'flex',
            overflowX: 'hidden',
            gap: 3,
            pb: 1,
          }}
        >
          {topReviews.map(review => (
            <Box key={review.id} sx={{width: 660, flexShrink: 0}}>
              <ReviewCard review={review} />
            </Box>
          ))}
        </Box>

        {!showAllReviews && otherReviews.length > 0 && (
          <Box
            sx={{
              mt: 5,
              mb: 4,
              width: '100%',
              display: 'flex',
              justifyContent: 'center',
            }}
          >
            <Button
              variant="outlined"
              size="large"
              onClick={() => setShowAllReviews(true)}
            >
              See more Reviews
            </Button>
          </Box>
        )}

        {showAllReviews && otherReviews.length > 0 && (
          <Box
            sx={{
              width: '100%',
              maxWidth: 1380,
              display: 'flex',
              overflowX: 'auto',
              gap: 3,
              pb: 3,
              mt: 3,
              '&::-webkit-scrollbar': {
                height: '8px',
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: 'rgba(0,0,0,0.2)',
                borderRadius: '10px',
              },
              '&::-webkit-scrollbar-track': {
                backgroundColor: 'rgba(0,0,0,0.1)',
              },
            }}
          >
            {otherReviews.map(review => (
              <Box key={review.id} sx={{width: 670, flexShrink: 0}}>
                <ReviewCard review={review} />
              </Box>
            ))}
          </Box>
        )}
      </Box>

      {/* Simple Bottom Navigation/Menu */}
      <Box
        sx={{
          position: 'fixed',
          bottom: 0,
          left: 0,
          right: 0,
          height: 50,
          bgcolor: '#ffffff',
          borderTop: '1px solid #ddd',
          display: 'flex',
          justifyContent: 'space-around',
          alignItems: 'center',
          zIndex: 1000,
        }}
      >
        <Typography variant="body2">Home</Typography>
        <Typography variant="body2">Categories</Typography>
        <Typography variant="body2">Cart</Typography>
        <Typography variant="body2">Profile</Typography>
      </Box>
    </ThemeProvider>
  );
}

interface ReviewCardProps {
  review: Review;
}

const ReviewCard: React.FC<ReviewCardProps> = React.memo(({review}) => {
  return (
    <Paper
      elevation={2}
      sx={{
        p: 3,
        display: 'flex',
        flexDirection: 'column',
        height: 280,
      }}
    >
      {/* Associated product name & image */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          mb: 1,
          gap: 1,
        }}
      >
        {review.productThumbnail && (
          <Box
            component="img"
            src={review.productThumbnail}
            alt={review.productName}
            width={40}
            height={40}
            sx={{borderRadius: '4px', objectFit: 'cover', cursor: 'pointer'}}
            onClick={() =>
              window.open(`/products/${review.productId}`, '_blank')
            }
          />
        )}
        <Typography
          variant="body2"
          component="a"
          href={`/product-details/${review.productId}`}
          sx={{textDecoration: 'underline', color: '#000845'}}
          target="_blank"
          rel="noopener noreferrer"
        >
          {review.productName}
        </Typography>
      </Box>

      {/* Review text */}
      <Box
        sx={{
          flexGrow: 1,
          overflowY: 'auto',
          pr: 1,
          mb: 1.5,
        }}
      >
        <Typography
          variant="body1"
          sx={{
            maxWidth: 610,
            wordBreak: 'break-word',
          }}
        >
          {review.text}
        </Typography>
      </Box>

      {/* Images */}
      {review.previewAssets.length > 0 && (
        <Grid container spacing={1} sx={{mt: 1, mb: 2}}>
          {review.previewAssets.map((src, index) => (
            <Grid item key={`${index}-${src}`}>
              <Box
                component="img"
                src={src}
                alt={`Review preview ${index + 1}`}
                loading="lazy"
                width={60}
                height={60}
                sx={{
                  borderRadius: '6px',
                  objectFit: 'cover',
                  display: 'block',
                  cursor: 'pointer',
                }}
                onClick={() => window.open(src, '_blank')}
                onError={e => {
                  const target = e.target as HTMLImageElement;
                  if (!target.dataset.errorHandled) {
                    target.src =
                      'https://via.placeholder.com/60x60/CCCCCC/333333?text=Error';
                    target.dataset.errorHandled = 'true';
                  }
                }}
              />
            </Grid>
          ))}
        </Grid>
      )}

      {/* Reviewer info */}
      <Grid container alignItems="center" spacing={1}>
        <Grid item>
          <Avatar
            src={review.avatar}
            alt={review.author}
            sx={{width: 40, height: 40}}
            onError={e => {
              const target = e.target as HTMLImageElement;
              if (!target.dataset.errorHandled) {
                target.src = 'https://placehold.co/40x40/F0F0F0/808080?text=AB';
                target.dataset.errorHandled = 'true';
              }
            }}
          />
        </Grid>
        <Grid item sx={{flexGrow: 1}}>
          <Typography variant="body2">
            {review.author || 'Verified Buyer'}
          </Typography>
        </Grid>
        <Grid item>
          <Typography variant="caption">{review.date}</Typography>
        </Grid>
        <Grid item>
          <Rating value={review.rating} readOnly size="small" />
        </Grid>
      </Grid>
    </Paper>
  );
});
