// services/chat.service.ts
import {injectable, BindingScope, inject, Getter} from '@loopback/core';
import {RestBindings, Request, HttpErrors} from '@loopback/rest';
import {AuthenticationBindings} from 'loopback4-authentication';
import {
  Chat,
  Customer,
  CustomerRelations,
  CustomerWithRelations,
  IAuthUserWithTenant,
  Seller,
  SellerWithRelations,
} from '../models';
import {ChatProxyType} from '../datasources/configs/chat-proxy.config';
import {restService} from '@sourceloop/core';
import {Filter, Where} from '@loopback/repository';
import {SellerProxyType} from '../datasources/configs/seller.proxy.config';
import {CustomerProxyType} from '../datasources/configs';
import {CustomerDto} from '../models/ecom-service/dto/customer-dto.model';

@injectable({scope: BindingScope.TRANSIENT})
export class ChatService {
  private token: string;

  constructor(
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    private readonly getCurrentUser: Getter<IAuthUserWithTenant>,
    @restService(Chat)
    private readonly chatProxy: ChatProxyType,
    @restService(Seller)
    private readonly sellerProxyService: SellerProxyType,
    @restService(Customer)
    private readonly customerProxy: CustomerProxyType,
  ) {
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    }
  }

  async createChatWithAuthenticatedCustomer(
    chat: Omit<Chat, 'id' | 'customerId'>,
  ): Promise<Chat & {seller: Seller & {presignedPhotoUrl: string | null}}> {
    const currentUser = await this.getCurrentUser();

    if (!currentUser?.userTenantId) {
      throw new HttpErrors.Unauthorized('Customer userTenantId is missing');
    }

    const [customer] = await this.customerProxy.find({
      where: {userTenantId: currentUser.userTenantId},
    });

    if (!customer || !customer.id) {
      throw new HttpErrors.NotFound('Customer not found for the current user');
    }

    const customerId = customer.id;

    const existingChats = await this.chatProxy.find({
      where: {
        customerId,
        sellerId: chat.sellerId,
      },
    });

    const filter: Filter<Seller> = {
      fields: {
        id: true,
        sellerId: true,
        userTenantId: true,
        status: true,
      },
      include: [
        {
          relation: 'userTenant',
          scope: {
            include: [
              {
                relation: 'user',
                scope: {
                  fields: {
                    firstName: true,
                    lastName: true,
                    email: true,
                    photoUrl: true,
                  },
                },
              },
            ],
          },
        },
      ],
    };

    const seller = (await this.sellerProxyService.findById(
      chat.sellerId,
      filter,
    )) as SellerWithRelations;

    const cdnOrigin = process.env.CDN_ORIGIN!;
    const photoUrl = seller?.userTenant?.user?.photoUrl;

    const presignedPhotoUrl = photoUrl ? `${cdnOrigin}/${photoUrl}` : null;

    const sellerWithPresignedUrl = Object.assign(seller, {
      presignedPhotoUrl,
    }) as unknown as Seller & {presignedPhotoUrl: string | null};

    const chatRecord =
      existingChats.length > 0
        ? existingChats[0]
        : await this.chatProxy.create({...chat, customerId});

    return Object.assign(chatRecord, {
      seller: sellerWithPresignedUrl,
    }) as Chat & {seller: Seller & {presignedPhotoUrl: string | null}};
  }

  async getSellerWithPresignedUrl(
    sellerId: string,
  ): Promise<SellerWithRelations & {presignedPhotoUrl: string | null}> {
    const filter: Filter<Seller> = {
      fields: {id: true, sellerId: true, userTenantId: true, status: true},
      include: [
        {
          relation: 'userTenant',
          scope: {
            include: [
              {
                relation: 'user',
                scope: {
                  fields: {
                    firstName: true,
                    lastName: true,
                    email: true,
                    photoUrl: true,
                  },
                },
              },
            ],
          },
        },
      ],
    };

    const seller = (await this.sellerProxyService.findById(
      sellerId,
      filter,
    )) as SellerWithRelations;

    const cdnOrigin = process.env.CDN_ORIGIN!;
    const photoUrl = seller?.userTenant?.user?.photoUrl;
    const presignedPhotoUrl = photoUrl ? `${cdnOrigin}/${photoUrl}` : null;

    return {
      ...seller,
      presignedPhotoUrl,
    } as SellerWithRelations & {presignedPhotoUrl: string | null};
  }

  async getCustomerWithPresignedUrl(
    customerId: string,
  ): Promise<CustomerWithRelations & {presignedPhotoUrl: string | null}> {
    const filter: Filter<CustomerDto> = {
      fields: {
        id: true,
      },
      include: [
        {
          relation: 'userTenant',
          scope: {
            include: [
              {
                relation: 'user',
                scope: {
                  fields: {
                    firstName: true,
                    lastName: true,
                    email: true,
                    photoUrl: true,
                  },
                },
              },
            ],
          },
        },
      ],
    };

    // Fetch using proxy that returns DTO — cast as unknown first
    const customer = (await this.customerProxy.findById(
      customerId,
      filter as unknown as Filter<CustomerDto>,
    )) as unknown as CustomerRelations;

    // ✅ Use correct relation name
    const userTenant = customer?.userTenant;
    const photoUrl = userTenant?.user?.photoUrl ?? null;

    const cdnOrigin = process.env.CDN_ORIGIN!;
    const presignedPhotoUrl = photoUrl ? `${cdnOrigin}/${photoUrl}` : null;

    return {
      ...customer,
      presignedPhotoUrl,
    } as CustomerWithRelations & {presignedPhotoUrl: string | null};
  }

  async findChatsForCurrentUser(
    search?: string,
  ): Promise<(Chat & {userData: any})[]> {
    const currentUser = await this.getCurrentUser();

    if (!currentUser?.id || !currentUser?.role) {
      throw new Error('Unauthorized or invalid user');
    }

    let sellerId: string | undefined;
    let customerId: string | undefined;

    if (currentUser.role === 'Seller') {
      const [seller] = await this.sellerProxyService.find({
        where: {userTenantId: currentUser.userTenantId ?? ''},
      });
      if (!seller) throw new Error('Seller not found');
      sellerId = seller.id;
    } else if (currentUser.role === 'Customer') {
      const [customer] = await this.customerProxy.find({
        where: {userTenantId: currentUser.userTenantId ?? ''},
      });
      if (!customer) throw new Error('Customer not found');
      customerId = customer.id;
    }

    const where: Where<Chat> = {
      deleted: false,
      ...(currentUser.role === 'Customer' ? {customerId} : {sellerId}),
    };

    const chats = await this.chatProxy.find({where});

    const enrichedChats = await Promise.all(
      chats.map(async chat => {
        let userData = null;

        if (currentUser.role === 'Seller') {
          userData = await this.getCustomerWithPresignedUrl(chat.customerId);
        } else {
          userData = await this.getSellerWithPresignedUrl(chat.sellerId);
        }

        return {...chat, userData};
      }),
    );

    // 🔍 Apply name filtering if search is provided
    if (search) {
      const lowerSearch = search.toLowerCase();
      return enrichedChats.filter(chat => {
        const user = chat.userData?.userTenant?.user;
        return (
          user?.firstName?.toLowerCase().includes(lowerSearch) ||
          user?.lastName?.toLowerCase().includes(lowerSearch)
        );
      }) as (Chat & {userData: any})[];
    }

    return enrichedChats as (Chat & {userData: any})[];
  }
}
