import * as Yup from 'yup';

export const bankDetailsValidationSchema = Yup.object()
  .shape({
    accountNumber: Yup.string()
      .required('Account number is required')
      .matches(/^\d+$/, 'Account number must contain only digits')
      .min(9, 'Account number must be at least 9 digits')
      .max(18, 'Account number must not exceed 18 digits'),

    accountHolder: Yup.string()
      .required('Account holder name is required')
      .matches(
        /^[A-Za-z\s]+$/,
        'Account holder name must contain only alphabets and spaces',
      )
      .min(3, 'Account holder name must be at least 3 characters')
      .max(100, 'Account holder name must not exceed 100 characters'),

    ifsc: Yup.string()
      .matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Invalid IFSC code format')
      .required('IFSC code is required'),

    uidai: Yup.string()
      .matches(/^\d{12}$/, 'Aadhaar number must be 12 digits')
      .nullable()
      .optional(),

    gst: Yup.string()
      .matches(
        /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
        'Invalid GST format',
      )
      .nullable()
      .optional(),

    pan: Yup.string()
      .matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Invalid PAN format')
      .nullable()
      .optional(),

    businessType: Yup.string().required('Business type is required'),
  })
  .test(
    'at-least-one-kyc',
    'At least one KYC document (Aadhaar, PAN, or GST) is required',
    function (values) {
      return !!(values.uidai || values.pan || values.gst);
    },
  );
