import {
  post,
  requestBody,
  response,
  HttpErrors,
  RestBindings,
  Request,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {CONTENT_TYPE, restService, STATUS_CODE} from '@sourceloop/core';
import {SubscriptionInput} from '../models/notification/subscription-input.model';
import {SubscribeProxyType} from '../datasources/configs/subscribe-proxy.config';
import {inject} from '@loopback/context';

export class SubscriptionController {
  constructor(
    @restService(SubscriptionInput)
    private readonly subscriptionProxyService: SubscribeProxyType,
    @inject(RestBindings.Http.REQUEST)
    private readonly request: Request,
    private token: string,
  ) {
    if (this.request.headers.authorization) {
      this.token = this.request.headers.authorization;
    }
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCampaign]})
  @post('/subscribe')
  @response(STATUS_CODE.OK, {
    description: 'Subscribe to FCM and Zoho Campaign',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'object',
          properties: {
            fcm: {type: 'string'},
            zoho: {type: 'string'},
          },
        },
      },
    },
  })
  async subscribeToTopics(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: {
            type: 'object',
            required: ['topic', 'zohoListKey', 'zohoTopicId'],
            properties: {
              topic: {type: 'string'},
              fcmToken: {type: 'string'},
              email: {type: 'string'},
              zohoListKey: {type: 'string'},
              zohoTopicId: {type: 'string'},
            },
          },
        },
      },
    })
    subscriptionData: SubscriptionInput,
  ) {
    if (!subscriptionData.fcmToken && !subscriptionData.email) {
      throw new HttpErrors.BadRequest(
        'At least one of "fcmToken" or "email" must be provided.',
      );
    }

    return this.subscriptionProxyService.subscribe(
      {
        topic: subscriptionData.topic,
        email: subscriptionData.email,
        fcmToken: subscriptionData.fcmToken,
        zohoListKey: subscriptionData.zohoListKey,
        zohoTopicId: subscriptionData.zohoTopicId,
      },
      this.token,
    );
  }
}
