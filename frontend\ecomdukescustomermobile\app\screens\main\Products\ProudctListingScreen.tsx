import React, {useEffect, useMemo, useState} from 'react';
import {
  View,
  Text,
  FlatList,
  Pressable,
  StyleSheet,
  ScrollView,
} from 'react-native';
import ProductCard from './Components/ProductCard';
import {colors} from '../../../theme/colors';
import customColors from '../../../theme/customColors';
import {ActivityIndicator, Searchbar} from 'react-native-paper';
import QuickFilters from './Components/QuickFilter';
import SubCategoryList from './Components/SubCategoryList';
import LeftDrawerModal from './Components/LeftDrawerModal';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {
  OnboardStackParamList,
  ProductScreenNavigationProp,
} from '../../../navigations/types';
import {Images} from '../../../assets/images';
import {
  FilterValue,
  useFiltersQuery,
  useSearchQuery,
} from '../../../redux/search/searchApiSlice';
import {SCREEN_NAME} from '../../../constants/screenNames';
import {useAddItemToCartMutation} from '../../../redux/cart/cartApiSlice';
import Toast from 'react-native-toast-message';
import {getSearchParam} from '../../../constants/product';
import {
  useAddItemToWishlistMutation,
  useRemoveItemFromWishlistMutation,
} from '../../../redux/wishlist/wishlistApiSlice';
import {useTypedSelector} from '../../../redux/store';
import {useGetUserQuery} from '../../../redux/auth/authApiSlice';
const ProductListingScreen = () => {
  const navigation = useNavigation<ProductScreenNavigationProp>();
  const route = useRoute<RouteProp<OnboardStackParamList, 'productDetails'>>();
  const passedSearchQuery = route.params?.searchQuery ?? '';
  const [searchQuery, setSearchQuery] = useState(passedSearchQuery);
  const [isDrawerVisible, setDrawerVisible] = useState(false);
  const [appliedFilters, setAppliedFilters] = useState<FilterValue[]>([]);
  const [additionalSearchFilter, setAdditionalSearchFilter] = useState<
    undefined | Record<string, unknown>
  >(undefined);
  const [addedToCart, setAddedToCart] = useState<Set<string>>(new Set());
  const [addItemToCart, {isLoading: addToCartLoading}] =
    useAddItemToCartMutation();
  const [addItemToWishlist] = useAddItemToWishlistMutation();
  const [removeItemFromWishlist] = useRemoveItemFromWishlistMutation();
  const getDiscountPercentage = (
    originalPrice: number,
    price: number,
  ): number => {
    if (originalPrice <= 0) {
      return 0;
    }
    return Math.round(((originalPrice - price) / originalPrice) * 100);
  };
  const {data: user} = useGetUserQuery();
  const profileId = user?.profileId;
  const {isLoggedIn} = useTypedSelector(state => state.auth);
  const searchParam = getSearchParam(isLoggedIn, profileId);

  const handleAddToCart = async (product: any) => {
    await addItemToCart({
      productVariantId: product.id,
      quantity: 1,
    }).unwrap();
    Toast.show({
      type: 'success',
      text1: 'Added to cart!',
    });
    setAddedToCart(prev => new Set(prev).add(product.id));
  };

  const {data: filters} = useFiltersQuery(searchQuery, {
    skip: !searchQuery,
  });

  const facetFilters = useMemo(() => {
    if (!filters) {
      return [];
    }
    return filters?.filter(item => item.isFacet)?.flatMap(item => item.values);
  }, [filters]);

  const {
    data: products,
    isLoading: dataLoading,
    refetch: reSearch,
  } = useSearchQuery(
    {
      keyword: searchQuery,
      where: additionalSearchFilter,
      ...searchParam,
    },
    {
      skip: !searchQuery?.trim(),
    },
  );

  const toggleFilter = (selectedFilter: FilterValue) => {
    setAppliedFilters(prev => {
      const exists = prev.some(
        f =>
          f.label === selectedFilter.label && f.value === selectedFilter.value,
      );
      if (exists) {
        const updated = prev.filter(
          f =>
            !(
              f.label === selectedFilter.label &&
              f.value === selectedFilter.value
            ),
        );
        return updated.length === 0 ? [] : updated;
      } else {
        return [...prev, selectedFilter];
      }
    });
  };

  const handleProductClick = (productId: string) => {
    navigation.navigate(SCREEN_NAME.PRODUCTS, {productId});
  };

  const [wishlistMap, setWishlistMap] = useState<Map<string, string>>(
    new Map(),
  );

  const handleToggleWishlist = async (productId: string) => {
    if (wishlistMap.has(productId)) {
      const wishlistIds = wishlistMap.get(productId);
      if (wishlistIds) {
        await removeItemFromWishlist(wishlistIds).unwrap();
        const updated = new Map(wishlistMap);
        updated.delete(productId);
        setWishlistMap(updated);
        Toast.show({text1: 'Removed from wishlist', type: 'info'});
      }
    } else {
      const result = await addItemToWishlist(productId).unwrap();
      const updated = new Map(wishlistMap);
      updated.set(productId, result.id ?? '');
      setWishlistMap(updated);
      Toast.show({text1: 'Added to wishlist', type: 'success'});
    }
  };
  useEffect(() => {
    const map = new Map();
    products?.forEach(p => {
      if (p.wishlist?.id) {
        map.set(p.id.toString(), p.wishlist.id);
      }
    });
    setWishlistMap(map);
  }, [products]);
  useEffect(() => {
    setSearchQuery(passedSearchQuery);
  }, [passedSearchQuery]);
  const subCategories = [
    {id: 1, name: 'Accessories', image: Images.cosmetics},
    {id: 2, name: 'Skin Care', image: Images.serum},
    {id: 3, name: 'Dresses', image: Images.jwellery},
    {id: 4, name: 'Essentials', image: Images.tshirt},
    {id: 5, name: 'Gadgets', image: Images.laptop},
  ];

  const onFilterSubmit = async () => {
    setDrawerVisible(false);
    if (!appliedFilters.length) {
      setAdditionalSearchFilter(undefined);
      return;
    }
    const ids = appliedFilters.flatMap(item => [...item.productVariantIds]);
    if (!ids?.length) {
      setAdditionalSearchFilter(undefined);
      return;
    }
    setAdditionalSearchFilter({id: {inq: ids}});
    await reSearch();
  };
  const handleCartPress = () => {
    navigation.navigate('mainHome', {screen: SCREEN_NAME.CART});
  };
  // eslint-disable-next-line @typescript-eslint/no-shadow
  const onSubmitSearch = (searchQuery: string) => {
    if (searchQuery.trim() !== '') {
      navigation.navigate(SCREEN_NAME.SUGGESTION, {
        searchQuery,
      });
      setSearchQuery('');
    }
  };
  const handleFocus = () => {
    navigation.navigate(SCREEN_NAME.SUGGESTION, {searchQuery: searchQuery});
    setSearchQuery(searchQuery);
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.subCategory}>
        <Searchbar
          placeholder="Search products..."
          onChangeText={text => {
            setSearchQuery(text);
            onSubmitSearch(text);
          }}
          onFocus={() => handleFocus()}
          onSubmitEditing={() => setSearchQuery(searchQuery)}
          onIconPress={() => setSearchQuery(searchQuery)}
          value={searchQuery}
          style={styles.searchbar}
          placeholderTextColor={colors.gray.medium}
        />
        <SubCategoryList
          data={subCategories}
          imageContainerStyle={styles.imageContainer}
        />
      </View>

      <View style={styles.header}>
        <Text style={styles.title}>Products </Text>
      </View>
      <QuickFilters
        onFilterPress={() => setDrawerVisible(true)}
        selectedFilters={appliedFilters}
        onToggleFilter={toggleFilter}
        filters={facetFilters}
      />

      {appliedFilters.length > 0 && (
        <View style={styles.chipContainer}>
          {appliedFilters.map(filter => (
            <View key={filter.label} style={styles.chip}>
              <Text style={styles.chipText}>{filter.label}</Text>
            </View>
          ))}
        </View>
      )}
      {dataLoading && <ActivityIndicator />}

      {!dataLoading && (!products || products.length === 0) && (
        <Text style={styles.notFoundText}>No products found.</Text>
      )}

      <FlatList
        data={products}
        numColumns={2}
        keyExtractor={item => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        columnWrapperStyle={styles.rowSpacing}
        renderItem={({item}) => {
          return (
            <Pressable style={styles.cardWrapper}>
              <ProductCard
                onPress={() => handleProductClick(item.id)}
                id={Number(item.id)}
                image={item.featuredAsset?.previewUrl}
                name={item.name}
                price={item.productVariantPrice.price}
                originalPrice={item.productVariantPrice.mrp}
                discount={`${getDiscountPercentage(
                  parseFloat(item.productVariantPrice.mrp),
                  parseFloat(item.productVariantPrice.price),
                )}%`}
                rating={4}
                isWishlisted={wishlistMap.has(item.id.toString())}
                onWishlistToggle={() =>
                  handleToggleWishlist(item.id.toString())
                }
                shortDescription={item.sku}
                isAddedToCart={addedToCart.has(item.id)}
                onAddToCart={() => {
                  if (addedToCart.has(item.id)) {
                    handleCartPress();
                  } else {
                    handleAddToCart(item);
                  }
                }}
                isloading={addToCartLoading ?? false}
              />
            </Pressable>
          );
        }}
      />
      <LeftDrawerModal
        visible={isDrawerVisible}
        onClose={() => setDrawerVisible(false)}
        selectedFilters={appliedFilters}
        onToggleFilter={toggleFilter}
        onFilterSubmit={onFilterSubmit}
        filters={filters ?? []}
      />
    </ScrollView>
  );
};

export default ProductListingScreen;

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: colors.secondary},
  header: {
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {fontSize: 18, fontWeight: 'bold'},
  sortButton: {
    borderColor: colors.gray.medium,
    marginRight: 8,
    minWidth: 200,
  },
  sortText: {
    fontSize: 14,
    color: colors.tertiary,
  },

  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 12,
    marginBottom: 8,
  },
  chip: {
    backgroundColor: colors.gray.light,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    margin: 4,
  },
  chipText: {fontSize: 12},
  list: {padding: 8},
  column: {justifyContent: 'space-between'},
  cardWrapper: {width: '48%', marginBottom: 16},
  modalOverlay: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: colors.transparent,
  },
  drawer: {
    position: 'absolute',
    right: 0,
    width: '80%',
    height: '100%',
    backgroundColor: customColors.white,
    padding: 16,
    elevation: 5,
  },
  subCategory: {marginBottom: 0, paddingBottom: 0},
  filter: {marginTop: 3, paddingTop: 3},
  listContainer: {
    paddingHorizontal: 12,
    paddingBottom: 16,
  },
  rowSpacing: {
    justifyContent: 'space-between',
    paddingHorizontal: 4,
  },

  searchbar: {
    marginHorizontal: 10,
    borderRadius: 25,
    marginTop: 10,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: colors.gray.medium,
    backgroundColor: customColors.white,
    height: 50,
  },
  quickFilterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    paddingHorizontal: 12,
    marginTop: 10,
  },

  quickButton: {
    backgroundColor: colors.gray.light,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginRight: 8,
    marginBottom: 6,
  },

  buttonText: {
    fontSize: 14,
    color: customColors.textBlack,
  },
  notFoundText: {textAlign: 'center', marginTop: 20},
  imageContainer: {
    alignItems: 'center',
    marginHorizontal: 5,
    marginTop: 10,
  },
  mainContainer: {
    zIndex: 10,
    elevation: 10,
  },
  menuContent: {
    borderRadius: 8,
    elevation: 4,
    shadowColor: customColors.textBlack,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 6,
  },
});
