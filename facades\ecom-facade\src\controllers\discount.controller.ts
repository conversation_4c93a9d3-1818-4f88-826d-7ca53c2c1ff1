import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {CreateDiscountDto, Discount} from '../models';
import {restService, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {PermissionKeys} from '@local/core';
import {authorize} from 'loopback4-authorization';
import {DiscountProxyType} from '../datasources/configs/discount-proxy.config';
import {service} from '@loopback/core';
import {DiscountHelperService} from '../services';

const basePath = '/discounts';

export class DiscountController {
  constructor(
    @restService(Discount)
    private discountProxy: DiscountProxyType,
    @service(DiscountHelperService)
    private readonly discountHelperService: DiscountHelperService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateDiscount]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Discount model instance',
    content: {'application/json': {schema: getModelSchemaRef(Discount)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CreateDiscountDto, {
            title: 'NewDiscountWithConditions',
            exclude: ['id'],
          }),
        },
      },
    })
    discountDto: CreateDiscountDto,
    @param.header.string('Authorization') token?: string,
  ): Promise<Discount> {
    return this.discountProxy.createDiscount(discountDto, token ?? '');
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDiscount]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'Discount model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(@param.where(Discount) where?: Where<Discount>): Promise<Count> {
    return this.discountProxy.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDiscount]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Discount model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Discount, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Discount) filter?: Filter<Discount>,
  ): Promise<Discount[]> {
    return this.discountProxy.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDiscount]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'Discount model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(Discount, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Discount, {exclude: 'where'})
    filter?: FilterExcludingWhere<Discount>,
  ): Promise<Discount> {
    return this.discountProxy.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateDiscount]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Discount PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(CreateDiscountDto, {partial: true}),
        },
      },
    })
    discount: CreateDiscountDto,
    @param.header.string('Authorization') token?: string,
  ): Promise<void> {
    await this.discountProxy.updateDiscountWithConditions(
      id,
      discount,
      token ?? '',
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewDiscount]})
  @post(`${basePath}/check-eligibility`)
  @response(STATUS_CODE.OK, {
    description: 'Check first-order discount eligibility',
    content: {
      'application/json': {
        schema: {
          type: 'object',
          properties: {
            isEligible: {type: 'boolean'},
            appliedDiscount: {
              type: ['object', 'null'],
              properties: {
                conditionId: {type: 'string'},
                discountValue: {type: 'number'},
                discountType: {type: 'string'},
              },
            },
            nextTierMessage: {type: 'string'},
            discountBreakdownMessage: {type: 'string'},
          },
        },
      },
    },
  })
  async checkFirstOrderDiscountEligibility(
    @requestBody({
      description: 'Cart and user context for discount evaluation',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              cartTotal: {type: 'number'},
              isFromApp: {type: 'boolean'},
            },
          },
        },
      },
    })
    body: {
      cartTotal: number;
      isFromApp: boolean;
    },
    @param.header.string('Authorization') token?: string,
  ): Promise<unknown> {
    return this.discountHelperService.checkDiscountEligibility(
      body,
      token ?? '',
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateDiscount]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Discount PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() discount: Discount,
  ): Promise<void> {
    await this.discountProxy.replaceById(id, discount);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteDiscount]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Discount DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.discountProxy.deleteById(id);
  }
}
