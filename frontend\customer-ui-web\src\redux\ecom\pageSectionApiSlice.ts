import {PageSection} from 'types/page-section'; // adjust import path as needed
import {buildFilterParams, IFilter} from 'types/api'; // you already have this
import {apiSlice} from 'redux/apiSlice';
import {ApiSliceIdentifier} from 'enums/api.enum';
import {ProductVariant} from 'types/product';

export const pageSectionApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getPageSections: builder.query<PageSection[], IFilter>({
      query: filter => ({
        url: '/page-sections',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    getMostViewedProducts: builder.query<ProductVariant[], IFilter>({
      query: filter => ({
        url: '/products/most-viewed',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    getRecentlyViewedProducts: builder.query<ProductVariant[], IFilter>({
      query: filter => ({
        url: '/products/recently-viewed',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    getTopSellingProducts: builder.query<ProductVariant[], IFilter>({
      query: filter => ({
        url: '/products/top-selling',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
  }),
});

export const {
  useGetPageSectionsQuery,
  useLazyGetPageSectionsQuery,
  useGetMostViewedProductsQuery,
  useGetRecentlyViewedProductsQuery,
  useGetTopSellingProductsQuery,
} = pageSectionApiSlice;
