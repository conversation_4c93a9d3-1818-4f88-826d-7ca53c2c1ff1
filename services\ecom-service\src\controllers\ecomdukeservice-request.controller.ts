import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
  repository,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';

import {EcomdukeserviceRequest} from '../models';
import {EcomdukeserviceRequestRepository} from '../repositories';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';

const basePath = '/ecomdukeservice-requests';

export class EcomDukeServiceRequestController {
  constructor(
    @repository(EcomdukeserviceRequestRepository)
    public requestRepo: EcomdukeserviceRequestRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateServiceRequest]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'EcomdukeserviceRequest model instance',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(EcomdukeserviceRequest)},
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(EcomdukeserviceRequest, {
            exclude: ['id'],
            title: 'NewEcomDukeServiceRequest',
          }),
        },
      },
    })
    request: Omit<EcomdukeserviceRequest, 'id'>,
  ): Promise<EcomdukeserviceRequest> {
    return this.requestRepo.create(request);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewServiceRequest]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'EcomdukeserviceRequest count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(EcomdukeserviceRequest) where?: Where<EcomdukeserviceRequest>,
  ): Promise<Count> {
    return this.requestRepo.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewServiceRequest]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of EcomdukeserviceRequest instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(EcomdukeserviceRequest, {
            includeRelations: true,
          }),
        },
      },
    },
  })
  async find(
    @param.filter(EcomdukeserviceRequest)
    filter?: Filter<EcomdukeserviceRequest>,
  ): Promise<EcomdukeserviceRequest[]> {
    return this.requestRepo.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewServiceRequest]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'EcomdukeserviceRequest model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(EcomdukeserviceRequest, {
          includeRelations: true,
        }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(EcomdukeserviceRequest, {exclude: 'where'})
    filter?: FilterExcludingWhere<EcomdukeserviceRequest>,
  ): Promise<EcomdukeserviceRequest> {
    return this.requestRepo.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateServiceRequest]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'EcomdukeserviceRequest PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(EcomdukeserviceRequest, {partial: true}),
        },
      },
    })
    request: Partial<EcomdukeserviceRequest>,
  ): Promise<void> {
    await this.requestRepo.updateById(id, request);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteServiceRequest]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'EcomdukeserviceRequest DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.requestRepo.deleteById(id);
  }
}
