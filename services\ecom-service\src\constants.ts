export const UPLOAD_FILE_SIZE = 10 * 1024 * 1024;

export const MAX_FILES = 5;

export const ALLOWED_FILE_EXTENSIONS = ['.doc', '.txt', '.pdf'];

export const ALLOWED_IMAGE_EXTENSIONS = [
  '.jpg',
  '.jpeg',
  '.png',
  '.gif',
  '.webp',
  '.svg',
];
export const ALLOWED_VIDEO_EXTENSIONS = [
  '.mp4',
  '.mov',
  '.avi',
  '.wmv',
  '.flv',
  '.mkv',
  '.webm',
  '.mpeg',
  '.mpg',
  '.3gp',
  '.ogg',
  '.m4v',
];

export const ALLOWED_ASSET_EXTENSIONS = [
  ...ALLOWED_IMAGE_EXTENSIONS,
  ...ALLOWED_VIDEO_EXTENSIONS,
];

export const metaFields = [
  'deleted',
  'deletedOn',
  'deletedBy',
  'createdBy',
  'createdOn',
  'modifiedOn',
  'modifiedBy',
];

export const fieldsExcludeMetaFields = metaFields.reduce(
  (acc, field) => {
    acc[field] = false;
    return acc;
  },
  {} as Record<string, boolean>,
);
