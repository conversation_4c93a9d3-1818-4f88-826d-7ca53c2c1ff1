import {model, property, hasMany} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {
  PromoApplicability,
  PromoCodeVisibility,
  PromoType,
} from '../enums/promo-type.enum';
import {PromoCodeCollection} from './promo-code-collection.model';
import {PromoCodeProduct} from './promo-code-product.model';

@model({settings: {strict: false}, name: 'promo_codes'})
export class PromoCode extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  code: string;

  @property({
    type: 'string',
    required: true,
  })
  type: PromoType;

  @property({
    type: 'number',
    required: true,
  })
  value: number;

  @property({
    type: 'string',
    name: 'applies_to',
    required: true,
  })
  appliesTo: PromoApplicability;

  @property({
    type: 'number',
    name: 'min_cart_value',
  })
  minCartValue?: number;

  @property({
    type: 'number',
    name: 'min_product_value',
  })
  minProductValue?: number;

  @property({
    type: 'number',
    name: 'max_discount_cap',
  })
  maxDiscountCap?: number;

  @property({
    type: 'boolean',
    name: 'is_first_time_user_only',
    default: false,
  })
  isFirstTimeUserOnly?: boolean;

  @property({
    type: 'boolean',
    name: 'is_repeat_user_only',
    default: false,
  })
  isRepeatUserOnly?: boolean;

  @property({
    type: 'date',
    name: 'valid_from',
    required: true,
  })
  validFrom: string;

  @property({
    type: 'date',
    name: 'valid_till',
    required: true,
  })
  validTill: string;

  @property({
    type: 'number',
    name: 'usage_limit_total',
  })
  usageLimitTotal?: number;

  @property({
    type: 'number',
    name: 'usage_limit_per_user',
  })
  usageLimitPerUser?: number;

  @property({
    type: 'string',
    required: true,
  })
  visibility: PromoCodeVisibility;

  @property({
    type: 'boolean',
    name: 'is_active',
    default: true,
  })
  isActive?: boolean;

  @hasMany(() => PromoCodeCollection, {keyTo: 'promoCodeId'})
  promoCodeCollections: PromoCodeCollection[];

  @hasMany(() => PromoCodeProduct, {keyTo: 'promoCodeId'})
  promoCodeProducts: PromoCodeProduct[];

  constructor(data?: Partial<PromoCode>) {
    super(data);
  }
}

export interface PromoCodeRelations {
  // define navigational properties here if any
}

export type PromoCodeWithRelations = PromoCode & PromoCodeRelations;
