'use client';
import React, {useEffect, useState, useCallback} from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  Collapse,
  styled,
} from '@mui/material';
import {ArrowDown2, ArrowRight2} from 'iconsax-react';
import {usePathname, useRouter} from 'next/navigation';
import {unsetCredentials} from 'redux/auth/authSlice';
import {unsetMonitor} from 'redux/apimonitor/apiMonitorSlice';
import {ApiTagTypes} from 'redux/types';
import {useAppDispatch} from 'redux/hooks';
import {apiSlice} from 'redux/apiSlice';
import AlertDeactivateAccount from 'components/AlertDeactivateAccount';
import {useGetUserQuery} from 'redux/auth/authApiSlice';

// ----- Styles -----
const StyledCard = styled(Box)(({theme}) => ({
  backgroundColor: '#fff',
  borderRadius: '16px',
  boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.1)',
  padding: theme.spacing(3),
  width: '100%',
  marginTop: theme.spacing(1),
}));

const StyledListItemButton = styled(ListItemButton)(({theme}) => ({
  padding: theme.spacing(1.5, 2),
  borderRadius: '8px',
  '&.Mui-selected': {backgroundColor: '#f0f0f0'},
  '&:hover': {backgroundColor: '#f8f8f8'},
}));

const StyledListItemText = styled(ListItemText)(({theme}) => ({
  '& .MuiTypography-root': {fontWeight: 500},
}));

const StyledSubListItemText = styled(ListItemText)(({theme}) => ({
  '& .MuiTypography-root': {
    fontSize: '0.875rem',
    marginLeft: theme.spacing(2),
  },
}));

const legalItems = [
  {label: 'Terms of Use', path: '/legals/terms'},
  {label: 'Privacy Policy', path: '/legals/privacy'},
  {label: 'Affiliate Policy', path: '/legals/affiliate'},
  {label: 'Return & Refund Policy', path: '/legals/refund'},
  {label: 'Infringement Policy', path: '/legals/infringement'},
  {label: 'Agreements', path: '/legals/agreements'},
  {label: 'Licence', path: '/legals/licence'},
  {label: 'Disclaimer', path: '/legals/disclaimer'},
  {label: 'Guidelines', path: '/legals/guidelines'},
];

const AccountMenu = () => {
  const [openMyAccount, setOpenMyAccount] = useState(false);
  const [openLoginSecurity, setOpenLoginSecurity] = useState(false);
  const [openHelpSupport, setOpenHelpSupport] = useState(false);
  const [openLegal, setOpenLegal] = useState(false);
  const [openDeactivateDialog, setOpenDeactivateDialog] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const {data: user} = useGetUserQuery();

  const handleDeactivateDialogClose = useCallback(() => {
    setOpenDeactivateDialog((prev: boolean) => !prev);
  }, []);

  const handleLogout = async () => {
    dispatch(unsetCredentials());
    dispatch(unsetMonitor());
    dispatch(apiSlice.util.invalidateTags([ApiTagTypes.User]));
    router.push('/login');
  };

  useEffect(() => {
    if (['/profile', '/address'].some(p => pathname.includes(p))) {
      setOpenMyAccount(true);
    }
    if (pathname.includes('/change-password')) {
      setOpenMyAccount(true);
      setOpenLoginSecurity(true);
    }

    if (
      ['/faq', '/help', '/customer-service'].some(p => pathname.includes(p))
    ) {
      setOpenHelpSupport(true);
    }
    if (pathname.startsWith('/legals')) {
      setOpenLegal(true);
    }
  }, [pathname]);

  const renderSubItem = (
    label: string,
    path?: string,
    color = '#9A2D8E',
    onClick?: () => void,
  ) =>
    path ? (
      <ListItemButton
        sx={{pl: 4}}
        selected={pathname.includes(path)}
        onClick={() => router.push(path)}
        key={label}
      >
        <StyledSubListItemText primary={label} />
      </ListItemButton>
    ) : onClick ? (
      <ListItemButton sx={{pl: 4}} onClick={onClick} key={label}>
        <StyledSubListItemText primary={label} />
      </ListItemButton>
    ) : (
      <ListItem sx={{pl: 4}} key={label}>
        <StyledSubListItemText primary={label} />
      </ListItem>
    );

  return (
    <StyledCard>
      <List component="nav">
        {[
          {label: 'Orders', path: '/orders'},
          {label: 'Wish List', path: '/wish-list'},
          {label: 'Messages', path: '/message'},
        ].map(({label, path}) => (
          <StyledListItemButton
            key={label}
            selected={pathname.includes(path)}
            onClick={() => router.push(path)}
          >
            <StyledListItemText primary={label} />
          </StyledListItemButton>
        ))}

        <StyledListItemButton onClick={() => setOpenMyAccount(o => !o)}>
          <StyledListItemText primary="My Account" />
          {openMyAccount ? <ArrowDown2 /> : <ArrowRight2 />}
        </StyledListItemButton>
        <Collapse in={openMyAccount} timeout="auto" unmountOnExit>
          <List disablePadding>
            {renderSubItem('My Profile', '/profile')}
            {renderSubItem('Manage Address', '/address')}
            <StyledListItemButton
              sx={{pl: 4}}
              onClick={() => setOpenLoginSecurity(p => !p)}
            >
              <StyledListItemText primary="Login and Security" sx={{pl: 2}} />
              {openLoginSecurity ? <ArrowDown2 /> : <ArrowRight2 />}
            </StyledListItemButton>
            <Collapse in={openLoginSecurity} timeout="auto" unmountOnExit>
              <List disablePadding sx={{pl: 2}}>
                {renderSubItem(
                  'Change Password',
                  '/change-password',
                  '#00008B',
                )}
                {renderSubItem(
                  'Deactivate account',
                  undefined,
                  '#00008B',
                  handleDeactivateDialogClose,
                )}
              </List>
            </Collapse>
          </List>
        </Collapse>

        {/* Help and Support */}
        <StyledListItemButton onClick={() => setOpenHelpSupport(p => !p)}>
          <StyledListItemText primary="Help and Support" />
          {openHelpSupport ? <ArrowDown2 /> : <ArrowRight2 />}
        </StyledListItemButton>
        <Collapse in={openHelpSupport} timeout="auto" unmountOnExit>
          <List disablePadding>
            {renderSubItem('Help', '/help')}
            {renderSubItem('FAQ', '/faq')}
            {renderSubItem('Customer Service', '/customer-service')}
          </List>
        </Collapse>

        <ListItem sx={{pl: 2, mt: 1}}>
          <StyledListItemText primary="Start Selling" />
        </ListItem>

        {/* Legal */}
        <StyledListItemButton onClick={() => setOpenLegal(p => !p)}>
          <StyledListItemText primary="Legal" />
          {openLegal ? <ArrowDown2 /> : <ArrowRight2 />}
        </StyledListItemButton>
        <Collapse in={openLegal} timeout="auto" unmountOnExit>
          <List disablePadding>
            {legalItems.map(({label, path}) => renderSubItem(label, path))}
          </List>
        </Collapse>

        <StyledListItemButton onClick={handleLogout}>
          <StyledListItemText primary="Sign-out" />
        </StyledListItemButton>
      </List>
      <AlertDeactivateAccount
        id={user?.id ?? ''}
        open={openDeactivateDialog}
        handleClose={handleDeactivateDialogClose}
      />
    </StyledCard>
  );
};

export default AccountMenu;
