import {Order, OrderRelations, Cart, OrderLineItem, PromoCode, Address} from '../models';
import {PgDataSource} from '../datasources';
import {Getter, inject} from '@loopback/core';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {
  repository,
  BelongsToAccessor,
  HasManyRepositoryFactory,
} from '@loopback/repository';
import {CartRepository} from './cart.repository';
import {OrderLineItemRepository} from './order-line-item.repository';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {PromoCodeRepository} from './promo-code.repository';
import {AddressRepository} from './address.repository';

export class OrderRepository extends SequelizeUserModifyCrudRepositoryCore<
  Order,
  typeof Order.prototype.id,
  OrderRelations
> {
  public readonly cart: BelongsToAccessor<Cart, typeof Order.prototype.id>;

  public readonly orderLineItems: HasManyRepositoryFactory<
    OrderLineItem,
    typeof Order.prototype.id
  >;

  public readonly promoCode: BelongsToAccessor<
    PromoCode,
    typeof Order.prototype.id
  >;

  public readonly shippingAddress: BelongsToAccessor<Address, typeof Order.prototype.id>;

  public readonly billingAddress: BelongsToAccessor<Address, typeof Order.prototype.id>;

  constructor(
    @inject('datasources.pg') dataSource: PgDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('CartRepository')
    protected cartRepositoryGetter: Getter<CartRepository>,
    @repository.getter('OrderLineItemRepository')
    protected orderLineItemRepositoryGetter: Getter<OrderLineItemRepository>,
    @repository.getter('PromoCodeRepository')
    protected promoCodeRepositoryGetter: Getter<PromoCodeRepository>, @repository.getter('AddressRepository') protected addressRepositoryGetter: Getter<AddressRepository>,
  ) {
    super(Order, dataSource, getCurrentUser);
    this.billingAddress = this.createBelongsToAccessorFor('billingAddress', addressRepositoryGetter,);
    this.registerInclusionResolver('billingAddress', this.billingAddress.inclusionResolver);
    this.shippingAddress = this.createBelongsToAccessorFor('shippingAddress', addressRepositoryGetter,);
    this.registerInclusionResolver('shippingAddress', this.shippingAddress.inclusionResolver);
    this.promoCode = this.createBelongsToAccessorFor(
      'promoCode',
      promoCodeRepositoryGetter,
    );
    this.registerInclusionResolver(
      'promoCode',
      this.promoCode.inclusionResolver,
    );
    this.orderLineItems = this.createHasManyRepositoryFactoryFor(
      'orderLineItems',
      orderLineItemRepositoryGetter,
    );
    this.registerInclusionResolver(
      'orderLineItems',
      this.orderLineItems.inclusionResolver,
    );
    this.cart = this.createBelongsToAccessorFor('cart', cartRepositoryGetter);
    this.registerInclusionResolver('cart', this.cart.inclusionResolver);
  }
}
