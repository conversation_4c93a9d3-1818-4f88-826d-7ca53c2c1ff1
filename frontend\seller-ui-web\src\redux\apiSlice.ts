import type { BaseQueryFn, FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { ApiSliceIdentifier } from 'enums/api.enum';
import { AuthResData, setCredentials, unsetCredentials } from './auth/authSlice';
import { getBaseUrl } from './redux.helper';
import type { RootState } from './store';
import { ApiTagTypes } from './types';
import { getErrorMessage } from 'utils/hooks/useApiErrorHandler';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';

const authEndpoints = ['verifyOtp', 'login', 'sendOtp', 'signUp', 'createSignUpToken'];

/**
 * Base query function with re-Authentication handling and header preparation.
 * This function serves as an interceptor for API requests.
 *
 * @param args - The fetch arguments for the request.
 * @param api - The API object provided by `createApi`.
 * @param extraOptions - Extra options for the query.
 */
const RESULT_ERROR_STATUS = 401;
const FORBIDDEN_ERROR_STATUS = 403;
const baseQueryWithReauth: BaseQueryFn<
  {
    url: string;
    method?: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    body?: any;
    apiSliceIdentifier?: ApiSliceIdentifier;
  },
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const state = api.getState() as RootState;

  const baseUrl = getBaseUrl(state, args.apiSliceIdentifier);

  const baseQuery = fetchBaseQuery({
    baseUrl,
    prepareHeaders(headers, { getState }) {
      const token = (getState() as RootState).auth.accessToken;
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      } else {
        const guestToken = (getState() as RootState).auth.guestToken;
        if (guestToken) {
          headers.set('Authorization', `Bearer ${guestToken}`);
        }
      }
      headers.set('x-origin', 'ecomdukes-seller');

      return headers;
    }
  });

  let result = await baseQuery(args, api, extraOptions);
  if (result.error?.status === FORBIDDEN_ERROR_STATUS) {
    const baseUrl = window.location.origin; // dynamically gets base URL
    window.location.href = `${baseUrl}/unauthorized/403`;
  }
  if (result.error?.status === RESULT_ERROR_STATUS && !authEndpoints.includes(api.endpoint)) {
    // try to get a new token
    const refreshResult = await baseQuery(
      {
        url: getBaseUrl(state) + '/auth/token-refresh',
        method: 'POST',
        body: { refreshToken: (api.getState() as RootState).auth.refreshToken }
      },
      api,
      extraOptions
    );
    if (refreshResult.data) {
      api.dispatch(setCredentials(refreshResult.data as AuthResData));
      result = await baseQuery(args, api, extraOptions);
    } else {
      api.dispatch(unsetCredentials());
    }
  } else if (result.error) {
    const errorMessage = getErrorMessage(result.error);
    openSnackbar({
      message: errorMessage,
      open: true,
      variant: 'alert',
      alert: { color: 'error' }
    } as SnackbarProps);
  } else {
    // do nothing
  }
  return result;
};

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  endpoints: () => ({}),
  tagTypes: Object.values(ApiTagTypes)
});
