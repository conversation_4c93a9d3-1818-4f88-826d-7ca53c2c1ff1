import {injectable, /* inject, */ BindingScope} from '@loopback/core';
import {PageSection, PageSectionBulkDto, PageSectionDto} from '../models';
import {repository} from '@loopback/repository';
import {PageSectionRepository, SectionItemRepository} from '../repositories';
import {HttpErrors} from '@loopback/rest';

@injectable({scope: BindingScope.TRANSIENT})
export class PageSectionService {
  constructor(
    @repository(PageSectionRepository)
    private readonly pageSectionRepository: PageSectionRepository,
    @repository(SectionItemRepository)
    private readonly sectionItemRepository: SectionItemRepository,
  ) {}

  async createPageSection(dto: PageSectionDto): Promise<PageSection> {
    const {items, pageType, ...rest} = dto;
    const transaction =
      await this.pageSectionRepository.dataSource.beginTransaction();
    try {
      // STEP 1: Check if section exists by pageType
      let pageSection = await this.pageSectionRepository.findOne({
        where: {
          pageType, // assuming `pageType` is unique per section
          id: dto.id,
        },
      });

      // STEP 2: If section doesn't exist, create it
      if (!pageSection) {
        pageSection = await this.pageSectionRepository.create(
          {pageType, ...rest},
          {transaction},
        );
      } else {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const {id, ...restExcludeId} = dto;
        await this.pageSectionRepository.updateById(
          pageSection.id,
          restExcludeId,
          {
            transaction,
          },
        );
        await this.sectionItemRepository.deleteAllHard(
          {pageSectionId: pageSection.id},
          {transaction},
        );
      }

      // STEP 3: Create section items attached to the section
      await this.sectionItemRepository.createAll(
        items.map(item => ({
          ...item,
          pageSectionId: pageSection.id,
        })),
        {transaction},
      );
      await transaction.commit();
      return pageSection;
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  async createBulkPageSection(dto: PageSectionBulkDto): Promise<PageSection[]> {
    const result: PageSection[] = [];
    for (const section of dto.sections) {
      // eslint-disable-next-line no-await-in-loop
      const created = await this.createPageSection(section);
      result.push(created);
    }
    return result;
  }

  async reorderPageSection(
    id: string,
    updates: Partial<PageSection>,
  ): Promise<void> {
    if (!updates.displayOrder) {
      throw HttpErrors.BadRequest(
        'New displayOrder is required for reordering',
      );
    }

    const section = await this.pageSectionRepository.findById(id);
    if (!section) {
      throw new HttpErrors.NotFound(`Section with ID ${id} not found`);
    }

    const currentOrder = section.displayOrder;
    const newOrder = updates.displayOrder;

    if (currentOrder === newOrder) return;

    const transaction =
      await this.pageSectionRepository.dataSource.beginTransaction();
    try {
      const allSections = await this.pageSectionRepository.find(
        {
          where: {pageType: section.pageType},
          order: ['displayOrder ASC'],
        },
        {transaction},
      );

      const reorderPromises: Promise<unknown>[] = [];

      if (newOrder < currentOrder) {
        // Shift sections between newOrder and currentOrder - 1 down by 1
        for (const s of allSections) {
          if (
            s.id !== id &&
            s.displayOrder >= newOrder &&
            s.displayOrder < currentOrder
          ) {
            reorderPromises.push(
              this.pageSectionRepository.updateById(
                s.id,
                {
                  displayOrder: s.displayOrder + 1,
                },
                {transaction},
              ),
            );
          }
        }
      } else {
        // Shift sections between currentOrder + 1 and newOrder up by 1
        for (const s of allSections) {
          if (
            s.id !== id &&
            s.displayOrder <= newOrder &&
            s.displayOrder > currentOrder
          ) {
            reorderPromises.push(
              this.pageSectionRepository.updateById(
                s.id,
                {
                  displayOrder: s.displayOrder - 1,
                },
                {transaction},
              ),
            );
          }
        }
      }

      reorderPromises.push(
        this.pageSectionRepository.updateById(
          id,
          {displayOrder: newOrder},
          {transaction},
        ),
      );

      await Promise.all(reorderPromises);
      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  }
}
