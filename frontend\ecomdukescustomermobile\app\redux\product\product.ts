import {ReactElement, ReactNode} from 'react';
import {Seller} from '../../types/seller';
import {Customer} from '../../types/customer';

export interface Asset {
  deleted: boolean;
  deletedOn: string;
  deletedBy: string;
  createdOn: string;
  modifiedOn: string;
  createdBy: string;
  modifiedBy: string;
  id: string;
  name: string;
  type: string;
  mimeType: string;
  width: number;
  height: number;
  fileSize: number;
  source: string;
  preview: string;
  focalPoint: string;
  previewUrl: string;
}

export interface TaxCategory {
  name: string;
  id: string;
  isDefault: boolean;
}

export interface ProductDetail {
  id: string;
  productId: string;
  productVariantId: string;
  details: string;
  foreignKey: string;
}

export interface ProductSpecification {
  id: string;
  name: string;
  value: string;
}

export interface ProductReturnPolicy {
  id: string;
  returnPolicy: string;
  productId: string;
  productVariantId: string;
  product: string;
  foreignKey: string;
}

export interface ProductSuitability {
  id: string;
  suitableFor: string;
  productId: string;
  productVariantId: string;
  product: string;
  foreignKey: string;
}

export interface ProductMoreInfo {
  id: string;
  info: string;
  productId: string;
  productVariantId: string;
  foreignKey: string;
}

export interface ProductDisclaimer {
  id: string;
  disclaimer: string;
  productId: string;
  productVariantId: string;
}

export interface ProductCustomField {
  id: string;
  productId: string;
  productVariantId: string | null;
  name: string;
  label: string;
  placeholder: string;
  fieldType:
    | 'text'
    | 'number'
    | 'select'
    | 'textarea'
    | 'checkbox'
    | 'radio'
    | string; // extendable
  isRequired: boolean;
  validationRegex: string | null;
}

export interface ProductPricing {
  id: string;
  modifiedBy: string;
  modifiedOn: string; // ISO date string, can be typed as `Date` if you're parsing it
  mrp: string;
  price: string;
  productVariantId: string;
  currencyCode: string;
}
export interface ProductVariantAsset {
  id?: string;
  position: number;
  assetId: string;
  productVariantId: string;
  asset?: Asset;
}
export interface ProductOptionGroup {
  id: string;
  code: string;
  name: string;
  unit: string;
  productId: string;
}
export interface ProductOption {
  id: string;
  name: string;
  code: string;
  productOptionGroupId: string;
  productOptionGroup: ProductOptionGroup;
}
export interface ProductVariantOption {
  id: string;
  productVariantId: string;
  productOptionId: string;
  productOption: ProductOption;
}
export interface ProductVariant {
  deleted: boolean;
  deletedOn: string;
  deletedBy: string;
  createdOn: string;
  modifiedOn: string;
  createdBy: string;
  modifiedBy: string;
  id: string;
  name: string;
  enabled: boolean;
  sku: string;
  outOfStockThreshold: number;
  trackInventory: string;
  productId: string;
  featuredAssetId: string;
  taxCategoryId: string;
  product: Product;
  taxCategory: TaxCategory;
  productVariantAssets: ProductVariantAsset[];
  productSpecifications: ProductSpecification[];
  productDetail: ProductDetail;
  productMoreInfo: ProductMoreInfo;
  productDisclaimer: ProductDisclaimer;
  productReturnPolicy: ProductReturnPolicy;
  productSuitability: ProductSuitability;
  productTermsAndCondition: ProductTermsAndCondition;
  productBoxContents: ProductBoxContent[];
  productUniqueness: ProductUniqueness;
  productPersonalWork: ProductPersonalWork;
  productVariantOptions: ProductVariantOption[];
  featuredAsset: Asset;
  assets: Asset[];
  productCustomizationFields: ProductCustomField[];
  productVariantPrice: ProductPricing;
  seller?: Seller;
  wishlist?: Wishlist;
  reviews?: Review[];
}
export interface Wishlist {
  id?: string;
  customerId: string;
  productVariantId: string;
  productVariant?: ProductVariant;
}
export enum ReviewStatus {
  APPROVED = 'approved',
  HIDDEN = 'hidden',
}
export interface Review {
  createdOn: any;
  id?: string;
  rating: number;
  review?: string;
  deleted: boolean;
  reviewAssets?: string[];
  customerId: string;
  productVariantId: string;
  orderLineItemId: string;
  customer?: Customer;
  previewAssets?: string[];
}
export interface ProductTermsAndCondition {
  id: string;
  terms: string;
  productId: string;
  productVariantId: string;
  product: string;
  foreignKey: string;
}

export interface ProductBoxContent {
  id: string;
  itemName: string;
  quantity: number;
  productId: string;
  productVariantId: string;
  product: string;
  foreignKey: string;
}

export interface ProductUniqueness {
  id: string;
  uniqueness: string;
  productId: string;
  productVariantId: string;
  product: string;
  foreignKey: string;
}

export interface ProductPersonalWork {
  id: string;
  workLevel: string;
  productId: string;
  productVariantId: string;
  product: string;
  foreignKey: string;
}
export interface ProductAsset {
  id: string;
  assetId: string;
  productId: string;
  position: number;
  asset: Asset;
}
export interface Collection {
  id: string;
  name: string;
}

export interface Product {
  deleted: boolean;
  deletedOn: string;
  deletedBy: string;
  createdOn: string;
  modifiedOn: string;
  createdBy: string;
  modifiedBy: string;
  id: string;
  name: string;
  description: string;
  productId: string;
  slug: string;
  enabled: boolean;
  sellerId: string;
  featuredAssetId: string;
  taxCategory: TaxCategory;
  productSpecifications: ProductSpecification[];
  productDetails: ProductDetail[];
  productMoreInfos: ProductMoreInfo[];
  productDisclaimers: ProductDisclaimer[];
  productReturnPolicies: ProductReturnPolicy[];
  productSuitabilities: ProductSuitability[];
  productTermsAndConditions: ProductTermsAndCondition[];
  productBoxContents: ProductBoxContent[];
  productUniquenesses: ProductUniqueness[];
  productPersonalWorks: ProductPersonalWork[];
  featuredAsset: Asset;
  productAssets: ProductAsset[];
  productVariants: ProductVariant[];
  productCustomizationFields: ProductCustomField[];
  collectionId?: string;
  collection?: Collection;
  rating: number;
  isGiftWrapAvailable: boolean;
  isGiftWrapCharge: string;
}

export interface ProductTabsProps {
  children?: ReactElement | ReactNode | string;
  value: string | number;
  index: number;
}

export interface CustomizationValue {
  id?: string;
  value: string;
  cartId: string;
  orderId: string;
  customizationFieldId: string;
}

export interface ProductCustomField {
  id: string;
  productId: string;
  productVariantId: string | null;
  name: string;
  label: string;
  placeholder: string;
  fieldType:
    | 'text'
    | 'number'
    | 'select'
    | 'textarea'
    | 'checkbox'
    | 'radio'
    | string; // extendable
  isRequired: boolean;
  validationRegex: string | null;
  productCustomizationOptions?: ProductCustomizationOptions[];
  customizationValue?: CustomizationValue;
}

export interface ProductCustomizationOptions {
  id: string;
  value: string;
  label: string;
  productCustomizationFieldId: string;
}
