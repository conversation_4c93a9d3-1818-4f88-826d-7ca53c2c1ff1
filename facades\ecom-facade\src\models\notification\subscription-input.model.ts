// src/models/subscription-input.model.ts
import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

@model({name: 'subscriptions'})
export class SubscriptionInput extends UserModifiableEntity {
  @property({
    type: 'string',
    required: true,
  })
  topic: string;

  @property({
    type: 'string',
    required: false,
  })
  email?: string;

  @property({
    type: 'string',
    required: false,
  })
  fcmToken?: string;

  @property({
    type: 'string',
    required: true,
  })
  zohoListKey: string;

  @property({
    type: 'string',
    required: true,
  })
  zohoTopicId: string;

  constructor(data?: Partial<SubscriptionInput>) {
    super(data);
  }
}
