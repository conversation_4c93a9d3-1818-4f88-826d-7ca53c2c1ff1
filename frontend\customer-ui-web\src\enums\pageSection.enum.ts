export enum SectionType {
  CAROUSEL = 'carousel',
  FEATURED_PRODUCTS = 'featured_products',
  BANNER = 'banner',
  TEXT_BLOCK = 'text_block',
  FEATURED_COLLECTION = 'featured-collection',
  PRODUCT_CARDS = 'product-cards',
  RECENTLY_VIEWED = 'recently-viewed',
  MOST_VIEWED = 'most-viewed',
  TOP_SELLING = 'top-selling',
  PRODUCT_FILTER = 'product-filter',
  ALL_CATEGORIES = 'all-categories',
  FACETS = 'facets',
  GIFT_PRODUCTS = 'gift-products',
}

export enum SectionItemType {
  PRODUCT = 'product',
  CATEGORY = 'category',
  IMAGE = 'image',
  CUSTOM_CARD = 'custom_card',
  COLLECTION = 'collection',
  TEXT = 'text',
  FACET_VALUES = 'facet-values',
}

export enum CardStyle {
  BASIC = 'basic',
  IMAGE_ONLY = 'image-only',
  IMAGE_TITLE = 'image-title',
  IMAGE_TITLE_SUBTITLE = 'image-title-subtitle',
  OFFER_CARD = 'offer-card',
  OFFER_CARD_INVERSE = 'offer-card-inverse',
  PRODUCT_CARD = 'product-card',
  AVATAR_WITH_TITLE = 'avatar-with-title',
}

export enum PageType {
  HOME = 'home',
  SELL_ON_ECOMDUKES = 'sell-on-ecomdukes',
  ABOUT_US = 'about-us',
  CONTACT_US = 'contact-us',
}

export const PRODUCT_SECTION_TYPES = [
  SectionType.PRODUCT_CARDS,
  SectionType.FEATURED_PRODUCTS,
];
