export interface Plan {
  id: string;
  name: string;
  key: string;
  status: string;
  currency: string;
  amount: number;
  salesThreshold: number;
}

export interface PlanPricing {
  id: string;
  planId: string;
  minSalesThreshold: number;
  maxSalesThreshold: number;
  price: number;
}

export interface PlanWithRelations {
  id: string;
  name: string;
  key: string;
  status: string;
  currency: string;
  planPricings: PlanPricing[];
  planFeatureValues: PlanFeatureValue[];
}

interface PlanFeatureValue {
  id: string;
  planId: string;
  featureValueId: string;
  featureValue: FeatureValue;
}

export interface FeatureValue {
  id: string;
  value: string;
  key: string;
  valueType: string;
  featureId: string;
  feature: Feature;
}

export interface Feature {
  id: string;
  name: string;
  key: string;
  category: string;
  featureValues?: FeatureValue[];
}

export interface FeatureFormValues {
  id: string;
  name: string;
  featureValues?: FeatureValue[];
}
