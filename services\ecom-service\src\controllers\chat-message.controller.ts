import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {ChatMessage} from '../models';
import {ChatMessageRepository} from '../repositories';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';

const basePath = '/chat-messages';

export class ChatMessageController {
  constructor(
    @repository(ChatMessageRepository)
    public chatMessageRepository: ChatMessageRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateChatMessage]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'ChatMessage model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(ChatMessage)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ChatMessage, {
            title: 'NewChatMessage',
            exclude: ['id'],
          }),
        },
      },
    })
    chatMessage: Omit<ChatMessage, 'id'>,
  ): Promise<ChatMessage> {
    return this.chatMessageRepository.create(chatMessage);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewChatMessage]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'ChatMessage model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(
    @param.where(ChatMessage) where?: Where<ChatMessage>,
  ): Promise<Count> {
    return this.chatMessageRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewChatMessage]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of ChatMessage model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ChatMessage, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(ChatMessage) filter?: Filter<ChatMessage>,
  ): Promise<ChatMessage[]> {
    return this.chatMessageRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateChatMessage]})
  @patch(basePath)
  @response(STATUS_CODE.OK, {
    description: 'ChatMessage PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ChatMessage, {partial: true}),
        },
      },
    })
    chatMessage: ChatMessage,
    @param.where(ChatMessage) where?: Where<ChatMessage>,
  ): Promise<Count> {
    return this.chatMessageRepository.updateAll(chatMessage, where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewChatMessage]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'ChatMessage model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(ChatMessage, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(ChatMessage, {exclude: 'where'})
    filter?: FilterExcludingWhere<ChatMessage>,
  ): Promise<ChatMessage> {
    return this.chatMessageRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateChatMessage]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ChatMessage PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(ChatMessage, {partial: true}),
        },
      },
    })
    chatMessage: ChatMessage,
  ): Promise<void> {
    await this.chatMessageRepository.updateById(id, chatMessage);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteChatMessage]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'ChatMessage DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.chatMessageRepository.deleteById(id);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewChatMessage]})
  @get('/chat-messages/my')
  @response(STATUS_CODE.OK, {
    description: 'Array of ChatMessage model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(ChatMessage, {includeRelations: true}),
        },
      },
    },
  })
  async getMyMessages(): Promise<ChatMessage[]> {
    return this.chatMessageRepository.find();
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewChatMessage]})
  @patch(`${basePath}/mark-read/{chatId}`)
  @response(STATUS_CODE.OK, {
    description: 'ChatMessage PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async markAsRead(@param.path.string('chatId') chatId: string): Promise<void> {
    await this.chatMessageRepository.updateAll(
      {read: true},
      {chatId, read: false},
    );
  }
}
