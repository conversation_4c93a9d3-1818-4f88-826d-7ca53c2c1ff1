import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {UserFcm} from '../models';
import {UserFcmRepository} from '../repositories';
import {UserFcmHelperServiceService} from '../services';
import {service} from '@loopback/core';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {PermissionKeys} from '@local/core';
import {authorize} from 'loopback4-authorization';

export class UserFcmControllerController {
  constructor(
    @repository(UserFcmRepository)
    public userFcmRepository: UserFcmRepository,
    @service(UserFcmHelperServiceService)
    public userFcmHelperService: UserFcmHelperServiceService,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCampaign]})
  @post('/user-fcms')
  @response(200, {
    description: 'UserFcm model instance',
    content: {'application/json': {schema: getModelSchemaRef(UserFcm)}},
  })
  async create(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserFcm, {
            title: 'NewUserFcm',
            exclude: ['id'],
          }),
        },
      },
    })
    userFcm: Omit<UserFcm, 'id'>,
  ): Promise<UserFcm> {
    return this.userFcmHelperService.createOrUpdateFcm(userFcm);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCampaign]})
  @get('/user-fcms/count')
  @response(200, {
    description: 'UserFcm model count',
    content: {'application/json': {schema: CountSchema}},
  })
  async count(@param.where(UserFcm) where?: Where<UserFcm>): Promise<Count> {
    return this.userFcmRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewCampaign]})
  @get('/user-fcms')
  @response(200, {
    description: 'Array of UserFcm model instances',
    content: {
      'application/json': {
        schema: {
          type: 'array',
          items: getModelSchemaRef(UserFcm, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(UserFcm) filter?: Filter<UserFcm>,
  ): Promise<UserFcm[]> {
    return this.userFcmRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateCampaign]})
  @patch('/user-fcms')
  @response(200, {
    description: 'UserFcm PATCH success count',
    content: {'application/json': {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserFcm, {partial: true}),
        },
      },
    })
    userFcm: UserFcm,
    @param.where(UserFcm) where?: Where<UserFcm>,
  ): Promise<Count> {
    return this.userFcmRepository.updateAll(userFcm, where);
  }

  @get('/user-fcms/{id}')
  @response(200, {
    description: 'UserFcm model instance',
    content: {
      'application/json': {
        schema: getModelSchemaRef(UserFcm, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(UserFcm, {exclude: 'where'})
    filter?: FilterExcludingWhere<UserFcm>,
  ): Promise<UserFcm> {
    return this.userFcmRepository.findById(id, filter);
  }

  @patch('/user-fcms/{id}')
  @response(204, {
    description: 'UserFcm PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(UserFcm, {partial: true}),
        },
      },
    })
    userFcm: UserFcm,
  ): Promise<void> {
    await this.userFcmRepository.updateById(id, userFcm);
  }

  @put('/user-fcms/{id}')
  @response(204, {
    description: 'UserFcm PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() userFcm: UserFcm,
  ): Promise<void> {
    await this.userFcmRepository.replaceById(id, userFcm);
  }

  @del('/user-fcms/{id}')
  @response(204, {
    description: 'UserFcm DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.userFcmRepository.deleteById(id);
  }
}
