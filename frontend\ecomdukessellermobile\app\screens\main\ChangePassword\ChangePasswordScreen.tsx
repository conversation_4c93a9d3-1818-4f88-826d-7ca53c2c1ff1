import {SafeAreaView, StyleSheet, View} from 'react-native';
import React, {useState} from 'react';
import {KeyboardAwareScrollView} from 'react-native-keyboard-controller';
import {colors} from '../../../theme/colors';
import styleConstants from '../../../theme/styleConstants';
import customColors from '../../../theme/customColors';
import {Text, TextInput} from 'react-native-paper';
import CustomTextInput from '../../../components/InputFields/Textinput';
import {useUpdatePasswordMutation} from '../../../redux/auth/authApiSlice';
import {useFormik} from 'formik';
import {changePasswordValidationSchema} from '../../../validations/auth';
import {useTypedSelector} from '../../../redux/appstore';
import CustomButton from '../../../components/CustomButton/ContainedButton';
import Toast from 'react-native-toast-message';

type Props = {};

const ChangePasswordScreen = (props: Props) => {
  const [passwordVisible, setPasswordVisible] = useState<boolean>(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] =
    useState<boolean>(false);
  const [newPasswordVisible, setNewPasswordVisible] = useState<boolean>(false);

  const [updatePassword, {isLoading}] = useUpdatePasswordMutation();
  const {userDetails} = useTypedSelector(state => state.auth);

  const _renderSeperator = () => {
    return (
      <View
        style={{
          borderBottomWidth: 1,
          borderBottomColor: customColors.borderGrey,
          marginBottom: styleConstants.spacing.l,
        }}
      />
    );
  };

  const formik = useFormik({
    initialValues: {
      old: '',
      password: '',
      confirm: '',
    },
    validationSchema: changePasswordValidationSchema,
    onSubmit: async () => {
      await updatePassword({
        username: userDetails?.username ?? '',
        oldPassword: formik.values.old,
        password: formik.values.password,
      }).unwrap();
      Toast.show({
        type: 'success',
        text1: 'Password updated successfully',
      });
    },
  });

  return (
    <SafeAreaView style={styles.mainContainer}>
      <KeyboardAwareScrollView
        nestedScrollEnabled
        style={styles.scrolllViewStyle}
        contentContainerStyle={styles.scrollviewContainer}>
        <Text
          variant="titleMedium"
          style={{
            padding: styleConstants.spacing.x20,
            paddingTop: 0,
            fontSize: 18,
            paddingLeft: 0,
          }}>
          {'Change Password'}
        </Text>
        {_renderSeperator()}
        <CustomTextInput
          title="Current Password*"
          value={formik.values.old}
          onChangeText={formik.handleChange('old')}
          onBlur={formik.handleBlur('old')}
          touched={formik.touched.old}
          errors={formik.errors.old}
          secureTextEntry={!passwordVisible}
          right={
            <TextInput.Icon
              icon={!passwordVisible ? 'eye-off' : 'eye'}
              onPress={() => setPasswordVisible(!passwordVisible)}
            />
          }
        />
        <CustomTextInput
          title="New Password*"
          value={formik.values.password}
          onChangeText={formik.handleChange('password')}
          onBlur={formik.handleBlur('password')}
          touched={formik.touched.password}
          errors={formik.errors.password}
          secureTextEntry={!newPasswordVisible}
          right={
            <TextInput.Icon
              icon={!newPasswordVisible ? 'eye-off' : 'eye'}
              onPress={() => setNewPasswordVisible(!newPasswordVisible)}
            />
          }
        />
        <CustomTextInput
          title="Confirm Password*"
          value={formik.values.confirm}
          onChangeText={formik.handleChange('confirm')}
          onBlur={formik.handleBlur('confirm')}
          touched={formik.touched.confirm}
          errors={formik.errors.confirm}
          secureTextEntry={!confirmPasswordVisible}
          right={
            <TextInput.Icon
              icon={!confirmPasswordVisible ? 'eye-off' : 'eye'}
              onPress={() => setConfirmPasswordVisible(!confirmPasswordVisible)}
            />
          }
        />
        <CustomButton
          title="Update Password"
          disabled={isLoading}
          loading={isLoading}
          onPress={() => formik.handleSubmit()}
        />
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default ChangePasswordScreen;

const styles = StyleSheet.create({
  mainContainer: {
    backgroundColor: colors.surface,
    flex: 1,
    justifyContent: 'center',
  },
  scrolllViewStyle: {
    margin: styleConstants.spacing.x20,
    borderRadius: styleConstants.borderRadii.b10,
  },
  scrollviewContainer: {
    backgroundColor: customColors.white,
    borderRadius: styleConstants.borderRadii.b10,
    padding: styleConstants.spacing.x20,
    flexGrow: 1,
  },
});
