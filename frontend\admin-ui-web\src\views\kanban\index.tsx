'use client';

import { useState, useEffect } from 'react';

// material-ui
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Skeleton from '@mui/material/Skeleton';
import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';

// third-party
import { DragDropContext, Droppable, DropResult } from '@hello-pangea/dnd';

// project-imports
import MainCard from 'components/MainCard';
import ScrollX from 'components/ScrollX';
import { ThemeMode } from 'config';

// API
import { useGetTicketsQuery, useUpdateTicketMutation } from 'redux/app/support/supportApiSlice';

// types
import { KanbanColumn, KanbanItem } from 'types/kanban';
import Columns from './Columns';
import ItemDetails from './ItemDetails';

// Ticket interfaces
export interface ITicket {
  id?: string;
  category: string;
  title: string;
  description: string;
  priority?: number;
  shortCode: string;
  status: TicketStatus;
  assignee?: string;
  attachments?: string;
  createdOn?: string;
  modifiedOn?: string;
  createdBy?: string;
  modifiedBy?: string;
  deleted?: boolean;
  deletedBy?: string;
  deletedOn?: string;
}

export enum TicketStatus {
  Open = 'Open',
  InProgress = 'InProgress',
  Resolved = 'Resolved',
  Closed = 'Closed'
}

// Helper function to map column ID to ticket status
const getTicketStatusFromColumnId = (columnId: string): TicketStatus => {
  switch (columnId) {
    case 'open':
      return TicketStatus.Open;
    case 'inprogress':
      return TicketStatus.InProgress;
    case 'resolved':
      return TicketStatus.Resolved;
    case 'closed':
      return TicketStatus.Closed;
    default:
      return TicketStatus.Open;
  }
};

// Helper function to map ticket status to column ID
// const getColumnIdFromTicketStatus = (status: TicketStatus): string => {
//   switch (status) {
//     case TicketStatus.Open:
//       return 'open';
//     case TicketStatus.InProgress:
//       return 'inprogress';
//     case TicketStatus.Resolved:
//       return 'resolved';
//     case TicketStatus.Closed:
//       return 'closed';
//     default:
//       return 'open';
//   }
// };

// Convert tickets to kanban items
const convertTicketToKanbanItem = (ticket: ITicket): KanbanItem => ({
  id: ticket.id || '',
  title: ticket.title,
  description: ticket.description,
  image: '', // No image for tickets
  priority: ticket.priority || 0,
  dueDate: ticket.createdOn || '',
  assignedTo: ticket.assignee || ''
  // Add other properties as needed
});

// Create columns based on ticket status
const createKanbanColumns = (tickets: ITicket[]): { columns: KanbanColumn[]; columnsOrder: string[] } => {
  const statusColumns = {
    [TicketStatus.Open]: {
      id: 'open',
      title: 'Open',
      itemIds: [] as string[]
    },
    [TicketStatus.InProgress]: {
      id: 'inprogress',
      title: 'In Progress',
      itemIds: [] as string[]
    },
    [TicketStatus.Resolved]: {
      id: 'resolved',
      title: 'Resolved',
      itemIds: [] as string[]
    },
    [TicketStatus.Closed]: {
      id: 'closed',
      title: 'Closed',
      itemIds: [] as string[]
    }
  };

  // Populate item IDs for each status
  tickets.forEach((ticket) => {
    if (ticket.id && statusColumns[ticket.status]) {
      statusColumns[ticket.status].itemIds.push(ticket.id);
    }
  });

  const columns = Object.values(statusColumns);
  const columnsOrder = columns.map((col) => col.id);

  return { columns, columnsOrder };
};

const getDragWrapper = () => ({
  p: 2.5,
  px: 0,
  bgcolor: 'transparent',
  display: 'flex',
  overflow: 'auto'
});

const heightOptions = [120, 100, 160, 80, 60];

// ==============================|| KANBAN - BOARD ||============================== //

export default function Board() {
  const theme = useTheme();
  const [tickets, setTickets] = useState<ITicket[]>([]);
  const [kanbanData, setKanbanData] = useState<{
    columns: KanbanColumn[];
    columnsOrder: string[];
    items: KanbanItem[];
  }>({
    columns: [],
    columnsOrder: [],
    items: []
  });

  // API hooks
  const { data: apiData, error, isLoading } = useGetTicketsQuery({});
  const [updateTicket] = useUpdateTicketMutation();

  // Initialize kanban data when API data is received
  useEffect(() => {
    if (apiData) {
      // Process the API response

      let convertedTickets: ITicket[] = [];
      let kanbanItems: KanbanItem[] = [];

      // Handle different possible API response structures
      if (apiData.items && Array.isArray(apiData.items)) {
        // If API returns items array
        convertedTickets = apiData.items.map((item: KanbanItem) => {
          // Try to determine status from column assignment
          let status = TicketStatus.Open;
          if (apiData.columns) {
            for (const column of apiData.columns) {
              if (column.itemIds.includes(item.id)) {
                status = getTicketStatusFromColumnId(column.id);
                break;
              }
            }
          }

          return {
            id: item.id,
            title: item.title,
            description: item.description,
            priority: item.priority || 0,
            assignee: item.assignedTo || '',
            createdOn: item.dueDate || '',
            category: 'General',
            shortCode: `TKT-${item.id.slice(-3)}`,
            status,
            createdBy: 'system'
          };
        });
        kanbanItems = apiData.items;
      } else if (Array.isArray(apiData)) {
        // If API returns array of tickets directly
        convertedTickets = apiData.map((ticket: any, index: number) => ({
          id: ticket.id || `ticket-${index}`,
          title: ticket.title || 'Untitled',
          description: ticket.description || '',
          priority: ticket.priority || 0,
          assignee: ticket.assignee || '',
          createdOn: ticket.createdOn || '',
          category: ticket.category || 'General',
          shortCode: ticket.shortCode || `TKT-${index}`,
          status: ticket.status || TicketStatus.Open,
          createdBy: ticket.createdBy || 'system'
        }));
        kanbanItems = convertedTickets.map(convertTicketToKanbanItem);
      } else {
        // Fallback: create some default structure
        convertedTickets = [];
        kanbanItems = [];
      }

      setTickets(convertedTickets);

      // Use API data structure if available, otherwise create from converted tickets
      if (apiData.columns && apiData.columnsOrder && kanbanItems.length > 0) {
        setKanbanData({
          columns: apiData.columns,
          columnsOrder: apiData.columnsOrder,
          items: kanbanItems
        });
      } else {
        // Fallback: create kanban structure from converted tickets
        const { columns, columnsOrder } = createKanbanColumns(convertedTickets);
        const items = convertedTickets.map(convertTicketToKanbanItem);

        setKanbanData({
          columns,
          columnsOrder,
          items
        });
      }
    } else if (!isLoading) {
      // If no API data and not loading, show empty kanban board
      const { columns, columnsOrder } = createKanbanColumns([]);
      setKanbanData({
        columns,
        columnsOrder,
        items: []
      });
    }
  }, [apiData, isLoading]);

  // Handle drag & drop
  const onDragEnd = async (result: DropResult) => {
    const { source, destination, draggableId, type } = result;

    if (!destination) return;
    if (destination.droppableId === source.droppableId && destination.index === source.index) return;

    if (type === 'column') {
      const newColumnsOrder = Array.from(kanbanData.columnsOrder);
      newColumnsOrder.splice(source.index, 1);
      newColumnsOrder.splice(destination.index, 0, draggableId);

      setKanbanData((prev) => ({
        ...prev,
        columnsOrder: newColumnsOrder
      }));
      return;
    }

    // Find source and destination columns
    const sourceColumn = kanbanData.columns.find((col) => col.id === source.droppableId);
    const destinationColumn = kanbanData.columns.find((col) => col.id === destination.droppableId);

    if (!sourceColumn || !destinationColumn) return;

    let newColumns: KanbanColumn[];

    if (sourceColumn === destinationColumn) {
      // Moving within same column
      const newItemIds = Array.from(sourceColumn.itemIds);
      newItemIds.splice(source.index, 1);
      newItemIds.splice(destination.index, 0, draggableId);

      const newSourceColumn = {
        ...sourceColumn,
        itemIds: newItemIds
      };

      newColumns = kanbanData.columns.map((column) => (column.id === newSourceColumn.id ? newSourceColumn : column));
    } else {
      // Moving between different columns
      const newSourceItemIds = Array.from(sourceColumn.itemIds);
      newSourceItemIds.splice(source.index, 1);

      const newDestinationItemIds = Array.from(destinationColumn.itemIds);
      newDestinationItemIds.splice(destination.index, 0, draggableId);

      const newSourceColumn = {
        ...sourceColumn,
        itemIds: newSourceItemIds
      };

      const newDestinationColumn = {
        ...destinationColumn,
        itemIds: newDestinationItemIds
      };

      newColumns = kanbanData.columns.map((column) => {
        if (column.id === newSourceColumn.id) return newSourceColumn;
        if (column.id === newDestinationColumn.id) return newDestinationColumn;
        return column;
      });

      // Update ticket status and call API
      const newStatus = getTicketStatusFromColumnId(destination.droppableId);
      const updatedTickets = tickets.map((ticket) => {
        if (ticket.id === draggableId) {
          return { ...ticket, status: newStatus };
        }
        return ticket;
      });

      setTickets(updatedTickets);

      // Call API to update ticket status
      try {
        const ticketToUpdate = kanbanData.items.find((item) => item.id === draggableId);
        if (ticketToUpdate) {
          await updateTicket({
            id: draggableId,
            data: {
              ...ticketToUpdate
              // Note: The API expects KanbanItem format, not ticket format
              // The status might be handled differently in the backend
            }
          }).unwrap();
        }
      } catch (error) {
        // Handle error silently for now - in production you might want to show a toast
        // and revert the UI changes
        // Error is logged to help with debugging
      }
    }

    setKanbanData((prev) => ({
      ...prev,
      columns: newColumns
    }));
  };

  // Show error state
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">Failed to load tickets. Please try again later.</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex' }}>
      <ScrollX>
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable droppableId="columns" direction="horizontal" type="column">
            {(provided) => (
              <MainCard
                border={false}
                ref={provided.innerRef}
                sx={{ bgcolor: 'transparent' }}
                contentSX={getDragWrapper()}
                {...provided.droppableProps}
              >
                {kanbanData.columnsOrder.map((columnId: string, index: number) => {
                  const column = kanbanData.columns.find((col) => col.id === columnId);
                  if (!column) return null;

                  return isLoading ? (
                    <MainCard
                      key={columnId}
                      content={false}
                      sx={{
                        p: 1.5,
                        margin: `0 ${16}px 0 0`,
                        minWidth: 250,
                        bgcolor: theme.palette.mode === ThemeMode.DARK ? 'background.default' : 'secondary.lighter'
                      }}
                    >
                      <Stack spacing={1.25}>
                        <Skeleton variant="rounded" width="100%" height={38} />
                        <Skeleton variant="rounded" width="100%" height={heightOptions[Math.floor(Math.random() * heightOptions.length)]} />
                        <Skeleton variant="rounded" width="100%" height={heightOptions[Math.floor(Math.random() * heightOptions.length)]} />
                        <Skeleton variant="rounded" width="100%" height={heightOptions[Math.floor(Math.random() * heightOptions.length)]} />
                      </Stack>
                    </MainCard>
                  ) : (
                    <Columns key={columnId} column={column} index={index} items={kanbanData.items} tickets={tickets} />
                  );
                })}
                {provided.placeholder}
              </MainCard>
            )}
          </Droppable>
        </DragDropContext>
      </ScrollX>
      <ItemDetails tickets={tickets} />
    </Box>
  );
}
