'use client';

import { useState, useEffect } from 'react';

// material-ui
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Skeleton from '@mui/material/Skeleton';
import Box from '@mui/material/Box';

// third-party
import { DragDropContext, Droppable, DropResult } from '@hello-pangea/dnd';

// project-imports
import MainCard from 'components/MainCard';
import ScrollX from 'components/ScrollX';
import { ThemeMode } from 'config';

// types
import { KanbanColumn, KanbanItem } from 'types/kanban';
import Columns from './Columns';
import ItemDetails from './ItemDetails';

// Ticket interfaces
export interface ITicket {
  id?: string;
  category: string;
  title: string;
  description: string;
  priority?: number;
  shortCode: string;
  status: TicketStatus;
  assignee?: string;
  attachments?: string;
  createdOn?: string;
  modifiedOn?: string;
  createdBy?: string;
  modifiedBy?: string;
  deleted?: boolean;
  deletedBy?: string;
  deletedOn?: string;
}

export enum TicketStatus {
  Open = 'Open',
  InProgress = 'InProgress',
  Resolved = 'Resolved',
  Closed = 'Closed'
}

// Dummy data
const dummyTickets: ITicket[] = [
  {
    id: 'ticket-1',
    category: 'Bug',
    title: 'Fix login authentication issue',
    description: 'Users are unable to login with correct credentials',
    priority: 1,
    shortCode: 'BUG-001',
    status: TicketStatus.Open,
    assignee: 'John Doe',
    createdOn: '2024-01-15T10:30:00Z',
    createdBy: 'admin'
  },
  {
    id: 'ticket-2',
    category: 'Feature',
    title: 'Add dark mode toggle',
    description: 'Implement dark mode functionality for better user experience',
    priority: 2,
    shortCode: 'FEAT-002',
    status: TicketStatus.InProgress,
    assignee: 'Jane Smith',
    createdOn: '2024-01-16T14:20:00Z',
    createdBy: 'admin'
  },
  {
    id: 'ticket-3',
    category: 'Enhancement',
    title: 'Improve dashboard performance',
    description: 'Optimize dashboard loading time and reduce memory usage',
    priority: 2,
    shortCode: 'ENH-003',
    status: TicketStatus.InProgress,
    assignee: 'Bob Johnson',
    createdOn: '2024-01-17T09:15:00Z',
    createdBy: 'admin'
  },
  {
    id: 'ticket-4',
    category: 'Bug',
    title: 'Fix table sorting functionality',
    description: 'Table columns are not sorting correctly',
    priority: 3,
    shortCode: 'BUG-004',
    status: TicketStatus.Resolved,
    assignee: 'Alice Brown',
    createdOn: '2024-01-18T11:45:00Z',
    createdBy: 'admin'
  },
  {
    id: 'ticket-5',
    category: 'Documentation',
    title: 'Update API documentation',
    description: 'Add missing endpoints and update existing documentation',
    priority: 3,
    shortCode: 'DOC-005',
    status: TicketStatus.Closed,
    assignee: 'Charlie Wilson',
    createdOn: '2024-01-19T16:00:00Z',
    createdBy: 'admin'
  },
  {
    id: 'ticket-6',
    category: 'Feature',
    title: 'Add export functionality',
    description: 'Allow users to export data in various formats',
    priority: 2,
    shortCode: 'FEAT-006',
    status: TicketStatus.Open,
    assignee: 'Diana Lee',
    createdOn: '2024-01-20T13:30:00Z',
    createdBy: 'admin'
  }
];

// Convert tickets to kanban items
const convertTicketToKanbanItem = (ticket: ITicket): KanbanItem => ({
  id: ticket.id || '',
  title: ticket.title,
  description: ticket.description,
  image: '', // No image for tickets
  priority: ticket.priority || 0,
  dueDate: ticket.createdOn || '',
  assignedTo: ticket.assignee || ''
  // Add other properties as needed
});

// Create columns based on ticket status
const createKanbanColumns = (tickets: ITicket[]): { columns: KanbanColumn[]; columnsOrder: string[] } => {
  const statusColumns = {
    [TicketStatus.Open]: {
      id: 'open',
      title: 'Open',
      itemIds: [] as string[]
    },
    [TicketStatus.InProgress]: {
      id: 'inprogress',
      title: 'In Progress',
      itemIds: [] as string[]
    },
    [TicketStatus.Resolved]: {
      id: 'resolved',
      title: 'Resolved',
      itemIds: [] as string[]
    },
    [TicketStatus.Closed]: {
      id: 'closed',
      title: 'Closed',
      itemIds: [] as string[]
    }
  };

  // Populate item IDs for each status
  tickets.forEach((ticket) => {
    if (ticket.id && statusColumns[ticket.status]) {
      statusColumns[ticket.status].itemIds.push(ticket.id);
    }
  });

  const columns = Object.values(statusColumns);
  const columnsOrder = columns.map((col) => col.id);

  return { columns, columnsOrder };
};

const getDragWrapper = () => ({
  p: 2.5,
  px: 0,
  bgcolor: 'transparent',
  display: 'flex',
  overflow: 'auto'
});

const heightOptions = [120, 100, 160, 80, 60];

// ==============================|| KANBAN - BOARD ||============================== //

export default function Board() {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [tickets, setTickets] = useState<ITicket[]>(dummyTickets);
  const [kanbanData, setKanbanData] = useState<{
    columns: KanbanColumn[];
    columnsOrder: string[];
    items: KanbanItem[];
  }>({
    columns: [],
    columnsOrder: [],
    items: []
  });

  // Initialize kanban data
  useEffect(() => {
    const { columns, columnsOrder } = createKanbanColumns(tickets);
    const items = tickets.map(convertTicketToKanbanItem);

    setKanbanData({
      columns,
      columnsOrder,
      items
    });

    // Simulate loading
    setTimeout(() => setLoading(false), 1000);
  }, [tickets]);

  // Handle drag & drop
  const onDragEnd = (result: DropResult) => {
    const { source, destination, draggableId, type } = result;

    if (!destination) return;
    if (destination.droppableId === source.droppableId && destination.index === source.index) return;

    if (type === 'column') {
      const newColumnsOrder = Array.from(kanbanData.columnsOrder);
      newColumnsOrder.splice(source.index, 1);
      newColumnsOrder.splice(destination.index, 0, draggableId);

      setKanbanData((prev) => ({
        ...prev,
        columnsOrder: newColumnsOrder
      }));
      return;
    }

    // Find source and destination columns
    const sourceColumn = kanbanData.columns.find((col) => col.id === source.droppableId);
    const destinationColumn = kanbanData.columns.find((col) => col.id === destination.droppableId);

    if (!sourceColumn || !destinationColumn) return;

    let newColumns: KanbanColumn[];

    if (sourceColumn === destinationColumn) {
      // Moving within same column
      const newItemIds = Array.from(sourceColumn.itemIds);
      newItemIds.splice(source.index, 1);
      newItemIds.splice(destination.index, 0, draggableId);

      const newSourceColumn = {
        ...sourceColumn,
        itemIds: newItemIds
      };

      newColumns = kanbanData.columns.map((column) => (column.id === newSourceColumn.id ? newSourceColumn : column));
    } else {
      // Moving between different columns
      const newSourceItemIds = Array.from(sourceColumn.itemIds);
      newSourceItemIds.splice(source.index, 1);

      const newDestinationItemIds = Array.from(destinationColumn.itemIds);
      newDestinationItemIds.splice(destination.index, 0, draggableId);

      const newSourceColumn = {
        ...sourceColumn,
        itemIds: newSourceItemIds
      };

      const newDestinationColumn = {
        ...destinationColumn,
        itemIds: newDestinationItemIds
      };

      newColumns = kanbanData.columns.map((column) => {
        if (column.id === newSourceColumn.id) return newSourceColumn;
        if (column.id === newDestinationColumn.id) return newDestinationColumn;
        return column;
      });

      // Update ticket status
      const updatedTickets = tickets.map((ticket) => {
        if (ticket.id === draggableId) {
          let newStatus: TicketStatus;
          switch (destination.droppableId) {
            case 'open':
              newStatus = TicketStatus.Open;
              break;
            case 'inprogress':
              newStatus = TicketStatus.InProgress;
              break;
            case 'resolved':
              newStatus = TicketStatus.Resolved;
              break;
            case 'closed':
              newStatus = TicketStatus.Closed;
              break;
            default:
              newStatus = ticket.status;
          }
          return { ...ticket, status: newStatus };
        }
        return ticket;
      });

      setTickets(updatedTickets);
    }

    setKanbanData((prev) => ({
      ...prev,
      columns: newColumns
    }));
  };

  return (
    <Box sx={{ display: 'flex' }}>
      <ScrollX>
        <DragDropContext onDragEnd={onDragEnd}>
          <Droppable droppableId="columns" direction="horizontal" type="column">
            {(provided) => (
              <MainCard
                border={false}
                ref={provided.innerRef}
                sx={{ bgcolor: 'transparent' }}
                contentSX={getDragWrapper()}
                {...provided.droppableProps}
              >
                {kanbanData.columnsOrder.map((columnId: string, index: number) => {
                  const column = kanbanData.columns.find((col) => col.id === columnId);
                  if (!column) return null;

                  return loading ? (
                    <MainCard
                      key={columnId}
                      content={false}
                      sx={{
                        p: 1.5,
                        margin: `0 ${16}px 0 0`,
                        minWidth: 250,
                        bgcolor: theme.palette.mode === ThemeMode.DARK ? 'background.default' : 'secondary.lighter'
                      }}
                    >
                      <Stack spacing={1.25}>
                        <Skeleton variant="rounded" width="100%" height={38} />
                        <Skeleton variant="rounded" width="100%" height={heightOptions[Math.floor(Math.random() * heightOptions.length)]} />
                        <Skeleton variant="rounded" width="100%" height={heightOptions[Math.floor(Math.random() * heightOptions.length)]} />
                        <Skeleton variant="rounded" width="100%" height={heightOptions[Math.floor(Math.random() * heightOptions.length)]} />
                      </Stack>
                    </MainCard>
                  ) : (
                    <Columns key={columnId} column={column} index={index} items={kanbanData.items} tickets={tickets} />
                  );
                })}
                {provided.placeholder}
              </MainCard>
            )}
          </Droppable>
        </DragDropContext>
      </ScrollX>
      <ItemDetails tickets={tickets} />
    </Box>
  );
}
