import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

@model({name: 'zoho_tokens'})
export class ZohoToken extends UserModifiableEntity {
  @property({
    type: 'string',
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  accessToken: string;

  @property({
    type: 'number',
  })
  expiresIn?: number;

  @property({
    type: 'string',
  })
  fetchedAt?: string;

  constructor(data?: Partial<ZohoToken>) {
    super(data);
  }
}

export interface ZohoTokenRelations {
  // describe navigational properties here
}

export type ZohoTokenWithRelations = ZohoToken & ZohoTokenRelations;
