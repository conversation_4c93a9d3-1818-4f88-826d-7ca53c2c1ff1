import {model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

@model({name: 'legals'})
export class Legal extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: false,
  })
  id?: string;

  @property({type: 'string', required: true})
  data: string;

  @property({type: 'string', required: true})
  category: string;

  @property({
    type: 'string',
    required: true,
  })
  type: string;
}

export interface LegalRelations {}
export type LegalWithRelations = Legal & LegalRelations;
