import {apiSlice} from '../apiSlice';
import {buildFilterParams, IFilter} from '../../types/api';
import {ApiSliceIdentifier} from '../../constants/enums';
import {ProductVariant} from '../product/product';
import {PageSection} from '../../types/page-section';

export const pageSectionApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getPageSections: builder.query<PageSection[], IFilter>({
      query: filter => ({
        url: '/page-sections',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    getMostViewedProducts: builder.query<ProductVariant[], IFilter>({
      query: filter => ({
        url: '/products/most-viewed',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    getRecentlyViewedProducts: builder.query<ProductVariant[], IFilter>({
      query: filter => ({
        url: '/products/recently-viewed',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),

    getTopSellingProducts: builder.query<ProductVariant[], IFilter>({
      query: filter => ({
        url: '/products/top-selling',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter),
        },
      }),
    }),
  }),
});

export const {
  useGetPageSectionsQuery,
  useLazyGetPageSectionsQuery,
  useGetMostViewedProductsQuery,
  useGetRecentlyViewedProductsQuery,
  useGetTopSellingProductsQuery,
} = pageSectionApiSlice;
