import {Model, model, property} from '@loopback/repository';
import {CustomerStatus} from '@local/core';

@model()
export class CustomerStatusUpdateDto extends Model {
  @property({
    type: 'string',
    required: true,
    jsonSchema: {
      enum: Object.values(CustomerStatus),
    },
  })
  status: CustomerStatus;

  constructor(data?: Partial<CustomerStatusUpdateDto>) {
    super(data);
  }
}

export interface CustomerStatusDtoRelations {
  // describe navigational properties here
}

export type CustomerStatusDtoWithRelations = CustomerStatusUpdateDto &
  CustomerStatusDtoRelations;
