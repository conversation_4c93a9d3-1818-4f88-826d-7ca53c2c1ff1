'use client';

import { useState, useEffect, useMemo, ReactNode } from 'react';

// next
import { useRouter } from 'next/navigation';

// material-ui
import Grid from '@mui/material/Grid';
import TextField from '@mui/material/TextField';
import InputLabel from '@mui/material/InputLabel';

// project-imports
import MainCard from 'components/MainCard';

// assets
import withPermission from 'hoc/withPermission';
import { PermissionKeys } from 'enums/permission-keys.enum';
import {
  Autocomplete,
  Box,
  CardActions,
  CircularProgress,
  FormControlLabel,
  FormHelperText,
  Radio,
  RadioGroup,
  Switch,
  Tooltip,
  Typography
} from '@mui/material';
import { useLazyGetSellersQuery } from 'redux/app/seller/sellerApiSlice';
import { SellerStatus } from 'enums/seller.enum';
import { useFormik } from 'formik';
import { initialProductValue } from 'constants/product';
import { productSchema } from '../../../../validations/product';
import { ProductDto } from 'types/product-dto';
import { useLazyGetCollectionsQuery } from 'redux/app/collections/collectionApiSlice';
import { AutoCompleteOption } from 'types/common';
import { ProductAsset } from './ProductAsset';
import {
  useCreateAssetMutation,
  useCreateProductMutation,
  useGetAssetsCountQuery,
  useGetAssetsQuery
} from 'redux/app/products/productApiSlice';
import { ProductVariantForm } from './ProductVariant';
import { ProductCustomizationForm } from './Customization';
import { ProductMetaForm } from './ProductMetaForm';
import { useLazyGetFacetsQuery } from 'redux/app/facet/facetApiSlice';
import { LoadingButton } from '@mui/lab';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { useGetTaxCategoriesQuery } from 'redux/app/tax/taxCategoryApiSlice';
import { ProductLegalForm } from './ProductLegalForm';
import { ProductHighlights } from './ProductHighlights';
import { Asset } from 'types/product';

// ==============================|| ECOMMERCE - ADD PRODUCT ||============================== //
type Props = {
  initialValue?: ProductDto;
  isEdit?: boolean;
};
function AddNewProduct({ initialValue }: Props) {
  const router = useRouter();
  const [inputValue, setInputValue] = useState<string>('');
  const [collectionInput, setCollectionInput] = useState<string>('');
  const [facetInput, setFacetInput] = useState<string>('');
  const [openGallery, setOpenGallery] = useState<boolean>(false);
  const [selectedAssets, setSelectedAssets] = useState<{ id: string; preview: string }[]>([]);
  const [, setTaxCategoryInput] = useState('');

  const [getSeller, { isLoading: sellerListLoading, data: sellers }] = useLazyGetSellersQuery({});
  const [getCollection, { isLoading: collectionLoading, data: collections }] = useLazyGetCollectionsQuery({});
  const { data: taxCategories, isLoading: taxCategoryLoading } = useGetTaxCategoriesQuery({});
  const [getFacets, { isLoading: facetLoading, data: facets }] = useLazyGetFacetsQuery({});
  const [addAsset, { isLoading: isAssetUploading }] = useCreateAssetMutation();
  const [addProduct, { isLoading: productCreateLoading }] = useCreateProductMutation();
  const [page, setPage] = useState(0);
  const [assets, setAssets] = useState<Asset[]>([]);
  const limit = 10;
  const { data: totalAssets } = useGetAssetsCountQuery();
  const { data: paginatedData, refetch: refetchAsset } = useGetAssetsQuery({ order: ['createdOn DESC'], limit, skip: page * limit });

  const fetchSellers = async () => {
    await getSeller({
      where: { status: SellerStatus.APPROVED },
      include: [
        {
          relation: 'userTenant',
          required: true,
          scope: {
            include: [
              {
                relation: 'user',
                required: true,
                scope: {
                  where: { or: [{ firstName: { ilike: `%${inputValue}%` } }, { lastName: { ilike: `%${inputValue}%` } }] },
                  required: true
                }
              }
            ]
          }
        }
      ]
    }).unwrap();
  };

  const fetchCollections = async () => {
    await getCollection({
      where: { name: { ilike: `%${collectionInput}%` } },
      include: [
        {
          relation: 'childrens'
        }
      ]
    }).unwrap();
  };

  const fetchFacets = async () => {
    await getFacets({
      where: { name: { ilike: `%${facetInput}%` } },
      include: [
        {
          relation: 'facetValues'
        }
      ]
    }).unwrap();
  };

  const collectionOptions: AutoCompleteOption[] = useMemo(() => {
    return collections?.map((item) => ({ label: item.name, value: item.id })) ?? [];
  }, [collections]);

  const taxCategoryOptions: AutoCompleteOption[] = useMemo(() => {
    return (
      taxCategories
        ?.filter((item): item is { name: string; id: string } => Boolean(item.id)) // remove undefined ids
        .map((item) => ({ label: item.name, value: item.id })) ?? []
    );
  }, [taxCategories]);

  const sellerOptions: AutoCompleteOption[] = useMemo(() => {
    return (
      sellers?.map((item) => ({ label: `${item?.userTenant?.user?.firstName} ${item?.userTenant?.user?.lastName}`, value: item.id })) ?? []
    );
  }, [sellers]);

  const facetOptions: AutoCompleteOption[] = useMemo(() => {
    return (
      facets?.flatMap((facet) =>
        facet?.facetValues?.map((fv) => ({
          label: `${facet.name} - ${fv.name}`,
          value: fv.id
        }))
      ) ?? []
    );
  }, [facets]);

  const handleSubmit = async (values: ProductDto) => {
    await addProduct(values).unwrap();
    openSnackbar({
      open: true,
      message: 'Product created successfully',
      variant: 'alert',
      alert: { color: 'success' }
    } as SnackbarProps);
    router.push('/products');
  };

  const formik = useFormik<ProductDto>({
    initialValues: initialValue ?? initialProductValue,
    validationSchema: productSchema,
    onSubmit: (values) => {
      const { isGiftWrapAvailable, isGiftWrapCharge, ...rest } = values;

      const payload = {
        ...rest,
        isGiftWrapAvailable,
        isGiftWrapCharge: isGiftWrapAvailable ? Number(isGiftWrapCharge ?? 0) : 0
      };

      handleSubmit(payload);
    }
  });

  const handleFileChange = async (file: File) => {
    if (!file) return;
    const formData = new FormData();
    formData.append('file', file);
    await addAsset(formData).unwrap();
    refetchAsset();
  };

  useEffect(() => {
    if (selectedAssets?.length) {
      const hasFeaturedAsset = selectedAssets.some((asset) => asset.id === formik.values.featuredAssetId);

      if (!hasFeaturedAsset) {
        formik.setFieldValue('featuredAssetId', selectedAssets[0].id);
      }
    } else {
      formik.setFieldValue('featuredAssetId', '');
    }
    formik.setFieldValue(
      'assets',
      selectedAssets.map((asset) => asset.id)
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedAssets]);

  useEffect(() => {
    if (!inputValue) return;

    const debounceTimeout = setTimeout(fetchSellers, 300);
    return () => clearTimeout(debounceTimeout);
  }, [inputValue]);

  useEffect(() => {
    if (!collectionInput) return;

    const debounceTimeout = setTimeout(fetchCollections, 300);
    return () => clearTimeout(debounceTimeout);
  }, [collectionInput]);

  useEffect(() => {
    if (!facetInput) return;

    const debounceTimeout = setTimeout(fetchFacets, 300);
    return () => clearTimeout(debounceTimeout);
  }, [facetInput]);

  useEffect(() => {
    if (paginatedData) {
      setAssets((prev) => {
        const assetMap = new Map();
        prev.forEach((asset) => assetMap.set(asset.id, asset));
        paginatedData.forEach((asset) => assetMap.set(asset.id, asset));
        return Array.from(assetMap.values());
      });
    }
  }, [paginatedData]);

  const hasMore = useMemo(() => {
    if (totalAssets?.count) {
      return totalAssets?.count > assets?.length;
    }
    return false;
  }, [assets, totalAssets]);

  const loadMoreAssets = () => {
    if (assets.length < (totalAssets?.count ?? 0)) {
      setPage((prev) => prev + 1);
    }
  };

  const showErrorSummary = formik.submitCount > 0 && Object.keys(formik.errors).length > 0;

  return (
    <MainCard>
      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={9}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <MainCard>
                  <Grid container spacing={1}>
                    <Grid item xs={12} sm={6} lg={6}>
                      <InputLabel sx={{ mb: 1 }}>Product Name</InputLabel>
                      <TextField
                        placeholder="Enter product name"
                        fullWidth
                        name="name"
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.name && Boolean(formik.errors.name)}
                        helperText={Boolean(formik.errors.name) && formik.touched.name && formik.errors.name}
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <InputLabel sx={{ mb: 1 }}>Seller</InputLabel>
                      <Autocomplete
                        freeSolo
                        options={sellerOptions}
                        getOptionLabel={(option: AutoCompleteOption | string) => (typeof option === 'string' ? option : option.label)}
                        loading={sellerListLoading}
                        onInputChange={(event, newInputValue) => {
                          setInputValue(newInputValue);
                        }}
                        onChange={(e, newValue: AutoCompleteOption | string | null) => {
                          formik.setFieldValue('sellerId', typeof newValue === 'string' ? newValue : (newValue?.value ?? ''));
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Search"
                            InputProps={{
                              ...params.InputProps,
                              endAdornment: (
                                <>
                                  {sellerListLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                  {params.InputProps.endAdornment}
                                </>
                              )
                            }}
                            onBlur={formik.handleBlur}
                            error={formik.touched.sellerId && Boolean(formik.errors.sellerId)}
                            name="sellerId"
                          />
                        )}
                      />
                      {Boolean(formik.errors.sellerId) && formik.touched.sellerId && formik.errors.sellerId && (
                        <FormHelperText error>{formik.errors.sellerId}</FormHelperText>
                      )}
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <InputLabel sx={{ mb: 1 }}>Collection</InputLabel>
                      <Autocomplete
                        freeSolo
                        options={collectionOptions}
                        getOptionLabel={(option: AutoCompleteOption | string) => (typeof option === 'string' ? option : option.label)}
                        loading={collectionLoading}
                        onInputChange={(event, newInputValue) => {
                          setCollectionInput(newInputValue);
                        }}
                        onChange={(e, newValue: AutoCompleteOption | string | null) => {
                          formik.setFieldValue('collectionId', typeof newValue === 'string' ? newValue : (newValue?.value ?? ''));
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Search"
                            InputProps={{
                              ...params.InputProps,
                              endAdornment: (
                                <>
                                  {collectionLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                  {params.InputProps.endAdornment}
                                </>
                              )
                            }}
                            onBlur={formik.handleBlur}
                            error={formik.touched.collectionId && Boolean(formik.errors.collectionId)}
                            name="collectionId"
                          />
                        )}
                      />
                      {Boolean(formik.errors.collectionId) && formik.touched.collectionId && formik.errors.collectionId && (
                        <FormHelperText error>{formik.errors.collectionId}</FormHelperText>
                      )}
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <InputLabel sx={{ mb: 1 }}>Tax Category</InputLabel>
                      <Autocomplete
                        freeSolo
                        options={taxCategoryOptions}
                        getOptionLabel={(option: AutoCompleteOption | string) => (typeof option === 'string' ? option : option.label)}
                        loading={taxCategoryLoading}
                        onInputChange={(event, newInputValue) => {
                          setTaxCategoryInput(newInputValue);
                        }}
                        onChange={(e, newValue: AutoCompleteOption | string | null) => {
                          formik.setFieldValue('taxCategoryId', typeof newValue === 'string' ? newValue : (newValue?.value ?? ''));
                        }}
                        renderInput={(params) => (
                          <TextField
                            {...params}
                            label="Search"
                            InputProps={{
                              ...params.InputProps,
                              endAdornment: (
                                <>
                                  {taxCategoryLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                  {params.InputProps.endAdornment}
                                </>
                              )
                            }}
                            onBlur={formik.handleBlur}
                            name="taxCategoryId"
                          />
                        )}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <InputLabel sx={{ mb: 1 }}>Description</InputLabel>
                      <TextField
                        placeholder="Description"
                        fullWidth
                        multiline
                        minRows={3}
                        name="description"
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.description && Boolean(formik.errors.description)}
                        helperText={Boolean(formik.errors.description) && formik.touched.description && formik.errors.description}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <InputLabel sx={{ mt: 1, ml: 1 }}>Is Gift Wrap Available?</InputLabel>
                      <RadioGroup
                        row
                        name="isGiftWrapAvailable"
                        value={formik.values.isGiftWrapAvailable ? 'yes' : 'no'}
                        onChange={(e) => {
                          const value = e.target.value === 'yes';
                          formik.setFieldValue('isGiftWrapAvailable', value);
                          if (!value) {
                            formik.setFieldValue('giftWrapCharge', '');
                          }
                        }}
                        onBlur={formik.handleBlur}
                        sx={{ ml: 2 }}
                      >
                        <FormControlLabel value="yes" control={<Radio />} label="Yes" />
                        <FormControlLabel value="no" control={<Radio />} label="No" />
                      </RadioGroup>
                      {formik.touched.isGiftWrapAvailable && Boolean(formik.errors.isGiftWrapAvailable) && (
                        <FormHelperText error>{formik.errors.isGiftWrapAvailable}</FormHelperText>
                      )}

                      {formik.values.isGiftWrapAvailable && (
                        <Box mt={1} ml={2}>
                          <TextField
                            fullWidth
                            type="number"
                            label="Gift Wrap Charge"
                            name="isGiftWrapCharge"
                            value={formik.values.isGiftWrapCharge}
                            onChange={(e) => {
                              const { value } = e.target;
                              formik.setFieldValue('isGiftWrapCharge', value === '' ? '' : Number(value));
                            }}
                            onBlur={formik.handleBlur}
                            error={formik.touched.isGiftWrapCharge && Boolean(formik.errors.isGiftWrapCharge)}
                            helperText={formik.touched.isGiftWrapCharge && formik.errors.isGiftWrapCharge}
                            sx={{
                              '& input[type=number]': {
                                '-moz-appearance': 'textfield'
                              },
                              '& input[type=number]::-webkit-outer-spin-button': {
                                '-webkit-appearance': 'none',
                                margin: 0
                              },
                              '& input[type=number]::-webkit-inner-spin-button': {
                                '-webkit-appearance': 'none',
                                margin: 0
                              }
                            }}
                          />
                        </Box>
                      )}
                    </Grid>

                    <Grid item xs={12} sm={6} lg={6}>
                      <InputLabel sx={{ mb: 1 }}>Average Weight (kg)</InputLabel>
                      <TextField
                        fullWidth
                        type="number"
                        placeholder="Enter average weight"
                        name="averageWeight"
                        value={formik.values.averageWeight || ''}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        error={formik.touched.averageWeight && Boolean(formik.errors.averageWeight)}
                        helperText={formik.touched.averageWeight && formik.errors.averageWeight}
                        sx={{
                          '& input[type=number]': {
                            '-moz-appearance': 'textfield'
                          },
                          '& input[type=number]::-webkit-outer-spin-button': {
                            '-webkit-appearance': 'none',
                            margin: 0
                          },
                          '& input[type=number]::-webkit-inner-spin-button': {
                            '-webkit-appearance': 'none',
                            margin: 0
                          }
                        }}
                      />
                    </Grid>
                  </Grid>
                </MainCard>
              </Grid>
              <Grid item xs={12}>
                <ProductAsset
                  assets={assets ?? []}
                  openGallery={openGallery}
                  toggleGallery={() => {
                    setOpenGallery(!openGallery);
                  }}
                  handleFileChange={handleFileChange}
                  isAssetUploading={isAssetUploading}
                  setSelectedAssets={setSelectedAssets}
                  selectedAssets={selectedAssets}
                  handleFeatureAssetChange={(id: string) => {
                    formik.setFieldValue('featuredAssetId', id);
                  }}
                  featuredAssetId={formik.values.featuredAssetId}
                  hasMore={hasMore}
                  loadMoreAssets={loadMoreAssets}
                  assetsCount={totalAssets?.count ?? 0}
                />
              </Grid>

              <Grid item xs={12}>
                <ProductVariantForm formik={formik} />
              </Grid>
              <Grid item xs={12}>
                <ProductCustomizationForm formik={formik} />
              </Grid>
              <Grid item xs={12}>
                <ProductMetaForm formik={formik} />
              </Grid>
              <Grid item xs={12}>
                <ProductHighlights formik={formik} />
              </Grid>

              <Grid item xs={12}>
                <ProductLegalForm formik={formik} />
              </Grid>
            </Grid>
          </Grid>
          <Grid item xs={12} sm={3}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <MainCard title="Product Status">
                  <Box>
                    <FormControlLabel
                      control={<Switch inputProps={{ 'aria-label': 'Enabled switch' }} defaultChecked checked={formik.values.enabled} />}
                      label="Enabled"
                      name="enabled"
                      onChange={() => {
                        formik.setFieldValue('enabled', !formik.values.enabled);
                      }}
                    />
                  </Box>
                </MainCard>
              </Grid>
              <Grid item xs={12}>
                <MainCard title="Facet">
                  <Box>
                    <InputLabel sx={{ mb: 1 }}>Facet</InputLabel>
                    <Autocomplete
                      freeSolo
                      multiple
                      options={facetOptions}
                      getOptionLabel={(option: AutoCompleteOption | string) => (typeof option === 'string' ? option : option.label)}
                      loading={facetLoading}
                      onInputChange={(event, newInputValue) => {
                        setFacetInput(newInputValue);
                      }}
                      onChange={(e, newValue: (string | AutoCompleteOption)[]) => {
                        formik.setFieldValue(
                          'facets',
                          newValue.map((item) => (typeof item === 'string' ? item : item.value))
                        );
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          label="Search"
                          InputProps={{
                            ...params.InputProps,
                            endAdornment: (
                              <>
                                {facetLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                {params.InputProps.endAdornment}
                              </>
                            )
                          }}
                          onBlur={formik.handleBlur}
                          error={formik.touched.facets && Boolean(formik.errors.facets)}
                          name="facets"
                        />
                      )}
                    />
                    {Boolean(formik.errors.facets) && formik.touched.facets && formik.errors.facets && (
                      <FormHelperText error>{formik.errors.facets}</FormHelperText>
                    )}
                  </Box>
                </MainCard>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <CardActions sx={{ justifyContent: 'flex-end' }}>
          <Tooltip
            title={
              showErrorSummary ? (
                <Box>
                  <Typography fontWeight="bold">Please correct the following errors:</Typography>
                  <ul style={{ marginTop: 8, paddingLeft: 20 }}>
                    {Object.entries(formik.errors).map(([field, error]) => (
                      <li key={field}>{error as ReactNode}</li>
                    ))}
                  </ul>
                </Box>
              ) : (
                'Submit'
              )
            }
          >
            <LoadingButton variant="outlined" type="submit" loading={productCreateLoading}>
              Submit
            </LoadingButton>
          </Tooltip>
        </CardActions>
      </form>
    </MainCard>
  );
}

export default withPermission(PermissionKeys.CreateProduct)(AddNewProduct);
