import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  getModelSchemaRef,
  patch,
  put,
  del,
  requestBody,
  response,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {SellerShippingCharge} from '../models';
import {SellerShippingChargeRepository} from '../repositories';
import {STATUS_CODE, CONTENT_TYPE} from '@sourceloop/core';

const basePath = '/seller-shipping-charges';

export class SellerShippingChargeController {
  constructor(
    @repository(SellerShippingChargeRepository)
    public sellerShippingChargeRepository: SellerShippingChargeRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'SellerShippingCharge model instance',
    content: {
      [CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(SellerShippingCharge)},
    },
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerShippingCharge, {
            title: 'NewSellerShippingCharge',
            exclude: ['id'],
          }),
        },
      },
    })
    sellerShippingCharge: Omit<SellerShippingCharge, 'id'>,
  ): Promise<SellerShippingCharge> {
    return this.sellerShippingChargeRepository.create(sellerShippingCharge);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'SellerShippingCharge model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(
    @param.where(SellerShippingCharge) where?: Where<SellerShippingCharge>,
  ): Promise<Count> {
    return this.sellerShippingChargeRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of SellerShippingCharge model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(SellerShippingCharge, {
            includeRelations: true,
          }),
        },
      },
    },
  })
  async find(
    @param.filter(SellerShippingCharge) filter?: Filter<SellerShippingCharge>,
  ): Promise<SellerShippingCharge[]> {
    return this.sellerShippingChargeRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(basePath)
  @response(STATUS_CODE.OK, {
    description: 'SellerShippingCharge PATCH success count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async updateAll(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerShippingCharge, {partial: true}),
        },
      },
    })
    sellerShippingCharge: SellerShippingCharge,
    @param.where(SellerShippingCharge) where?: Where<SellerShippingCharge>,
  ): Promise<Count> {
    return this.sellerShippingChargeRepository.updateAll(
      sellerShippingCharge,
      where,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSeller]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'SellerShippingCharge model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(SellerShippingCharge, {
          includeRelations: true,
        }),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(SellerShippingCharge, {exclude: 'where'})
    filter?: FilterExcludingWhere<SellerShippingCharge>,
  ): Promise<SellerShippingCharge> {
    return this.sellerShippingChargeRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerShippingCharge PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(SellerShippingCharge, {partial: true}),
        },
      },
    })
    sellerShippingCharge: Partial<SellerShippingCharge>,
  ): Promise<void> {
    await this.sellerShippingChargeRepository.updateById(
      id,
      sellerShippingCharge,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerShippingCharge PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() sellerShippingCharge: SellerShippingCharge,
  ): Promise<void> {
    await this.sellerShippingChargeRepository.replaceById(
      id,
      sellerShippingCharge,
    );
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSeller]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'SellerShippingCharge DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.sellerShippingChargeRepository.deleteById(id);
  }
}
