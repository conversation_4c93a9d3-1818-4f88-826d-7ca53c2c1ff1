'use client';

import { Container, CircularProgress, Typography } from '@mui/material';
import { useGetDiscountByIdQuery } from 'redux/app/discount/discountApiSlice';
import DiscountForm from 'views/discount/DiscountForm';

const DiscountEditPage = ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const {
    data: discountData,
    isLoading,
    error
  } = useGetDiscountByIdQuery({ id, filter: { include: [{ relation: 'discountConditions' }] } });

  if (isLoading) {
    return (
      <Container sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Container>
    );
  }

  if (error || !discountData) {
    return (
      <Container sx={{ textAlign: 'center', mt: 4 }}>
        <Typography color="error" variant="h6">
          Failed to load discount
        </Typography>
      </Container>
    );
  }

  const initialValues = {
    id: discountData.id,
    name: discountData.name ?? '',
    description: discountData.description ?? '',
    isActive: discountData.isActive ?? true,
    startDate: discountData.startDate ?? '',
    endDate: discountData.endDate ?? '',
    usageLimitPerUser: discountData.usageLimitPerUser ?? 0,
    combinable: discountData.combinable ?? false,
    conditions:
      discountData.discountConditions?.map((cond) => ({
        conditionType: cond.conditionType,
        thresholdAmount: cond.thresholdAmount,
        discountType: cond.discountType,
        discountValue: cond.discountValue,
        isAppOnly: cond.isAppOnly ?? false
      })) ?? []
  };

  return (
    <Container maxWidth="md">
      <DiscountForm isEditMode={true} initialValues={initialValues} />
    </Container>
  );
};

export default DiscountEditPage;
