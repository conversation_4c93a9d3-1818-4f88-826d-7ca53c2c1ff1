import React from 'react';
import {View, Text, Image, StyleSheet, TouchableOpacity} from 'react-native';
import {colors} from '../../../../theme/colors';
import customColors from '../../../../theme/customColors';

type CardProps = {
  image: any;
  title?: string;
  subtitle?: string;
  onPress?: () => void;
};

const OfferCard = ({image, onPress, title, subtitle}: CardProps) => {
  const hasText = !!title || !!subtitle;
  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      {hasText && (
        <View style={styles.textContainer}>
          {title && <Text style={styles.title}>{title}</Text>}
          {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
        </View>
      )}
      <Image source={image} style={styles.image} resizeMode="cover" />
    </TouchableOpacity>
  );
};

export default OfferCard;

const styles = StyleSheet.create({
  card: {
    backgroundColor: customColors.white,
    borderRadius: 20,
    overflow: 'hidden',
    elevation: 3,
    marginBottom: 16,
    marginRight: 15,
    width: 130,
  },
  image: {
    borderBottomColor: customColors.white,
    width: '100%',
    height: 140,
  },
  textContainer: {
    padding: 12,
    paddingTop: 20,
    alignItems: 'flex-start',
    paddingBottom: 20,
    backgroundColor: colors.gray.backGround,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
    color: colors.tertiary,
  },

  forText: {
    color: colors.tertiary,
  },
  subtitle: {
    fontSize: 13,
    color: colors.gray.text,
  },
});
