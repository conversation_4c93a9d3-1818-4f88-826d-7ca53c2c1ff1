import React, {useEffect, useState} from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import CustomTextInput from '../../../components/InputFields/CustomTextInput';
import customColors from '../../../theme/customColors';
import {TextInput} from 'react-native-paper';
import CustomButton from '../../../components/CustomButton/CustomButton';
import {colors} from '../../../theme/colors';
import {
  useGetUserQuery,
  useUpdatePasswordMutation,
} from '../../../redux/auth/authApiSlice';
import {useFormik} from 'formik';
import Toast from 'react-native-toast-message';
import {ChangePasswordSchema} from '../../../validations/login';
import {useDispatch} from 'react-redux';
import {unsetCredentials} from '../../../redux/auth/authSlice';
import {unsetMonitor} from '../../../redux/apiMonitor/apiMonitorSlice';

const ChangePasswordScreen = () => {
  const [showCurrent, setShowCurrent] = useState(true);
  const [showNew, setShowNew] = useState(true);
  const [showConfirm, setShowConfirm] = useState(true);
  const {data: user} = useGetUserQuery();
  const dispatch = useDispatch();
  const handleLogout = () => {
    dispatch(unsetCredentials());
    dispatch(unsetMonitor());
    Toast.show({
      text1: 'You have been logged out. Please log in again.',
      type: 'info',
    });
  };

  const [updatePassword, {isLoading, error: updateError, reset: updateReset}] =
    useUpdatePasswordMutation();

  const formik = useFormik({
    initialValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
    validationSchema: ChangePasswordSchema,
    onSubmit: async values => {
      await updatePassword({
        username: user?.username ?? '',
        oldPassword: values.currentPassword,
        password: values.newPassword,
      }).unwrap();
      Toast.show({text1: 'Password updated successfully!', type: 'success'});
      setTimeout(() => {
        handleLogout();
      }, 2000);
      console.log('Submitting with values:', values);
    },
  });

  useEffect(() => {
    if (updateError) {
      updateReset();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [updateError]);
  return (
    <ScrollView contentContainerStyle={styles.container}>
      <View style={styles.container2}>
        <Text style={styles.title}>Change Password</Text>

        <Text style={styles.label}>Current Password</Text>
        <PasswordInput
          value={formik.values.currentPassword}
          onChangeText={formik.handleChange('currentPassword')}
          placeholder="Enter Old Password"
          secureEntry={showCurrent}
          onToggleSecureEntry={() => setShowCurrent(prev => !prev)}
          error={
            formik.touched.currentPassword && formik.errors.currentPassword
              ? true
              : false
          }
          touched={formik.touched.currentPassword}
          errors={formik.errors.currentPassword}
        />

        <Text style={styles.label}>New Password</Text>
        <PasswordInput
          value={formik.values.newPassword}
          onChangeText={formik.handleChange('newPassword')}
          placeholder="Enter New Password"
          secureEntry={showNew}
          onToggleSecureEntry={() => setShowNew(prev => !prev)}
          error={
            formik.touched.newPassword && formik.errors.newPassword
              ? true
              : false
          }
          touched={formik.touched.newPassword}
          errors={formik.errors.newPassword}
        />

        <Text style={styles.label}>Confirm Password</Text>
        <PasswordInput
          value={formik.values.confirmPassword}
          onChangeText={formik.handleChange('confirmPassword')}
          placeholder="Enter Confirm Password"
          secureEntry={showConfirm}
          onToggleSecureEntry={() => setShowConfirm(prev => !prev)}
          error={
            formik.touched.confirmPassword && formik.errors.confirmPassword
              ? true
              : false
          }
          touched={formik.touched.confirmPassword}
          errors={formik.errors.confirmPassword}
        />

        <View style={styles.rulesContainer}>
          <Text style={styles.rulesTitle}>New Password must contain:</Text>
          {[
            'At least 8 characters',
            'At least 1 lower letter (a-z)',
            'At least 1 uppercase letter (A-Z)',
            'At least 1 number (0-9)',
            'At least 1 special character',
          ].map((rule, index) => (
            <View key={index} style={styles.ruleItem}>
              <Text style={styles.bullet}>—</Text>
              <Text style={styles.ruleText}>{rule}</Text>
            </View>
          ))}
        </View>

        <CustomButton
          onPress={async () => {
            formik.setTouched({
              currentPassword: true,
              newPassword: true,
              confirmPassword: true,
            });
            await formik.handleSubmit();
          }}
          loading={isLoading}
          title={'Update Profile'}
        />
      </View>
    </ScrollView>
  );
};

export const PasswordInput = ({
  value,
  onChangeText,
  placeholder,
  secureEntry,
  onToggleSecureEntry,
  error,
  touched,
  errors,
}: {
  value: string;
  onChangeText: (text: string) => void;
  placeholder: string;
  secureEntry: boolean;
  onToggleSecureEntry: () => void;
  error?: boolean;
  touched?: boolean;
  errors?: string;
}) => {
  return (
    <>
      <CustomTextInput
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        secureTextEntry={secureEntry}
        title={''}
        touched={touched}
        errors={errors}
        error={error}
        right={
          <TextInput.Icon
            icon={secureEntry ? 'eye-off' : 'eye'}
            onPress={onToggleSecureEntry}
          />
        }
      />
      {!!error && <Text style={styles.errorText}>{error}</Text>}
    </>
  );
};

export default ChangePasswordScreen;
const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: colors.gray.backGround,
  },
  container2: {
    paddingVertical: 24,
    paddingHorizontal: 20,
    borderRadius: 12,
    backgroundColor: customColors.white,
  },
  title: {
    fontWeight: 'bold',
    fontSize: 18,
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    color: customColors.textBlack,
    marginBottom: 6,
    marginTop: 12,
    marginLeft: 8,
  },

  rulesContainer: {
    marginTop: 20,
  },
  rulesTitle: {
    fontWeight: '600',
    marginBottom: 10,
    fontSize: 15,
  },
  ruleItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
  },
  bullet: {
    fontSize: 18,
    marginRight: 6,
  },
  ruleText: {
    fontSize: 13,
    color: colors.gray.dark,
    flex: 1,
  },
  errorText: {color: colors.error, marginLeft: 4, fontSize: 12},
});
