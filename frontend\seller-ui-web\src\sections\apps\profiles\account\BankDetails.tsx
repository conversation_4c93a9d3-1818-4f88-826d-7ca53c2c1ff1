import { useFormik } from 'formik';

// material-ui
import {
  FormHelperText,
  Grid,
  InputLabel,
  OutlinedInput,
  Stack,
  Typography,
  Divider,
  Alert,
  Select,
  MenuItem,
  AlertTitle
} from '@mui/material';
import { LoadingButton } from '@mui/lab';

// project imports
import MainCard from 'components/MainCard';
import { bankDetailsValidationSchema } from 'validation/bank-details.validation';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';
import { BankDetailsType } from 'types/auth';
import { useAddBankDetailsMutation, useGetVendorQuery } from 'redux/auth/sellerApiSlice';
import { useGetUserQuery } from 'redux/auth/authApiSlice';
import { encryptData } from 'utils/encrypt';
import { businessTypes } from 'constants/business-types';
import { useEffect } from 'react';

// ==============================|| BANK DETAILS ||============================== //

const BankDetails = () => {
  const [addBankDetails, { isLoading }] = useAddBankDetailsMutation();
  const { data: user } = useGetUserQuery();
  const { data: vendor, isLoading: isVendorLoading, isFetching } = useGetVendorQuery();

  const isBankValidationFailed = vendor?.status === 'BANK_VALIDATION_FAILED';

  // Check for KYC document issues
  const kycIssues = vendor?.relatedDocs?.filter((doc) => doc.status === 'ACTION_REQUIRED' && doc.remarks) || [];

  const formik = useFormik<BankDetailsType>({
    initialValues: {
      accountNumber: '',
      accountHolder: '',
      ifsc: '',
      uidai: '',
      gst: '',
      pan: '',
      businessType: ''
    },
    validationSchema: bankDetailsValidationSchema,
    onSubmit: async (values) => {
      await addBankDetails({
        sellerId: user?.profileId ?? '',
        body: {
          accountNumber: encryptData(values.accountNumber),
          accountHolder: encryptData(values.accountHolder),
          ifsc: encryptData(values.ifsc),
          uidai: values.uidai ? encryptData(values.uidai) : undefined,
          gst: values.gst ? encryptData(values.gst) : undefined,
          pan: values.pan ? encryptData(values.pan) : undefined,
          businessType: values.businessType,
          accountType: values.accountType
        }
      }).unwrap();
      openSnackbar({
        open: true,
        message: 'Bank details updated successfully',
        variant: 'alert',
        alert: {
          color: 'success'
        },
        close: false
      } as SnackbarProps);
    }
  });

  useEffect(() => {
    if (isVendorLoading || isFetching) return;
    if (vendor) {
      formik.setValues({
        accountNumber: vendor?.bank?.accountNumber ?? '',
        accountHolder: vendor?.bank?.accountHolder ?? '',
        ifsc: vendor?.bank?.ifsc ?? '',
        uidai: vendor?.relatedDocs?.find((doc) => doc.docType === 'Aadhaar')?.docValue ?? '',
        gst: vendor?.relatedDocs?.find((doc) => doc.docType === 'GST')?.docValue ?? '',
        pan: vendor?.relatedDocs?.find((doc) => doc.docType === 'PAN')?.docValue ?? '',
        businessType: vendor?.businessType ?? '',
        accountType: vendor?.accountType ?? ''
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isVendorLoading, isFetching, vendor]);

  // Check if at least one KYC field is filled
  const hasKycField = !!(formik.values.uidai || formik.values.gst || formik.values.pan);

  return (
    <MainCard title="Bank Details">
      <form onSubmit={formik.handleSubmit}>
        <Grid container spacing={3}>
          {isBankValidationFailed && (
            <Grid item xs={12}>
              <Alert severity="error">
                <AlertTitle>Bank Validation Failed</AlertTitle>
                Your bank account details could not be validated. Please verify the information and try again.
                {vendor?.accountType === undefined && (
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Account type could not be populated. Please select it manually.
                  </Typography>
                )}
              </Alert>
            </Grid>
          )}
          <Grid item xs={12}>
            <Typography variant="h5">Banking Information</Typography>
            <Divider sx={{ my: 1.5 }} />
          </Grid>

          <Grid item xs={12} sm={6}>
            <Stack spacing={1}>
              <InputLabel htmlFor="accountHolder">Account Holder Name*</InputLabel>
              <OutlinedInput
                id="accountHolder"
                name="accountHolder"
                value={formik.values.accountHolder}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                placeholder="Enter account holder name"
                fullWidth
                error={Boolean(formik.touched.accountHolder && formik.errors.accountHolder)}
              />
              {formik.touched.accountHolder && formik.errors.accountHolder && (
                <FormHelperText error>{formik.errors.accountHolder}</FormHelperText>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6}>
            <Stack spacing={1}>
              <InputLabel htmlFor="accountNumber">Account Number*</InputLabel>
              <OutlinedInput
                id="accountNumber"
                name="accountNumber"
                value={formik.values.accountNumber}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                placeholder="Enter account number"
                fullWidth
                error={Boolean(formik.touched.accountNumber && formik.errors.accountNumber)}
              />
              {formik.touched.accountNumber && formik.errors.accountNumber && (
                <FormHelperText error>{formik.errors.accountNumber}</FormHelperText>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6}>
            <Stack spacing={1}>
              <InputLabel htmlFor="ifsc">IFSC Code*</InputLabel>
              <OutlinedInput
                id="ifsc"
                name="ifsc"
                value={formik.values.ifsc}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                placeholder="Enter IFSC code"
                fullWidth
                error={Boolean(formik.touched.ifsc && formik.errors.ifsc)}
              />
              {formik.touched.ifsc && formik.errors.ifsc && <FormHelperText error>{formik.errors.ifsc}</FormHelperText>}
              <Typography variant="caption" color="textSecondary">
                IFSC code is an 11-character code that identifies the bank branch (e.g., SBIN0001234)
              </Typography>
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6}>
            <Stack spacing={1}>
              <InputLabel htmlFor="businessType">Business Type*</InputLabel>
              <Select
                name="businessType"
                value={formik.values.businessType}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={Boolean(formik.touched.businessType && formik.errors.businessType)}
                fullWidth
              >
                <MenuItem disabled value="">
                  Select Business Type
                </MenuItem>
                {businessTypes.map((type) => (
                  <MenuItem key={type} value={type}>
                    {type}
                  </MenuItem>
                ))}
              </Select>
              {formik.touched.businessType && formik.errors.businessType && (
                <FormHelperText error>{formik.errors.businessType}</FormHelperText>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12} sm={6}>
            <Stack spacing={1}>
              <InputLabel htmlFor="businessType">Account Type*</InputLabel>
              <Select
                name="accountType"
                value={formik.values.accountType}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={Boolean(formik.touched.accountType && formik.errors.accountType)}
                fullWidth
                displayEmpty
              >
                <MenuItem disabled value="">
                  Select Account Type
                </MenuItem>
                {['Saving', 'Current'].map((type) => (
                  <MenuItem key={type} value={type}>
                    {type}
                  </MenuItem>
                ))}
              </Select>
              {formik.touched.accountType && formik.errors.accountType && (
                <FormHelperText error>{formik.errors.accountType}</FormHelperText>
              )}
            </Stack>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="h5">KYC Information</Typography>
            <Divider sx={{ my: 1.5 }} />
            <Alert severity="info" sx={{ mb: 2 }}>
              At least one KYC document (Aadhaar, PAN, or GST) is required for verification.
            </Alert>
          </Grid>
          {kycIssues.length > 0 && (
            <Grid item xs={12}>
              <Alert severity="warning" sx={{ mb: 2 }}>
                <AlertTitle>KYC Document Issues</AlertTitle>
                <Stack spacing={1}>
                  {kycIssues.map((doc, index) => (
                    <Typography key={index} variant="body2">
                      {doc.docName}: {doc.remarks}
                    </Typography>
                  ))}
                </Stack>
              </Alert>
            </Grid>
          )}
          <Grid item xs={12} sm={4}>
            <Stack spacing={1}>
              <InputLabel htmlFor="uidai">Aadhaar Number</InputLabel>
              <OutlinedInput
                id="uidai"
                name="uidai"
                value={formik.values.uidai}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                placeholder="Enter Aadhaar number"
                fullWidth
                error={Boolean(formik.touched.uidai && formik.errors.uidai)}
              />
              {formik.touched.uidai && formik.errors.uidai && <FormHelperText error>{formik.errors.uidai}</FormHelperText>}
              <Typography variant="caption" color="textSecondary">
                12-digit Aadhaar number without spaces
              </Typography>
            </Stack>
          </Grid>

          <Grid item xs={12} sm={4}>
            <Stack spacing={1}>
              <InputLabel htmlFor="pan">PAN</InputLabel>
              <OutlinedInput
                id="pan"
                name="pan"
                value={formik.values.pan}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                placeholder="Enter PAN"
                fullWidth
                error={Boolean(formik.touched.pan && formik.errors.pan)}
              />
              {formik.touched.pan && formik.errors.pan && <FormHelperText error>{formik.errors.pan}</FormHelperText>}
              <Typography variant="caption" color="textSecondary">
                10-character PAN (e.g., **********)
              </Typography>
            </Stack>
          </Grid>

          <Grid item xs={12} sm={4}>
            <Stack spacing={1}>
              <InputLabel htmlFor="gst">GST Number</InputLabel>
              <OutlinedInput
                id="gst"
                name="gst"
                value={formik.values.gst}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                placeholder="Enter GST number"
                fullWidth
                error={Boolean(formik.touched.gst && formik.errors.gst)}
              />
              {formik.touched.gst && formik.errors.gst && <FormHelperText error>{formik.errors.gst}</FormHelperText>}
              <Typography variant="caption" color="textSecondary">
                15-character GST number (e.g., 22AAAAA0000A1Z5)
              </Typography>
            </Stack>
          </Grid>

          {formik.touched.uidai && formik.touched.pan && formik.touched.gst && !hasKycField && (
            <Grid item xs={12}>
              <FormHelperText error>At least one KYC document (Aadhaar, PAN, or GST) is required</FormHelperText>
            </Grid>
          )}

          <Grid item xs={12}>
            <Stack direction="row" justifyContent="flex-end">
              <LoadingButton
                type="submit"
                variant="contained"
                loading={isLoading}
                disabled={!formik.isValid || !formik.dirty || !hasKycField}
              >
                Save Details
              </LoadingButton>
            </Stack>
          </Grid>
        </Grid>
      </form>
    </MainCard>
  );
};

export default BankDetails;
