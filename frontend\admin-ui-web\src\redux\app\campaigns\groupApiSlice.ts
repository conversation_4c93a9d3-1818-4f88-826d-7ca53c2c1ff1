import { ApiSliceIdentifier } from 'enums/api.enum';
import { apiSlice } from '../../apiSlice';
import { Count } from 'types/api'; // or define Count = { count: number }
import { Group } from 'types/group'; // Define this model as per your schema
import { IFilter } from '../types/filter'; // You can reuse your existing filter interface
import { buildFilterParams } from 'utils/buildFilterParams';

export const groupApiSlice = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getGroups: builder.query<Group[], IFilter | void>({
      query: (filter) => ({
        url: '/groups',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: {
          filter: buildFilterParams(filter ?? undefined)
        }
      })
    }),

    getGroupById: builder.query<Group, { id: string; filter?: Record<string, any> }>({
      query: ({ id, filter }) => ({
        url: `/groups/${id}`,
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: filter ? { filter: JSON.stringify(filter) } : {}
      })
    }),

    createGroup: builder.mutation<Group, Partial<Group>>({
      query: (group) => ({
        url: '/groups',
        method: 'POST',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: group
      })
    }),

    updateGroupById: builder.mutation<void, { id: string; data: Partial<Group> }>({
      query: ({ id, data }) => ({
        url: `/groups/${id}`,
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),

    replaceGroupById: builder.mutation<void, { id: string; data: Group }>({
      query: ({ id, data }) => ({
        url: `/groups/${id}`,
        method: 'PUT',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data
      })
    }),

    deleteGroupById: builder.mutation<void, string>({
      query: (id) => ({
        url: `/groups/${id}`,
        method: 'DELETE',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE
      })
    }),

    updateGroups: builder.mutation<Count, { data: Partial<Group>; where?: Record<string, any> }>({
      query: ({ data, where }) => ({
        url: '/groups',
        method: 'PATCH',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        body: data,
        params: where ? { where: JSON.stringify(where) } : {}
      })
    }),

    getGroupsCount: builder.query<Count, { where?: Record<string, unknown> }>({
      query: ({ where }) => ({
        url: '/groups/count',
        method: 'GET',
        apiSliceIdentifier: ApiSliceIdentifier.ECOM_FACADE,
        params: where ? { where: JSON.stringify(where) } : {}
      })
    })
  })
});

export const {
  useGetGroupsQuery,
  useGetGroupByIdQuery,
  useCreateGroupMutation,
  useUpdateGroupByIdMutation,
  useReplaceGroupByIdMutation,
  useDeleteGroupByIdMutation,
  useUpdateGroupsMutation,
  useGetGroupsCountQuery
} = groupApiSlice;
