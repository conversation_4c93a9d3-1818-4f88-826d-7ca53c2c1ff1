/* Replace with your SQL commands */
CREATE TABLE IF NOT EXISTS main.groups (
    id UUID NOT NULL DEFAULT (
        md5(
            (
                (random()) :: text || (clock_timestamp()) :: text
            )
        ) :: uuid
    ),
    name TEXT NOT NULL,
    topic_id TEXT NOT NULL,
    list_key TEXT NOT NULL,
    list_name TEXT NOT NULL,
    group_id TEXT NOT NULL,
    created_on TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    modified_on TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
    created_by <PERSON><PERSON><PERSON>,
    modified_by <PERSON><PERSON><PERSON>,
    deleted BOOLEAN DEFAULT FALSE NOT NULL,
    deleted_by <PERSON><PERSON><PERSON>,
    deleted_on TIMESTAMPTZ,
    CONSTRAINT pk_groups_id PRIMARY KEY (id)
);