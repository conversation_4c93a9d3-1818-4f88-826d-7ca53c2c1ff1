'use client';

import {useEffect, useState} from 'react';
import {
  Box,
  Card,
  CardContent,
  CircularProgress,
  Typography,
} from '@mui/material';
import draftToHtml from 'draftjs-to-html';
import {useDispatch} from 'react-redux';
import {
  useGetGuestTokenMutation,
  useGetTermsAndConditionQuery,
} from 'redux/terms-and-privacy/termsApiSlice';
import {setGuestToken} from 'redux/auth/authSlice';
import {Legals} from 'types/legal-category.enum';

interface LegalContentProps {
  type: Legals;
}

const LegalContent = ({type}: LegalContentProps) => {
  const dispatch = useDispatch();
  const [guestCode, setGuestCode] = useState<string | null>(null);
  const [getToken] = useGetGuestTokenMutation();

  const accessToken =
    typeof window !== 'undefined' ? localStorage.getItem('accessToken') : null;

  useEffect(() => {
    if (!accessToken) {
      const handleGuestLogin = async () => {
        const result = await getToken().unwrap();
        if (result?.accessToken) {
          dispatch(setGuestToken(result));
          setGuestCode(result.accessToken);
        }
      };
      handleGuestLogin();
    }
  }, [accessToken, getToken, dispatch]);

  const tokenToUse = accessToken || guestCode;

  const {data: termData, isLoading} = useGetTermsAndConditionQuery(
    tokenToUse!,
    {
      skip: !tokenToUse,
    },
  );

  const isHtml = (str: string) => /<\/?[a-z][\s\S]*>/i.test(str);
  const legalContentList = termData?.filter(term => term.type === type);

  return (
    <Card sx={{borderRadius: 3, p: 2}}>
      {isLoading ? (
        <Box textAlign="center" py={4}>
          <CircularProgress />
          <Typography mt={2}>Loading content...</Typography>
        </Box>
      ) : legalContentList?.length ? (
        legalContentList.map((item, index) => {
          let htmlContent = '<p>No content</p>';
          try {
            htmlContent =
              typeof item.data === 'string'
                ? isHtml(item.data)
                  ? item.data
                  : draftToHtml(JSON.parse(item.data))
                : draftToHtml(item.data);
          } catch (err) {
            console.error('Failed to convert content:', err);
            htmlContent = '<p>Error loading content.</p>';
          }

          return (
            <CardContent key={index} sx={{mb: 2}}>
              <div dangerouslySetInnerHTML={{__html: htmlContent}} />
            </CardContent>
          );
        })
      ) : (
        <Typography>No legal content found.</Typography>
      )}
    </Card>
  );
};

export default LegalContent;
