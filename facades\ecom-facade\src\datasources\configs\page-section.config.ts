import {ModifiedRestService, RestOperationTemplate} from '@sourceloop/core';
import {PageSection, PageSectionBulkDto, PageSectionDto} from '../../models';

export type PageSectionProxyType = {
  createPageSection(
    body: Omit<PageSectionDto, 'id'>,
    token: string,
  ): Promise<PageSection>;
  createBulkPageSection(
    body: PageSectionBulkDto,
    token: string,
  ): Promise<PageSection>;
  reorderPageSection(
    id: string,
    body: Partial<PageSection>,
    token: string,
  ): Promise<void>;
} & ModifiedRestService<PageSection>;

export const PageSectionProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'POST',
      url: '/page-sections',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      createPageSection: ['body', 'token'],
    },
  },
  {
    template: {
      method: 'POST',
      url: '/page-sections/bulk',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      createBulkPageSection: ['body', 'token'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: '/page-sections/{id}/reorder',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      reorderPageSection: ['id', 'body', 'token'],
    },
  },
];
