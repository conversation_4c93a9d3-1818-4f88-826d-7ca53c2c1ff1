import {belongsTo, model, property} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';

@model({name: 'ecomdukeservices'})
export class Ecomdukeservice extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  description?: string;

  @property({
    type: 'number',
    required: true,
    default: 0.0,
  })
  price: number;

  @property({
    type: 'string',
    required: true,
    default: 'INR',
  })
  currency: string;

  @belongsTo(() => Ecomdukeservice, {keyTo: 'id'}, {name: 'tax_category_id'})
  taxCategoryId: string;

  @property({
    type: 'boolean',
    required: true,
    default: true,
    name: 'is_active',
  })
  isActive: boolean;

  constructor(data?: Partial<Ecomdukeservice>) {
    super(data);
  }
}

export interface EcomDukeserviceRelations {
  // define navigational properties here
}

export type EcomDukeserviceWithRelations = Ecomdukeservice &
  EcomDukeserviceRelations;
