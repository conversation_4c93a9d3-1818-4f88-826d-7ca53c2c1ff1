import {FC, useEffect, useRef, useState} from 'react';
import {SectionItem} from '../../../../types/page-section';
import {Dimensions, FlatList, Image, StyleSheet} from 'react-native';
const {width} = Dimensions.get('window');
export const CarouselComponent: FC<{items: SectionItem[]}> = ({items}) => {
  const flatListRef = useRef<FlatList<any>>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  useEffect(() => {
    if (items.length === 0) {
      return;
    }

    const intervalId = setInterval(() => {
      const nextIndex = (currentIndex + 1) % items.length;
      setCurrentIndex(nextIndex);
      flatListRef.current?.scrollToIndex({index: nextIndex, animated: true});
    }, 3000);
    return () => clearInterval(intervalId);
  }, [currentIndex, items.length]);

  return (
    <FlatList
      ref={flatListRef}
      horizontal
      pagingEnabled
      data={items}
      showsHorizontalScrollIndicator={false}
      keyExtractor={item => item.id.toString()}
      renderItem={({item}) => (
        <Image
          source={{uri: item.previewUrl || item.imageUrl}}
          style={[styles.carouselImage, {width}]}
          resizeMode="cover"
        />
      )}
    />
  );
};

const styles = StyleSheet.create({
  carouselImage: {
    width: width,
    height: 180,
    resizeMode: 'cover',
  },
});
