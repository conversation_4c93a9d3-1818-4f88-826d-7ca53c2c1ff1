'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Paper,
  IconButton,
  Tooltip,
  Stack
} from '@mui/material';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Edit } from 'iconsax-react';

import {
  useCreatePinnedProductMutation,
  useDeletePinnedProductMutation,
  useGetPinnedProductsQuery
} from 'redux/app/products/productApiSlice';

import { ProductVariant } from 'types/product-variant';

type Props = {
  productVariants: ProductVariant[];
  hideTitle?: boolean;
  callback?: string;
};

export default function ProductVariantsTable({ productVariants, hideTitle, callback }: Props) {
  const router = useRouter();

  const { data: pinnedProducts = [], refetch: refetchPinned } = useGetPinnedProductsQuery({});
  const [createPinnedProduct] = useCreatePinnedProductMutation();
  const [deletePinnedProduct] = useDeletePinnedProductMutation();
  const [loading, setLoading] = useState<string | null>(null);

  const variantIdToProductIdMap = new Map<string, string>(productVariants.map((v) => [v.id, v.productId]));

  const getPinnedVariantForProduct = (productId: string): string | undefined => {
    for (const pinned of pinnedProducts) {
      const variantProductId = variantIdToProductIdMap.get(pinned.productVariantId);
      if (variantProductId === productId) {
        return pinned.productVariantId;
      }
    }
    return undefined;
  };

  const handlePin = async (variantToPin: ProductVariant) => {
    setLoading(variantToPin.id);
    const productId = variantToPin.productId;
    if (!productId) return;

    const currentPinned = pinnedProducts.find((pinned) => {
      const match = productVariants.find((v) => v.id === pinned.productVariantId);
      return match?.productId === productId;
    });

    const isSame = currentPinned?.productVariantId === variantToPin.id;

    if (isSame && currentPinned?.id) {
      await deletePinnedProduct(currentPinned.id).unwrap();
    } else {
      if (currentPinned?.id) {
        await deletePinnedProduct(currentPinned.id).unwrap();
        await refetchPinned();
      }
      await createPinnedProduct({ productVariantId: variantToPin.id }).unwrap();
    }

    await refetchPinned();
    setLoading(null);
  };

  if (!productVariants?.length) {
    return (
      <Typography variant="body2" color="text.secondary">
        <strong>No data found</strong>
      </Typography>
    );
  }

  return (
    <>
      {!hideTitle && (
        <Typography variant="h5" gutterBottom>
          Product Variants
        </Typography>
      )}

      <TableContainer component={Paper} elevation={1}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell>
                <strong>Image</strong>
              </TableCell>
              <TableCell>
                <strong>Name</strong>
              </TableCell>
              <TableCell>
                <strong>SKU</strong>
              </TableCell>
              <TableCell>
                <strong>Actions</strong>
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {productVariants.map((variant) => {
              const currentlyPinnedVariantId = getPinnedVariantForProduct(variant.productId);
              const pinned = currentlyPinnedVariantId === variant.id;

              return (
                <TableRow key={variant.id}>
                  <TableCell>
                    {variant.featuredAsset?.previewUrl ? (
                      <Image
                        src={variant.featuredAsset.previewUrl}
                        alt={variant.name}
                        width={40}
                        height={40}
                        style={{ objectFit: 'cover', borderRadius: 4 }}
                      />
                    ) : (
                      <span>—</span>
                    )}
                  </TableCell>
                  <TableCell>{variant.name}</TableCell>
                  <TableCell>{variant.sku}</TableCell>
                  <TableCell>
                    <Stack direction="row" spacing={1}>
                      <Tooltip title="Edit Product Variant">
                        <IconButton
                          color="primary"
                          onClick={() => router.push(`/products/variants/${variant.id}?callback=${callback ?? '/products'}`)}
                        >
                          <Edit />
                        </IconButton>
                      </Tooltip>

                      <Tooltip title={pinned ? 'Unpin Variant' : 'Pin this Variant'}>
                        <IconButton
                          onClick={() => handlePin(variant)}
                          color={pinned ? 'success' : 'default'}
                          disabled={loading === variant.id}
                        >
                          <Image
                            src={pinned ? '/assets/images/icons8-pin-25.png' : '/assets/images/icons8-pin-48.png'}
                            alt={pinned ? 'Pinned' : 'Unpinned'}
                            width={24}
                            height={24}
                          />
                        </IconButton>
                      </Tooltip>
                    </Stack>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
    </>
  );
}
