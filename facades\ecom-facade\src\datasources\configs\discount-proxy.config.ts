import {ModifiedRestService, RestOperationTemplate} from '@sourceloop/core';
import {CreateDiscountDto, Discount} from '../../models';

export type DiscountProxyType = {
  createDiscount(
    discount: Omit<CreateDiscountDto, 'id'>,
    token: string,
  ): Promise<Discount>;
  updateDiscountWithConditions(
    id: string,
    discount: Omit<CreateDiscountDto, 'id'>,
    token: string,
  ): Promise<Discount>;
  getEligibleFirstOrderDiscount(
    body: {
      customerId: string;
      cartTotal: number;
      isFromApp: boolean;
    },
    token: string,
  ): Promise<unknown>;
} & ModifiedRestService<Discount>;

export const DiscountProxyConfig: RestOperationTemplate[] = [
  {
    template: {
      method: 'POST',
      url: '/discounts',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{discount}',
    },
    functions: {
      createDiscount: ['discount', 'token'],
    },
  },
  {
    template: {
      method: 'PATCH',
      url: '/discounts/{id}',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{discount}',
    },
    functions: {
      updateDiscountWithConditions: ['id', 'discount', 'token'],
    },
  },
  {
    template: {
      method: 'POST',
      url: '/discounts/check-eligibility',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        Authorization: '{token}',
      },
      body: '{body}',
    },
    functions: {
      getEligibleFirstOrderDiscount: ['body', 'token'],
    },
  },
];
