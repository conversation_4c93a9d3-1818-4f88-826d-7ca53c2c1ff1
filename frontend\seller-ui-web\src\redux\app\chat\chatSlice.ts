import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ChatDto } from '../types/chat';

interface ChatState {
  selectedChat: ChatDto | null;
}

const initialState: ChatState = {
  selectedChat: null
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    setSelectedChat(state, action: PayloadAction<ChatDto | null>) {
      state.selectedChat = action.payload;
    }
  }
});

export const { setSelectedChat } = chatSlice.actions;
export default chatSlice.reducer;
