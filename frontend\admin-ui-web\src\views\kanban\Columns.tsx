'use client';

import { CSSProperties } from 'react';

// material-ui
import { useTheme, Theme } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Chip from '@mui/material/Chip';

// third-party
import { Droppable, Draggable, DraggingStyle, NotDraggingStyle } from '@hello-pangea/dnd';

// project-imports
import { ThemeMode } from 'config';

// types
import { KanbanColumn, KanbanItem } from 'types/kanban';
import { ITicket } from './index';
import Items from './Items';

interface Props {
  column: KanbanColumn;
  index: number;
  items: KanbanItem[];
  tickets: ITicket[];
}

// column drag wrapper
function getDragWrapper(
  isDragging: boolean,
  draggableStyle: DraggingStyle | NotDraggingStyle | undefined,
  theme: Theme,
  radius: string
): CSSProperties | undefined {
  return {
    minWidth: 250,
    border: '1px solid',
    borderColor: theme.palette.divider,
    borderRadius: radius,
    userSelect: 'none',
    margin: `0 ${16}px 0 0`,
    height: '100%',
    ...draggableStyle
  };
}

// column drop wrapper
function getDropWrapper(isDraggingOver: boolean, theme: Theme, radius: string) {
  const bgcolor = theme.palette.mode === ThemeMode.DARK ? theme.palette.background.default : theme.palette.secondary[200];
  const bgcolorDrop = theme.palette.mode === ThemeMode.DARK ? theme.palette.text.disabled : theme.palette.secondary.light + '65';

  return {
    background: isDraggingOver ? bgcolorDrop : bgcolor,
    padding: '8px 16px 14px',
    width: 'auto',
    borderRadius: radius
  };
}

// ==============================|| KANBAN BOARD - COLUMN ||============================== //

export default function Columns({ column, index, items, tickets }: Props) {
  const theme = useTheme();

  // Get tickets for this column
  const columnTickets: ITicket[] = column.itemIds
    .map((itemId: string) => tickets.find((ticket: ITicket) => ticket.id === itemId))
    .filter((ticket): ticket is ITicket => ticket !== undefined);

  return (
    <Draggable draggableId={column.id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          style={getDragWrapper(snapshot.isDragging, provided.draggableProps.style, theme, `12px`)}
        >
          {/* Column Header */}
          <Box
            {...provided.dragHandleProps}
            sx={{
              p: 2,
              bgcolor: theme.palette.mode === ThemeMode.DARK ? 'background.paper' : 'secondary.lighter',
              borderRadius: '12px 12px 0 0',
              borderBottom: `1px solid ${theme.palette.divider}`
            }}
          >
            <Stack direction="row" justifyContent="space-between" alignItems="center">
              <Typography variant="h6" component="div">
                {column.title}
              </Typography>
              <Chip label={columnTickets.length} size="small" color="primary" variant="outlined" />
            </Stack>
          </Box>

          {/* Column Content */}
          <Droppable droppableId={column.id} type="item">
            {(providedDrop, snapshotDrop) => (
              <div
                ref={providedDrop.innerRef}
                {...providedDrop.droppableProps}
                style={getDropWrapper(snapshotDrop.isDraggingOver, theme, `0 0 12px 12px`)}
              >
                {columnTickets.map((ticket, i) => (
                  <Items key={ticket.id} ticket={ticket} index={i} />
                ))}
                {providedDrop.placeholder}
              </div>
            )}
          </Droppable>
        </div>
      )}
    </Draggable>
  );
}
