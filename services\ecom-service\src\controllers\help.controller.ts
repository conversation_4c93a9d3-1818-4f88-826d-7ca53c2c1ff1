import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  patch,
  put,
  del,
  requestBody,
  response,
  getModelSchemaRef,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {PermissionKeys} from '@local/core';
import {Help} from '../models';
import {HelpRepository} from '../repositories';

const basePath = '/helps';

export class HelpController {
  constructor(
    @repository(HelpRepository)
    public helpRepository: HelpRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateSupport]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Help model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Help)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Help, {
            title: 'NewHelp',
            exclude: ['id'],
          }),
        },
      },
    })
    help: Omit<Help, 'id'>,
  ): Promise<Help> {
    return this.helpRepository.create(help);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSupport]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'Help model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(@param.where(Help) where?: Where<Help>): Promise<Count> {
    return this.helpRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSupport]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Help model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Help, {includeRelations: true}),
        },
      },
    },
  })
  async find(@param.filter(Help) filter?: Filter<Help>): Promise<Help[]> {
    return this.helpRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSupport]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'Help model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Help, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Help, {exclude: 'where'}) filter?: FilterExcludingWhere<Help>,
  ): Promise<Help> {
    return this.helpRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSupport]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Help PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Help, {partial: true}),
        },
      },
    })
    help: Partial<Help>,
  ): Promise<void> {
    await this.helpRepository.updateById(id, help);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSupport]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Help PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() help: Help,
  ): Promise<void> {
    await this.helpRepository.replaceById(id, help);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteSupport]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Help DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.helpRepository.deleteById(id);
  }
}
