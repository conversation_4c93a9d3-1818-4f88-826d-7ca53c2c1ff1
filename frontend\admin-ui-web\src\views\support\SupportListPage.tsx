'use client';

import { useMemo, useState, MouseEvent, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@mui/material/styles';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { SortingState, ColumnFiltersState, ColumnDef, PaginationState } from '@tanstack/react-table';
import IconButton from '../../components/@extended/IconButton';
import { Add, Edit, Eye, Trash } from 'iconsax-react';
import moment from 'moment-timezone';
import { convertFiltersToLoopbackWhere, convertPaginationToLoopback, convertSortingToLoopbackSort } from '../../utils/table-filter';
import Loader from 'components/Loader';
import { ThemeMode } from 'config';
import { Chip } from '@mui/material';
import { FaqStatus } from 'enums/faq-status.enum';
import { FaqVisibility } from 'enums/faq-visibility.enum';
import { PermissionKeys } from 'enums/permission-keys.enum';
import { useAuth } from 'contexts/AuthContext';
import withPermission from 'hoc/withPermission';
import IndeterminateCheckbox from 'components/third-party/react-table/IndeterminateCheckbox';
import { SupportContactInfo } from 'types/admin';
import SupportTable from './SupportTable';
import AlertSupportDelete from './AlertSupportDelete';
import { useGetSupportCountQuery, useGetSupportQuery } from 'redux/app/contents/support/supportApiSlice';

const SupportListPage = () => {
  const theme = useTheme();
  const router = useRouter();
  const { hasPermission } = useAuth();

  const visibilityLabels = useMemo(
    () => ({
      [FaqVisibility.ALL]: 'All Users',
      [FaqVisibility.ADMIN]: 'Admin Only',
      [FaqVisibility.SELLER]: 'Seller Only',
      [FaqVisibility.CUSTOMER]: 'Customer Only',
      [FaqVisibility.SellOnEcomdukes]: 'Sell on Ecomdukes'
    }),
    []
  );

  const statusLabels = useMemo(
    () => ({
      [FaqStatus.ACTIVE]: { label: 'Active', color: 'success' as const },
      [FaqStatus.INACTIVE]: { label: 'Inactive', color: 'default' as const }
    }),
    []
  );

  const [sorting, setSorting] = useState<SortingState>([{ id: 'createdOn', desc: true }]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationState>({ pageIndex: 0, pageSize: 10 });
  const [supportDeleteId, setsupportDeleteId] = useState<string>('');

  const canCreate = hasPermission(PermissionKeys.CreateSupport);
  const canEdit = hasPermission(PermissionKeys.UpdateSupport);
  const canDelete = hasPermission(PermissionKeys.DeleteSupport);

  const [open, setOpen] = useState<boolean>(false);
  const {
    data: supportList,
    isLoading: supportListLoading,
    refetch
  } = useGetSupportQuery({
    order: convertSortingToLoopbackSort(sorting),
    where: convertFiltersToLoopbackWhere(columnFilters, globalFilter, ['faq.quetions', 'faq.answer']),
    ...convertPaginationToLoopback(pagination)
  });

  const { data: supportCount, isLoading: faqCountLoading } = useGetSupportCountQuery();

  const handleClose = useCallback(() => setOpen((prev) => !prev), []);
  useEffect(() => {
    const fetchData = async () => {
      if (refetch) {
        await refetch();
      }
    };
    fetchData();
  }, [refetch]);

  const columns = useMemo<ColumnDef<any>[]>(
    () => [
      {
        id: 'Row Selection',
        header: ({ table }) => (
          <IndeterminateCheckbox
            checked={table.getIsAllRowsSelected()}
            indeterminate={table.getIsSomeRowsSelected()}
            onChange={table.getToggleAllRowsSelectedHandler()}
          />
        ),
        cell: ({ row }) => (
          <IndeterminateCheckbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            indeterminate={row.getIsSomeSelected()}
            onChange={row.getToggleSelectedHandler()}
          />
        )
      },
      {
        header: 'Support Email',
        accessorKey: 'supportEmail',
        enableSorting: true,
        cell: ({ row }) => <Typography>{row.original?.supportEmail ?? '-'}</Typography>
      },
      {
        header: 'Visibility',
        accessorKey: 'visibility',
        cell: ({ row }) => {
          const visibility: FaqVisibility = row.original.visibility;
          return visibilityLabels[visibility] || 'Unknown';
        }
      },
      {
        header: 'Support Phone',
        accessorKey: 'supportPhone',
        enableSorting: true,
        cell: ({ row }) => <Typography> {row.original?.supportPhone ? `+91${row.original.supportPhone}` : '-'}</Typography>
      },
      {
        header: 'CreatedOn',
        accessorKey: 'createdOn',
        enableSorting: true,
        cell: ({ row }) => <Typography>{moment(row.original?.createdOn).format('DD-MM-YYYY') ?? '-'}</Typography>
      },
      {
        header: 'Status',
        accessorKey: 'status',
        cell: ({ row }) => {
          const status: FaqStatus = row.original.status;
          const chipData = statusLabels[status] || { label: 'Unknown', color: 'default' };
          return <Chip color={chipData.color} label={chipData.label} size="small" variant="outlined" />;
        }
      },

      {
        header: 'Actions',
        enableSorting: false,
        cell: ({ row }) => {
          const collapseIcon =
            row.getCanExpand() && row.getIsExpanded() ? (
              <Add
                style={{
                  color: theme.palette.error.main,
                  transform: 'rotate(45deg)'
                }}
              />
            ) : (
              <Eye />
            );

          return (
            <Stack direction="row" alignItems="center" justifyContent="center" spacing={0}>
              <Tooltip title="View">
                <IconButton
                  color="secondary"
                  onClick={row.getToggleExpandedHandler()}
                  sx={{
                    color: theme.palette.primary.light,
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  {collapseIcon}
                </IconButton>
              </Tooltip>
              <Tooltip title="Edit">
                <IconButton
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    router.push(`/contents/support/edit/${row.original.id}`);
                  }}
                  disabled={!canEdit}
                  sx={{
                    ':hover': {
                      backgroundColor: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.primary.light
                    }
                  }}
                >
                  <Edit />
                </IconButton>
              </Tooltip>

              <Tooltip title="Delete">
                <IconButton
                  color="error"
                  sx={{
                    ':hover': {
                      color: theme.palette.mode === ThemeMode.DARK ? theme.palette.common.white : theme.palette.error[100]
                    }
                  }}
                  onClick={(e: MouseEvent<HTMLButtonElement>) => {
                    e.stopPropagation();
                    handleClose();
                    setsupportDeleteId(row.original.id as string);
                  }}
                  disabled={!canDelete}
                >
                  <Trash />
                </IconButton>
              </Tooltip>
            </Stack>
          );
        }
      }
    ],
    [theme, router, canEdit, canDelete, handleClose, statusLabels, visibilityLabels]
  );

  return (
    <>
      {faqCountLoading || supportListLoading ? (
        <Loader />
      ) : (
        <SupportTable
          {...{
            data: supportList as SupportContactInfo[],
            columns,
            setSorting,
            sorting,
            columnFilters,
            setColumnFilters,
            loading: supportListLoading,
            globalFilter,
            setGlobalFilter,
            pagination,
            setPagination,
            totalRows: supportCount?.count ?? 0,
            canCreate,
            refetch
          }}
        />
      )}
      <AlertSupportDelete refetch={refetch} id={supportDeleteId} title={supportDeleteId} open={open} handleClose={handleClose} />
    </>
  );
};

export default withPermission(PermissionKeys.ViewSupport)(SupportListPage);
