'use client';

import { useEffect, useState } from 'react';
import { useTheme } from '@mui/material/styles';
import Chip from '@mui/material/Chip';
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import FormControlLabel from '@mui/material/FormControlLabel';
import ListItemText from '@mui/material/ListItemText';
import ListItemIcon from '@mui/material/ListItemIcon';
import Stack from '@mui/material/Stack';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { ShieldCross, TickSquare } from 'iconsax-react';
import MainCard from 'components/MainCard';
import { Button } from '@mui/material';
import {
  useCreateSubscriptionMutation,
  useGetFeaturesQuery,
  useGetPlansQuery,
  useGetSubscriptionQuery
} from '../../../../redux/ecom/ecomApiSlice';
import { useGetUserQuery } from '../../../../redux/auth/authApiSlice';
import { useApiErrorHandler } from 'hooks/useApiErrorHandler';
import { openSnackbar } from 'api/snackbar';
import { SnackbarProps } from 'types/snackbar';

export default function Pricing() {
  const theme = useTheme();
  const { data: plans = [] } = useGetPlansQuery();
  const { data: features = [] } = useGetFeaturesQuery();
  const { data: user } = useGetUserQuery();
  const currencySymbols: Record<string, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    INR: '₹'
  };

  const { data: subscriptions, refetch } = useGetSubscriptionQuery(user?.profileId ?? '', {
    skip: !user?.profileId
  });
  const handleError = useApiErrorHandler();

  const [createSubscriptionApi, { error: updateError, reset: updateReset }] = useCreateSubscriptionMutation();
  useEffect(() => {
    if (user?.profileId) {
      refetch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.profileId, refetch]);

  const [selectedPlan, setSelectedPlan] = useState<string>(plans[0]?.name || '');

  useEffect(() => {
    if (subscriptions?.length) {
      const activeSubscription = subscriptions.find((sub) => sub.status === 'ACTIVE');
      if (activeSubscription) {
        const subscribedPlan = plans.find((plan) => plan.id === activeSubscription.planId);
        if (subscribedPlan) {
          setSelectedPlan(subscribedPlan.name);
        }
      }
    } else if (plans.length) {
      setSelectedPlan(plans[0]?.name);
    }
  }, [subscriptions, plans]);
  const handlePlanChange = (value: string) => {
    setSelectedPlan(value);
  };
  const handleSubscription = async () => {
    if (!user?.profileId) return;

    const selectedPlanObj = plans.find((plan) => plan.name === selectedPlan);
    if (!selectedPlanObj) return;

    await createSubscriptionApi({
      subscriberId: user.profileId,
      planId: selectedPlanObj.id
    }).unwrap();
    refetch();
    openSnackbar({
      open: true,
      message: 'Plans updated successfully',
      variant: 'alert',
      alert: { color: 'success' }
    } as SnackbarProps);
  };

  const currentPlan = plans.find((plan) => plan.name === selectedPlan);
  const priceSelectedPlan = {
    padding: 3,
    borderRadius: 1,
    border: '1px solid',
    borderColor: theme.palette.divider,
    bgcolor: theme.palette.info.lighter
  };

  const priceUnselectedPlan = {
    padding: 3,
    borderRadius: 1,
    border: '1px solid',
    borderColor: theme.palette.divider,
    bgcolor: theme.palette.background.paper
  };
  useEffect(() => {
    if (updateError) {
      handleError(updateError);
      updateReset();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [handleError, updateError]);
  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6} lg={7}>
            <MainCard>
              <List component="ul">
                {features.map((feature) => {
                  const matchingFeature = currentPlan?.planFeatureValues.find(
                    (featureValue) => featureValue.featureValue?.feature?.id === feature.id
                  );

                  return (
                    <ListItem key={feature.id} divider>
                      <ListItemText primary={feature.name} />
                      <ListItemIcon>
                        {matchingFeature?.featureValue?.value === 'true' ? (
                          <TickSquare size="16" color={theme.palette.success.main} />
                        ) : matchingFeature?.featureValue?.value === 'false' ? (
                          <ShieldCross size="16" color={theme.palette.error.main} />
                        ) : (
                          <Typography variant="body1">{matchingFeature?.featureValue?.value || 'N/A'}</Typography>
                        )}
                      </ListItemIcon>
                    </ListItem>
                  );
                })}
              </List>
            </MainCard>
          </Grid>

          <Grid item xs={12} md={6} lg={5}>
            <MainCard>
              <RadioGroup value={selectedPlan} onChange={(e) => handlePlanChange(e.target.value)}>
                <Stack spacing={2}>
                  {plans.map((plan) => {
                    const subscribedPlan = subscriptions?.find((subscription) => subscription.planId === plan.id);

                    return (
                      <Box key={plan.id} sx={selectedPlan === plan.name ? priceSelectedPlan : priceUnselectedPlan}>
                        <FormControlLabel
                          value={plan.name}
                          control={<Radio />}
                          label={
                            <Stack spacing={0.5} direction="row" alignItems="center" justifyContent="space-between" sx={{ width: '100%' }}>
                              <Stack spacing={0}>
                                <Stack spacing={1} direction="row">
                                  <Typography variant="h5">{plan.name}</Typography>
                                  {subscribedPlan ? (
                                    <Chip
                                      label={subscribedPlan.status}
                                      size="small"
                                      color={subscribedPlan.status === 'ACTIVE' ? 'success' : 'error'}
                                    />
                                  ) : (
                                    ''
                                  )}
                                </Stack>
                              </Stack>
                              <Stack spacing={0.5} alignItems="flex-end">
                                {plan.planPricings.map((pricing) => (
                                  <Typography key={pricing.id} variant="body2">
                                    {currencySymbols[plan.currency] ?? plan.currency} {pricing.price} for {pricing.minSalesThreshold}–
                                    {pricing.maxSalesThreshold} sales
                                  </Typography>
                                ))}
                              </Stack>
                            </Stack>
                          }
                          sx={{
                            width: '100%',
                            alignItems: 'flex-start',
                            '& .MuiSvgIcon-root': { fontSize: 32 },
                            '& .MuiFormControlLabel-label': { width: '100%' },
                            '& .MuiRadio-root': { p: 0, pl: 1, pr: 1, pt: 0.5 }
                          }}
                        />
                      </Box>
                    );
                  })}
                </Stack>
              </RadioGroup>
            </MainCard>
          </Grid>
        </Grid>

        <Grid item xs={12}>
          <Stack direction="row" justifyContent="flex-end" alignItems="center" spacing={2}>
            <Button
              type="submit"
              variant="contained"
              onClick={handleSubscription}
              disabled={subscriptions?.some((subscription) => subscription.planId === currentPlan?.id && subscription.status === 'ACTIVE')}
            >
              continue
            </Button>
          </Stack>
        </Grid>
      </Grid>
    </Grid>
  );
}
