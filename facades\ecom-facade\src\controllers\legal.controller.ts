import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  patch,
  put,
  del,
  requestBody,
  response,
  getModelSchemaRef,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {PermissionKeys} from '@local/core';
import {
  CONTENT_TYPE,
  ModifiedRestService,
  restService,
  STATUS_CODE,
} from '@sourceloop/core';
import {Legal} from '../models/ecom-service/legal.model';

const basePath = '/legals';

export class LegalController {
  constructor(
    @restService(Legal)
    public legalProxy: ModifiedRestService<Legal>,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateLegal]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Legal model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Legal)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Legal, {
            title: 'NewLegal',
            exclude: ['id'],
          }),
        },
      },
    })
    legal: Omit<Legal, 'id'>,
  ): Promise<Legal> {
    return this.legalProxy.create(legal);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewLegal]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'Legal model count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(@param.where(Legal) where?: Where<Legal>): Promise<Count> {
    return this.legalProxy.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewLegal]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Legal model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Legal, {includeRelations: true}),
        },
      },
    },
  })
  async find(@param.filter(Legal) filter?: Filter<Legal>): Promise<Legal[]> {
    return this.legalProxy.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewLegal]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'Legal model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Legal, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Legal, {exclude: 'where'})
    filter?: FilterExcludingWhere<Legal>,
  ): Promise<Legal> {
    return this.legalProxy.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateLegal]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Legal PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Legal, {partial: true}),
        },
      },
    })
    legal: Partial<Legal>,
  ): Promise<void> {
    await this.legalProxy.updateById(id, legal);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateLegal]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Legal PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Legal),
        },
      },
    })
    legal: Legal,
  ): Promise<void> {
    await this.legalProxy.replaceById(id, legal);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteLegal]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Legal DELETE success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.legalProxy.deleteById(id);
  }
}
