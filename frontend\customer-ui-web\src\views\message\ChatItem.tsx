'use client';

import {Avatar, Box, Stack, Typography} from '@mui/material';
import {ChatDto} from 'types/chat';
interface ChatItemProps {
  chat: ChatDto;
  isSelected: boolean;
  onClick: () => void;
}

export default function ChatItem({chat, isSelected, onClick}: ChatItemProps) {
  const sellerUser = chat?.userData?.userTenant?.user;

  const fullName =
    `${sellerUser?.firstName ?? ''} ${sellerUser?.lastName ?? ''}`.trim();
  const sellerName = fullName || `Seller (${chat?.sellerId})`;

  const displayName =
    sellerName.length > 16 ? `${sellerName.slice(0, 13)}...` : sellerName;

  const seller = chat?.userData;

  const initials =
    `${seller?.userTenant?.user?.firstName?.[0] || ''}${seller?.userTenant?.user?.lastName?.[0] || ''}`.toUpperCase();

  return (
    <Box
      onClick={onClick}
      sx={{
        p: 1.5,
        mb: 1,
        borderRadius: 2,
        cursor: 'pointer',
        bgcolor: isSelected ? 'primary.lighter' : 'grey.100',
        '&:hover': {bgcolor: 'primary.light'},
      }}
    >
      <Stack direction="row" spacing={1} alignItems="center">
        <Avatar
          alt={sellerName}
          src={seller?.presignedPhotoUrl || undefined}
          sx={{width: 40, height: 40}}
        >
          {!seller?.presignedPhotoUrl && initials}
        </Avatar>

        <Box>
          <Typography variant="subtitle1" noWrap>
            {displayName}
          </Typography>
        </Box>
      </Stack>
    </Box>
  );
}
