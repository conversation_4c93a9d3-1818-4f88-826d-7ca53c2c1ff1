import {DialogTitle} from '@mui/material';
import {Box} from '@mui/material';
import {DialogActions} from '@mui/material';
import {Dialog} from '@mui/material';
import {DialogContent} from '@mui/material';
import {Typography, Button, Divider} from '@mui/material';
import {Stack} from '@mui/system';
import MainCard from 'components/MainCard';
import {Add, Edit} from 'iconsax-react';
import {useRouter} from 'next/navigation';
import {useState} from 'react';
import {AddressDto} from 'types/address';
interface CartAddressCardProps {
  addresses: AddressDto[];
  selectedShippingAddress: AddressDto | null;
  selectedBillingAddress: AddressDto | null;
  onSelectAddress: (address: AddressDto) => void;
  onSelectBillingAddress: (address: AddressDto) => void;
}

const CartAddressCard: React.FC<CartAddressCardProps> = ({
  addresses,
  selectedShippingAddress,
  onSelectAddress,
  selectedBillingAddress,
  onSelectBillingAddress,
}) => {
  const router = useRouter();
  const [openShippingAddressModal, setOpenShippingAddressModal] =
    useState(false);
  const [openBillingAddressModal, setOpenBillingAddressModal] = useState(false);

  const handleAddShippingAddress = () => {
    router.push('/address');
  };

  return (
    <MainCard sx={{p: 3, borderRadius: 2, boxShadow: 1, mb: 3}}>
      <Stack spacing={3}>
        <Stack spacing={2}>
          <Typography variant="h6" fontWeight="bold">
            Shipping address
          </Typography>
          {selectedShippingAddress ? (
            <Stack
              direction="row"
              spacing={2}
              alignItems="center"
              sx={{
                p: 2,
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 2,
              }}
            >
              <Stack flexGrow={1}>
                <Typography fontWeight="bold">
                  {selectedShippingAddress.addressType}
                </Typography>
                <Typography>
                  {selectedShippingAddress.addressLine1},{' '}
                  {selectedShippingAddress.addressLine2},{' '}
                  {selectedShippingAddress.locality}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {selectedShippingAddress.city},{' '}
                  {selectedShippingAddress.state} -{' '}
                  {selectedShippingAddress.zipCode},{' '}
                  {selectedShippingAddress.country}
                </Typography>
              </Stack>
              <Button
                color="secondary"
                startIcon={<Edit />}
                size="small"
                onClick={() => setOpenShippingAddressModal(true)}
              >
                Change
              </Button>
            </Stack>
          ) : (
            <Typography variant="body2" color="text.secondary">
              No address found. Please add one.
            </Typography>
          )}

          <Button
            fullWidth
            color="secondary"
            variant="outlined"
            startIcon={<Add />}
            sx={{alignSelf: 'flex-start'}}
            onClick={handleAddShippingAddress}
          >
            Add shipping address
          </Button>

          <Typography variant="body2" color="text.secondary">
            Estimated delivery: 25 May - 08 Jun
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Dispatches from Mumbai
          </Typography>
        </Stack>

        <Divider />
        <Dialog
          open={openShippingAddressModal}
          onClose={() => setOpenShippingAddressModal(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Select Shipping Address</DialogTitle>
          <DialogContent dividers>
            <Stack spacing={2}>
              {addresses.map(address => (
                <Box
                  key={address.id}
                  onClick={() => {
                    onSelectAddress(address);
                    setOpenShippingAddressModal(false);
                  }}
                  sx={{
                    p: 2,
                    border: '1px solid',
                    borderColor:
                      selectedShippingAddress?.id === address.id
                        ? 'primary.main'
                        : 'divider',
                    borderRadius: 2,
                    cursor: 'pointer',
                    bgcolor:
                      selectedShippingAddress?.id === address.id
                        ? '#E3F2FD'
                        : 'white',
                  }}
                >
                  <Typography fontWeight="bold">
                    {address.addressType}
                  </Typography>
                  <Typography>
                    {address.addressLine1}, {address.addressLine2},{' '}
                    {address.locality}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {address.city}, {address.state} - {address.zipCode},{' '}
                    {address.country}
                  </Typography>
                </Box>
              ))}
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setOpenShippingAddressModal(false)}
              color="inherit"
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog
          open={openBillingAddressModal}
          onClose={() => setOpenBillingAddressModal(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Select Shipping Address</DialogTitle>
          <DialogContent dividers>
            <Stack spacing={2}>
              {addresses.map(address => (
                <Box
                  key={address.id}
                  onClick={() => {
                    onSelectBillingAddress(address);
                    setOpenBillingAddressModal(false);
                  }}
                  sx={{
                    p: 2,
                    border: '1px solid',
                    borderColor:
                      selectedBillingAddress?.id === address.id
                        ? 'primary.main'
                        : 'divider',
                    borderRadius: 2,
                    cursor: 'pointer',
                    bgcolor:
                      selectedBillingAddress?.id === address.id
                        ? '#E3F2FD'
                        : 'white',
                  }}
                >
                  <Typography fontWeight="bold">
                    {address.addressType}
                  </Typography>
                  <Typography>
                    {address.addressLine1}, {address.addressLine2},{' '}
                    {address.locality}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {address.city}, {address.state} - {address.zipCode},{' '}
                    {address.country}
                  </Typography>
                </Box>
              ))}
            </Stack>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setOpenBillingAddressModal(false)}
              color="inherit"
            >
              Close
            </Button>
          </DialogActions>
        </Dialog>

        {/* Billing Address Section (if needed, make this dynamic too) */}
        <Stack spacing={2}>
          <Typography variant="h6" fontWeight="bold">
            Billing address
          </Typography>
          {selectedBillingAddress ? (
            <Stack
              direction="row"
              spacing={2}
              alignItems="center"
              sx={{
                p: 2,
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: 2,
              }}
            >
              <Stack flexGrow={1}>
                <Typography fontWeight="bold">
                  {selectedBillingAddress.addressType}
                </Typography>
                <Typography>
                  {selectedBillingAddress.addressLine1},{' '}
                  {selectedBillingAddress.addressLine2},{' '}
                  {selectedBillingAddress.locality}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {selectedBillingAddress.city}, {selectedBillingAddress.state}{' '}
                  - {selectedBillingAddress.zipCode},{' '}
                  {selectedBillingAddress.country}
                </Typography>
              </Stack>
              <Button
                color="secondary"
                startIcon={<Edit />}
                size="small"
                onClick={() => setOpenBillingAddressModal(true)}
              >
                Change
              </Button>
            </Stack>
          ) : (
            <Typography variant="body2" color="text.secondary">
              No address found. Please add one.
            </Typography>
          )}

          <Button
            fullWidth
            color="secondary"
            variant="outlined"
            startIcon={<Add />}
            sx={{alignSelf: 'flex-start'}}
            onClick={handleAddShippingAddress}
          >
            Add billing address
          </Button>
        </Stack>
      </Stack>
    </MainCard>
  );
};

export default CartAddressCard;
