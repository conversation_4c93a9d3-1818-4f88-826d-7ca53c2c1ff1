/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */
import 'react-native-reanimated';
import React from 'react';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {PaperProvider} from 'react-native-paper';
import {theme} from './theme/theme';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {Provider} from 'react-redux';
import AppNavigations from './navigations';
import {NavigationContainer} from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import {toastConfig} from './utils/AlertConfig';
import {store} from './redux/store';
import {PaymentProvider} from './providers/PaymentProvider';

function App(): React.JSX.Element {
  return (
    <>
      <Provider store={store}>
        <PaymentProvider>
          <GestureHandlerRootView>
            <SafeAreaProvider>
              <PaperProvider theme={theme}>
                <NavigationContainer>
                  <AppNavigations />
                </NavigationContainer>
                <Toast config={toastConfig} position="top" />
              </PaperProvider>
            </SafeAreaProvider>
          </GestureHandlerRootView>
        </PaymentProvider>
      </Provider>
    </>
  );
}

export default App;
