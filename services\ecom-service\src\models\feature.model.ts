import {model, property, hasMany} from '@loopback/repository';
import {UserModifiableEntity} from '@sourceloop/core';
import {FeatureValue} from './feature-value.model';

@model({settings: {strict: false}, name: 'features'})
export class Feature extends UserModifiableEntity {
  @property({
    type: 'string',
    id: true,
    generated: true,
  })
  id?: string;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
  })
  key?: string;

  @property({
    type: 'string',
  })
  category?: string;

  @hasMany(() => FeatureValue, {keyTo: 'featureId'})
  featureValues: FeatureValue[];

  constructor(data?: Partial<Feature>) {
    super(data);
  }
}

export interface FeatureRelations {
  // describe navigational properties here
}

export type FeatureWithRelations = Feature & FeatureRelations;
