import React, { useRef, useState } from 'react';
import { Modal, Box, Button } from '@mui/material';
import ReactCrop, { Crop, PercentCrop, PixelCrop } from 'react-image-crop';
import { LoadingButton } from '@mui/lab';
import 'react-image-crop/dist/ReactCrop.css';

interface CropImageModalProps {
  open: boolean;
  onClose: () => void;
  onCropComplete: (croppedImage: File) => void;
  imageSrc: string;
  aspect?: number;
}

const CropImageModal: React.FC<CropImageModalProps> = ({ open, onClose, onCropComplete, imageSrc, aspect = 1 }) => {
  const [crop, setCrop] = useState<Crop>({
    unit: 'px',
    width: 100,
    height: 100,
    x: 0,
    y: 0
  });

  const [completedCrop, setCompletedCrop] = useState<PixelCrop | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);

  const handleCropChange = (newCrop: Crop) => {
    setCrop(newCrop);
  };

  const handleCropComplete = (pixelCrop: PixelCrop, percentageCrop: PercentCrop) => {
    setCompletedCrop(pixelCrop);
  };

  const handleSave = async () => {
    if (!completedCrop || !imageRef.current) return;

    setIsLoading(true);
    try {
      const croppedBlob = await getCroppedImg(imageRef.current, completedCrop);
      const file = new File([croppedBlob], 'cropped-image.jpg', { type: croppedBlob.type });
      onCropComplete(file); // ✅ Now passing a File instead of Blob
    } catch {
    } finally {
      setIsLoading(false);
      onClose();
    }
  };

  return (
    <Modal open={open} onClose={onClose}>
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '80%',
          height: '74%',
          maxWidth: 500,
          bgcolor: 'background.paper',
          boxShadow: 24,
          p: 3,
          outline: 'none',
          borderRadius: 1
        }}
      >
        <Box sx={{ position: 'relative', width: '100%', height: 400 }}>
          <ReactCrop crop={crop} onChange={handleCropChange} onComplete={handleCropComplete} aspect={1}>
            <img
              ref={imageRef}
              src={imageSrc}
              alt="Crop preview"
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain'
              }}
            />
          </ReactCrop>
        </Box>
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3, gap: 2 }}>
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
          <LoadingButton variant="contained" color="primary" onClick={handleSave} loading={isLoading} disabled={!completedCrop}>
            Save Crop
          </LoadingButton>
        </Box>
      </Box>
    </Modal>
  );
};

async function getCroppedImg(image: HTMLImageElement, crop: PixelCrop): Promise<Blob> {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) {
    throw new Error('Canvas context not available');
  }

  const scaleX = image.naturalWidth / image.width;
  const scaleY = image.naturalHeight / image.height;

  canvas.width = crop.width;
  canvas.height = crop.height;

  ctx.drawImage(image, crop.x * scaleX, crop.y * scaleY, crop.width * scaleX, crop.height * scaleY, 0, 0, crop.width, crop.height);

  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (!blob) {
          reject(new Error('Canvas is empty'));
          return;
        }
        resolve(blob);
      },
      'image/jpeg',
      0.9
    );
  });
}

export default CropImageModal;
