import {
  Count,
  CountSchema,
  Filter,
  FilterExcludingWhere,
  repository,
  Where,
} from '@loopback/repository';
import {
  post,
  param,
  get,
  patch,
  put,
  del,
  requestBody,
  response,
  getModelSchemaRef,
} from '@loopback/rest';
import {authenticate, STRATEGY} from 'loopback4-authentication';
import {authorize} from 'loopback4-authorization';
import {CONTENT_TYPE, STATUS_CODE} from '@sourceloop/core';
import {PermissionKeys} from '@local/core';

import {Support} from '../models';
import {SupportRepository} from '../repositories';

const basePath = '/support';

export class SupportController {
  constructor(
    @repository(SupportRepository)
    public supportRepository: SupportRepository,
  ) {}

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.CreateSupport]})
  @post(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Support model instance',
    content: {[CONTENT_TYPE.JSON]: {schema: getModelSchemaRef(Support)}},
  })
  async create(
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Support, {
            title: 'NewSupport',
            exclude: ['id', 'createdOn', 'modifiedOn', 'deleted'],
          }),
        },
      },
    })
    support: Omit<Support, 'id'>,
  ): Promise<Support> {
    return this.supportRepository.create(support);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSupport]})
  @get(`${basePath}/count`)
  @response(STATUS_CODE.OK, {
    description: 'Support count',
    content: {[CONTENT_TYPE.JSON]: {schema: CountSchema}},
  })
  async count(@param.where(Support) where?: Where<Support>): Promise<Count> {
    return this.supportRepository.count(where);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSupport]})
  @get(basePath)
  @response(STATUS_CODE.OK, {
    description: 'Array of Support model instances',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: {
          type: 'array',
          items: getModelSchemaRef(Support, {includeRelations: true}),
        },
      },
    },
  })
  async find(
    @param.filter(Support) filter?: Filter<Support>,
  ): Promise<Support[]> {
    return this.supportRepository.find(filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.ViewSupport]})
  @get(`${basePath}/{id}`)
  @response(STATUS_CODE.OK, {
    description: 'Support model instance',
    content: {
      [CONTENT_TYPE.JSON]: {
        schema: getModelSchemaRef(Support, {includeRelations: true}),
      },
    },
  })
  async findById(
    @param.path.string('id') id: string,
    @param.filter(Support, {exclude: 'where'})
    filter?: FilterExcludingWhere<Support>,
  ): Promise<Support> {
    return this.supportRepository.findById(id, filter);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSupport]})
  @patch(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Support PATCH success',
  })
  async updateById(
    @param.path.string('id') id: string,
    @requestBody({
      content: {
        [CONTENT_TYPE.JSON]: {
          schema: getModelSchemaRef(Support, {partial: true}),
        },
      },
    })
    support: Partial<Support>,
  ): Promise<void> {
    await this.supportRepository.updateById(id, support);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.UpdateSupport]})
  @put(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Support PUT success',
  })
  async replaceById(
    @param.path.string('id') id: string,
    @requestBody() support: Support,
  ): Promise<void> {
    await this.supportRepository.replaceById(id, support);
  }

  @authenticate(STRATEGY.BEARER)
  @authorize({permissions: [PermissionKeys.DeleteSupport]})
  @del(`${basePath}/{id}`)
  @response(STATUS_CODE.NO_CONTENT, {
    description: 'Support DELETE (soft delete) success',
  })
  async deleteById(@param.path.string('id') id: string): Promise<void> {
    await this.supportRepository.deleteById(id);
  }
}
