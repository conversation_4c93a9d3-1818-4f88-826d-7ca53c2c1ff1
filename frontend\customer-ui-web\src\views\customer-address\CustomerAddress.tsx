'use client';
import {Box, Grid} from '@mui/material';
import {Stack, Typography} from '@mui/material';

import {AddCircle, Edit2, Trash} from 'iconsax-react';
import {useState} from 'react';
import AddressForm from './AddressForm';

import {useGetUserQuery} from 'redux/auth/authApiSlice';
import {
  useCreateCustomerAddressMutation,
  useDeleteCustomerByIdMutation,
  useGetAddressesQuery,
  useUpdateCustomerByIdMutation,
} from 'redux/address/customerAddressApiSlice';
import {AddressDto} from 'types/address';
import {openSnackbar} from 'api/snackbar';
import {SnackbarProps} from 'types/snackbar';
import {Dialog} from '@mui/material';
import {DialogTitle} from '@mui/material';
import {DialogContent} from '@mui/material';
import {DialogContentText} from '@mui/material';
import {DialogActions} from '@mui/material';
import {Button} from '@mui/material';

export default function CustomerAddress() {
  const [formOpen, setFormOpen] = useState(false);
  const [editData, setEditData] = useState<AddressDto | null>(null);
  const [deleteTargetId, setDeleteTargetId] = useState<string | null>(null);
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(
    null,
  );

  const [createCustomer] = useCreateCustomerAddressMutation();
  const [updateCustomer] = useUpdateCustomerByIdMutation();

  const {data: user} = useGetUserQuery();
  const customerId = user?.profileId;
  const [deleteCustomer] = useDeleteCustomerByIdMutation();

  const {data: addresses = [], refetch: refetchAddresses} =
    useGetAddressesQuery(customerId ?? '', {
      skip: !customerId,
    });
  const handleAddClick = () => {
    setEditData(null);
    setFormOpen(true);
  };

  const handleEditClick = (address: AddressDto) => {
    setEditData(address);
    setFormOpen(true);
  };
  const handleDeleteClick = (id: string) => {
    setDeleteTargetId(id);
  };

  const handleDeleteConfirm = async () => {
    if (!deleteTargetId) return;
    try {
      await deleteCustomer(deleteTargetId).unwrap();
      openSnackbar({
        open: true,
        message: `Address has been deleted.`,
        variant: 'alert',
        alert: {color: 'success'},
      } as SnackbarProps);
      await refetchAddresses();
    } catch (error) {
      console.error('Delete failed', error);
    } finally {
      setDeleteTargetId(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteTargetId(null);
  };
  const handleCancel = () => {
    setFormOpen(false);
    setEditData(null);
  };
  const handleSave = async (formData: AddressDto) => {
    const payload: AddressDto = {
      ...formData,
      customerId,
    };

    if (editData?.id) {
      // Edit case
      await updateCustomer({id: editData.id, data: payload}).unwrap();

      openSnackbar({
        open: true,
        message: `Address has been successfully updated.`,
        variant: 'alert',
        alert: {color: 'success'},
      } as SnackbarProps);
    } else {
      // Create case
      await createCustomer(payload).unwrap();

      openSnackbar({
        open: true,
        message: `Address has been successfully created.`,
        variant: 'alert',
        alert: {color: 'success'},
      } as SnackbarProps);
    }

    setFormOpen(false);
    setEditData(null);
    await refetchAddresses();
  };

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Stack spacing={1}>
          <Typography variant="h5" sx={{textAlign: 'left', mb: 1}}>
            Manage Addresses
          </Typography>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: 'white',
              borderRadius: '8px',
              border: '1px solid #C8D3D5',
              p: 2,
              minHeight: '50px',
              width: '100%',
              gap: 1,
              cursor: 'pointer',
            }}
            onClick={handleAddClick}
          >
            <AddCircle size={20} />

            <Typography
              variant="body1"
              sx={{
                textAlign: 'center',
                color: '#888',
              }}
            >
              Add address
            </Typography>
          </Box>
        </Stack>
      </Grid>
      <Grid item xs={12}>
        <Stack spacing={1}>
          <Typography variant="h5" sx={{textAlign: 'left', mb: 1}}>
            Manage Addresses
          </Typography>
          {addresses.map((address, index) => (
            <Box
              key={address.id ?? index}
              onClick={() => setSelectedAddressId(address.id ?? '')}
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                bgcolor: selectedAddressId === address.id ? '#E3F2FD' : 'white',
                borderRadius: '8px',
                border: `1px solid ${
                  selectedAddressId === address.id ? '#1976d2' : '#C8D3D5'
                }`,
                p: 2,
                minHeight: '70px',
                width: '100%',
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  bgcolor: '#C8D3D5',
                  borderRadius: '20px',
                  px: 2,
                  py: 0.5,
                  minWidth: '50px',
                  textAlign: 'center',
                  whiteSpace: 'nowrap',
                }}
              >
                {address.addressType}
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  flex: 1,
                  textAlign: 'center',
                }}
              >
                {`${address.addressLine1}, ${address.addressLine2 ? address.addressLine2 + ', ' : ''}${address.locality}, ${address.city}, ${address.state}, ${address.zipCode}, ${address.country}`}
              </Typography>
              <Box sx={{display: 'flex', alignItems: 'center', gap: 2}}>
                <Box
                  onClick={() => handleEditClick(address)}
                  sx={{cursor: 'pointer'}}
                >
                  <Edit2 size={20} />
                </Box>
                <Box
                  onClick={() => handleDeleteClick(address.id!)}
                  sx={{cursor: 'pointer'}}
                >
                  <Trash size={20} />
                </Box>
              </Box>
            </Box>
          ))}
        </Stack>
      </Grid>
      {formOpen && (
        <Grid item xs={12}>
          <AddressForm
            initialData={editData ?? undefined}
            onCancel={handleCancel}
            onSave={handleSave}
          />
        </Grid>
      )}
      <Dialog
        open={!!deleteTargetId}
        onClose={handleDeleteCancel}
        aria-labelledby="confirm-delete-title"
        aria-describedby="confirm-delete-description"
      >
        <DialogTitle id="confirm-delete-title">{'Confirm Delete'}</DialogTitle>
        <DialogContent>
          <DialogContentText id="confirm-delete-description">
            Are you sure you want to delete this address?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="inherit">
            Cancel
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" autoFocus>
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Grid>
  );
}
