import * as Yup from 'yup';
export const loginValidationSchema = Yup.object().shape({
  username: Yup.string().required('Email is required').email('Invalid email'),
  password: Yup.string().required('Password is required'),
});
export const forgotPasswordSchema = Yup.object().shape({
  email: Yup.string().email('Invalid email').required('Email is required'),
});
export const ChangePasswordSchema = Yup.object()
  .shape({
    currentPassword: Yup.string().required('Current Password is required'),
    newPassword: Yup.string()
      .required('New Password is required')
      .matches(
        /^.*(?=.{8,})((?=.*[!@#$%^&*()\-_=+{};:,<.>]){1})(?=.*\d)((?=.*[a-z]){1})((?=.*[A-Z]){1}).*$/,
        'Password must contain at least 8 characters, one uppercase, one number and one special character',
      )
      .test(
        'not-same-as-old',
        'New Password must be different from Current Password',
        function (value) {
          const {currentPassword} = this.parent;
          return value !== currentPassword;
        },
      ),
    confirmPassword: Yup.string()
      .required('Confirm Password is required')
      .oneOf([Yup.ref('newPassword')], "Passwords don't match."),
  })
  .test(
    'all-passwords-not-same',
    'Current, New, and Confirm Password must not all be the same',
    function (values) {
      if (!values) return true;
      const {currentPassword, newPassword, confirmPassword} = values;
      return !(
        currentPassword &&
        newPassword &&
        confirmPassword &&
        currentPassword === newPassword &&
        newPassword === confirmPassword
      );
    },
  );
