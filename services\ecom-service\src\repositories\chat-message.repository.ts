import {Getter, inject} from '@loopback/core';
import {BelongsToAccessor, repository} from '@loopback/repository';
import {SequelizeDataSource} from '@loopback/sequelize';
import {AuthenticationBindings} from 'loopback4-authentication';
import {IAuthUserWithPermissions} from 'loopback4-authorization';
import {SequelizeUserModifyCrudRepositoryCore} from '@local/core';
import {ChatMessage, ChatMessageRelations, Chat} from '../models';
import {ChatRepository} from './chat.repository';

export class ChatMessageRepository extends SequelizeUserModifyCrudRepositoryCore<
  ChatMessage,
  typeof ChatMessage.prototype.id,
  ChatMessageRelations
> {
  public readonly chat: BelongsToAccessor<
    Chat,
    typeof ChatMessage.prototype.id
  >;

  constructor(
    @inject('datasources.pg') dataSource: SequelizeDataSource,
    @inject.getter(AuthenticationBindings.CURRENT_USER)
    public readonly getCurrentUser: Getter<IAuthUserWithPermissions>,
    @repository.getter('ChatRepository')
    protected chatRepositoryGetter: Getter<ChatRepository>,
  ) {
    super(ChatMessage, dataSource, getCurrentUser);
    this.chat = this.createBelongsToAccessorFor('chat', chatRepositoryGetter);
    this.registerInclusionResolver('chat', this.chat.inclusionResolver);
  }
}
