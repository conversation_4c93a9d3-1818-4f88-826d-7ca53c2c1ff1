'use client';

import React, { useEffect } from 'react';
import { Card, CardContent, Typography, Box, Grid, Divider, Chip } from '@mui/material';
import Loader from 'components/Loader';
import { useGetDiscountByIdQuery } from 'redux/app/discount/discountApiSlice';

function DiscountView({ discountId }: { discountId: string }) {
  const { data, isLoading, error, refetch } = useGetDiscountByIdQuery({
    id: discountId,
    filter: { include: [{ relation: 'discountConditions' }] }
  });

  useEffect(() => {
    refetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isLoading) return <Loader />;
  if (error || !data) return <Typography color="error">Error fetching Discount</Typography>;

  return (
    <Card sx={{ p: 2, boxShadow: 3, mt: 1 }}>
      <CardContent>
        <Typography variant="h4" gutterBottom>
          Discount Details
        </Typography>
        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={2}>
          {/* Left Column */}
          <Grid item xs={12} md={6}>
            <Typography display="flex" flexDirection="column" sx={{ mt: 2 }}>
              <strong>Name:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1
                }}
              >
                {data.name}
              </Box>
            </Typography>

            <Typography display="flex" flexDirection="column" sx={{ mt: 2 }}>
              <strong>Description:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1
                }}
              >
                {data.description || 'N/A'}
              </Box>
            </Typography>

            <Typography display="flex" flexDirection="column" sx={{ mt: 2 }}>
              <strong>Status:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1
                }}
              >
                <Chip label={data.isActive ? 'Active' : 'Inactive'} color={data.isActive ? 'success' : 'error'} size="small" />
              </Box>
            </Typography>

            <Typography display="flex" flexDirection="column" sx={{ mt: 2 }}>
              <strong>Valid From:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1
                }}
              >
                {new Date(data.startDate ?? '').toLocaleString()}
              </Box>
            </Typography>
          </Grid>

          {/* Right Column */}
          <Grid item xs={12} md={6}>
            <Typography display="flex" flexDirection="column" sx={{ mt: 2 }}>
              <strong>Usage Limit Per User:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1
                }}
              >
                {data.usageLimitPerUser || 'Unlimited'}
              </Box>
            </Typography>

            <Typography display="flex" flexDirection="column" sx={{ mt: 2 }}>
              <strong>Combinable:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1
                }}
              >
                <Chip label={data.combinable ? 'Yes' : 'No'} color={data.combinable ? 'success' : 'default'} size="small" />
              </Box>
            </Typography>

            <Typography display="flex" flexDirection="column" sx={{ mt: 2 }}>
              <strong>Created On:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1
                }}
              >
                {data.createdOn ? new Date(data.createdOn).toLocaleString() : 'N/A'}
              </Box>
            </Typography>

            <Typography display="flex" flexDirection="column" sx={{ mt: 2 }}>
              <strong>Valid Till:</strong>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 1,
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block',
                  mt: 1
                }}
              >
                {new Date(data.endDate ?? '').toLocaleString()}
              </Box>
            </Typography>
          </Grid>
        </Grid>

        {/* Discount Conditions */}
        {data.discountConditions && data.discountConditions.length > 0 && (
          <>
            <Typography variant="h5" sx={{ mt: 4, mb: 2 }}>
              Discount Conditions
            </Typography>

            <Grid container spacing={2}>
              {data.discountConditions.map((condition, index) => (
                <Grid item xs={12} md={6} key={index}>
                  <Card variant="outlined" sx={{ p: 2, borderRadius: 2 }}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="subtitle1" fontWeight="bold">
                        {condition.conditionType === 'BASIC' && 'Basic Discount'}
                        {condition.conditionType === 'ADDITIONAL' && 'Additional Discount'}
                        {condition.conditionType === 'APP_ONLY' && 'App Exclusive Discount'}
                      </Typography>
                      {condition.isAppOnly && <Chip label="App Only" color="info" size="small" />}
                    </Box>

                    <Grid container spacing={1} sx={{ mt: 1 }}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          Min Cart Value:
                        </Typography>
                        <Typography variant="body1">₹{condition.thresholdAmount}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">
                          Discount:
                        </Typography>
                        <Typography variant="body1">
                          {condition.discountType === 'FLAT' ? '₹' : ''}
                          {condition.discountValue}
                          {condition.discountType === 'PERCENT' ? '%' : ''}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </>
        )}
      </CardContent>
    </Card>
  );
}

export default DiscountView;
